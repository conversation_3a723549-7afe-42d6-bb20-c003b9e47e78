{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/hooks/useBookmarks.ts"], "sourcesContent": ["import { useBookmarkContext } from '@/contexts/BookmarkContext';\n\nexport const useBookmarks = () => {\n  return useBookmarkContext();\n};\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,eAAe;IAC1B,OAAO,CAAA,GAAA,mIAAA,CAAA,qBAAkB,AAAD;AAC1B", "debugId": null}}, {"offset": {"line": 17, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface CardProps {\n  children: React.ReactNode;\n  className?: string;\n  padding?: 'none' | 'sm' | 'md' | 'lg';\n  hover?: boolean;\n}\n\nconst Card: React.FC<CardProps> = ({\n  children,\n  className,\n  padding = 'md',\n  hover = false,\n  ...props\n}) => {\n  const baseClasses = 'glass backdrop-blur-xl bg-white/80 dark:bg-slate-900/80 border border-white/20 dark:border-slate-700/50 rounded-2xl shadow-xl';\n\n  const paddingClasses = {\n    none: '',\n    sm: 'p-4',\n    md: 'p-6',\n    lg: 'p-8'\n  };\n\n  const hoverClasses = hover ? 'hover:shadow-2xl hover:transform hover:scale-105 transition-all duration-300' : '';\n\n  return (\n    <div\n      className={cn(\n        baseClasses,\n        paddingClasses[padding],\n        hoverClasses,\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n};\n\nexport default Card;\n"], "names": [], "mappings": ";;;;AACA;;;AASA,MAAM,OAA4B,CAAC,EACjC,QAAQ,EACR,SAAS,EACT,UAAU,IAAI,EACd,QAAQ,KAAK,EACb,GAAG,OACJ;IACC,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,MAAM;QACN,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,eAAe,QAAQ,iFAAiF;IAE9G,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,cAAc,CAAC,QAAQ,EACvB,cACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/components/ui/Badge.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\nimport { BadgeProps } from '@/types';\n\nconst Badge: React.FC<BadgeProps> = ({\n  children,\n  variant,\n  size = 'md',\n  ...props\n}) => {\n  const baseClasses = 'inline-flex items-center font-medium rounded-full';\n  \n  const variants = {\n    primary: 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg',\n    secondary: 'bg-gradient-to-r from-slate-100 to-slate-200 text-slate-800 dark:from-slate-700 dark:to-slate-600 dark:text-slate-200 shadow-md',\n    success: 'bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-lg',\n    warning: 'bg-gradient-to-r from-yellow-500 to-orange-600 text-white shadow-lg',\n    danger: 'bg-gradient-to-r from-red-500 to-pink-600 text-white shadow-lg'\n  };\n\n  const sizes = {\n    sm: 'px-2 py-0.5 text-xs',\n    md: 'px-2.5 py-1 text-sm',\n    lg: 'px-3 py-1.5 text-base'\n  };\n\n  return (\n    <span\n      className={cn(\n        baseClasses,\n        variants[variant],\n        sizes[size]\n      )}\n      {...props}\n    >\n      {children}\n    </span>\n  );\n};\n\nexport default Badge;\n"], "names": [], "mappings": ";;;;AACA;;;AAGA,MAAM,QAA8B,CAAC,EACnC,QAAQ,EACR,OAAO,EACP,OAAO,IAAI,EACX,GAAG,OACJ;IACC,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,SAAS;QACT,QAAQ;IACV;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK;QAEZ,GAAG,KAAK;kBAER;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\nimport { ButtonProps } from '@/types';\n\nconst Button: React.FC<ButtonProps> = ({\n  children,\n  variant = 'primary',\n  size = 'md',\n  loading = false,\n  disabled = false,\n  onClick,\n  type = 'button',\n  className,\n  ...props\n}) => {\n  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none';\n  \n  const variants = {\n    primary: 'bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700 focus:ring-blue-500 shadow-lg hover:shadow-xl transform hover:scale-105',\n    secondary: 'bg-gradient-to-r from-slate-100 to-slate-200 text-slate-900 hover:from-slate-200 hover:to-slate-300 focus:ring-slate-500 dark:from-slate-800 dark:to-slate-700 dark:text-slate-100 dark:hover:from-slate-700 dark:hover:to-slate-600 shadow-md hover:shadow-lg',\n    outline: 'border border-slate-300 text-slate-700 hover:bg-white/50 focus:ring-slate-500 dark:border-slate-600 dark:text-slate-300 dark:hover:bg-slate-800/50 backdrop-blur-sm',\n    ghost: 'text-slate-700 hover:bg-white/50 focus:ring-slate-500 dark:text-slate-300 dark:hover:bg-slate-800/50 backdrop-blur-sm'\n  };\n\n  const sizes = {\n    sm: 'px-3 py-1.5 text-sm',\n    md: 'px-4 py-2 text-sm',\n    lg: 'px-6 py-3 text-base'\n  };\n\n  return (\n    <button\n      type={type}\n      onClick={onClick}\n      disabled={disabled || loading}\n      className={cn(\n        baseClasses,\n        variants[variant],\n        sizes[size],\n        className\n      )}\n      {...props}\n    >\n      {loading && (\n        <svg\n          className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n          fill=\"none\"\n          viewBox=\"0 0 24 24\"\n        >\n          <circle\n            className=\"opacity-25\"\n            cx=\"12\"\n            cy=\"12\"\n            r=\"10\"\n            stroke=\"currentColor\"\n            strokeWidth=\"4\"\n          />\n          <path\n            className=\"opacity-75\"\n            fill=\"currentColor\"\n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n          />\n        </svg>\n      )}\n      {children}\n    </button>\n  );\n};\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AACA;;;AAGA,MAAM,SAAgC,CAAC,EACrC,QAAQ,EACR,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,OAAO,EACP,OAAO,QAAQ,EACf,SAAS,EACT,GAAG,OACJ;IACC,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,MAAM;QACN,SAAS;QACT,UAAU,YAAY;QACtB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAED,GAAG,KAAK;;YAER,yBACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 157, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/components/ui/StarRating.tsx"], "sourcesContent": ["import React from 'react';\nimport { StarIcon } from '@heroicons/react/24/solid';\nimport { StarIcon as StarOutlineIcon } from '@heroicons/react/24/outline';\nimport { cn } from '@/lib/utils';\n\ninterface StarRatingProps {\n  rating: number;\n  maxRating?: number;\n  size?: 'sm' | 'md' | 'lg';\n  showValue?: boolean;\n  interactive?: boolean;\n  onRatingChange?: (rating: number) => void;\n  className?: string;\n}\n\nconst StarRating: React.FC<StarRatingProps> = ({\n  rating,\n  maxRating = 5,\n  size = 'md',\n  showValue = false,\n  interactive = false,\n  onRatingChange,\n  className\n}) => {\n  const sizeClasses = {\n    sm: 'h-4 w-4',\n    md: 'h-5 w-5',\n    lg: 'h-6 w-6'\n  };\n\n  const handleStarClick = (starRating: number) => {\n    if (interactive && onRatingChange) {\n      onRatingChange(starRating);\n    }\n  };\n\n  return (\n    <div className={cn('flex items-center gap-1', className)}>\n      <div className=\"flex\">\n        {Array.from({ length: maxRating }, (_, index) => {\n          const starRating = index + 1;\n          const isFilled = starRating <= rating;\n          \n          return (\n            <button\n              key={index}\n              type=\"button\"\n              onClick={() => handleStarClick(starRating)}\n              disabled={!interactive}\n              className={cn(\n                'transition-colors',\n                interactive ? 'hover:scale-110 cursor-pointer' : 'cursor-default',\n                isFilled ? 'text-yellow-400' : 'text-secondary-300 dark:text-secondary-600'\n              )}\n            >\n              {isFilled ? (\n                <StarIcon className={sizeClasses[size]} />\n              ) : (\n                <StarOutlineIcon className={sizeClasses[size]} />\n              )}\n            </button>\n          );\n        })}\n      </div>\n      \n      {showValue && (\n        <span className=\"ml-2 text-sm text-secondary-600 dark:text-secondary-400\">\n          {rating.toFixed(1)}/{maxRating}\n        </span>\n      )}\n    </div>\n  );\n};\n\nexport default StarRating;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAYA,MAAM,aAAwC,CAAC,EAC7C,MAAM,EACN,YAAY,CAAC,EACb,OAAO,IAAI,EACX,YAAY,KAAK,EACjB,cAAc,KAAK,EACnB,cAAc,EACd,SAAS,EACV;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,eAAe,gBAAgB;YACjC,eAAe;QACjB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;;0BAC5C,8OAAC;gBAAI,WAAU;0BACZ,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAU,GAAG,CAAC,GAAG;oBACrC,MAAM,aAAa,QAAQ;oBAC3B,MAAM,WAAW,cAAc;oBAE/B,qBACE,8OAAC;wBAEC,MAAK;wBACL,SAAS,IAAM,gBAAgB;wBAC/B,UAAU,CAAC;wBACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qBACA,cAAc,mCAAmC,kBACjD,WAAW,oBAAoB;kCAGhC,yBACC,8OAAC,6MAAA,CAAA,WAAQ;4BAAC,WAAW,WAAW,CAAC,KAAK;;;;;qFAEtC,8OAAC,+MAAA,CAAA,WAAe;4BAAC,WAAW,WAAW,CAAC,KAAK;;;;;;uBAb1C;;;;;gBAiBX;;;;;;YAGD,2BACC,8OAAC;gBAAK,WAAU;;oBACb,OAAO,OAAO,CAAC;oBAAG;oBAAE;;;;;;;;;;;;;AAK/B;uCAEe", "debugId": null}}, {"offset": {"line": 242, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/components/ui/Modal.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { cn } from '@/lib/utils';\nimport { ModalProps } from '@/types';\nimport { XMarkIcon } from '@heroicons/react/24/outline';\n\nconst Modal: React.FC<ModalProps> = ({\n  isOpen,\n  onClose,\n  title,\n  children,\n  size = 'md'\n}) => {\n  useEffect(() => {\n    const handleEscape = (e: KeyboardEvent) => {\n      if (e.key === 'Escape') {\n        onClose();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'hidden';\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen, onClose]);\n\n  if (!isOpen) return null;\n\n  const sizeClasses = {\n    sm: 'max-w-md',\n    md: 'max-w-lg',\n    lg: 'max-w-2xl',\n    xl: 'max-w-4xl'\n  };\n\n  return (\n    <div className=\"fixed inset-0 z-50 overflow-y-auto\">\n      <div className=\"flex min-h-screen items-center justify-center p-4\">\n        {/* Backdrop */}\n        <div\n          className=\"fixed inset-0 bg-black bg-opacity-50 transition-opacity\"\n          onClick={onClose}\n        />\n        \n        {/* Modal */}\n        <div\n          className={cn(\n            'relative w-full bg-white dark:bg-secondary-800 rounded-lg shadow-xl transform transition-all',\n            sizeClasses[size]\n          )}\n        >\n          {/* Header */}\n          <div className=\"flex items-center justify-between p-6 border-b border-secondary-200 dark:border-secondary-700\">\n            <h3 className=\"text-lg font-semibold text-secondary-900 dark:text-secondary-100\">\n              {title}\n            </h3>\n            <button\n              onClick={onClose}\n              className=\"text-secondary-400 hover:text-secondary-600 dark:hover:text-secondary-300 transition-colors\"\n            >\n              <XMarkIcon className=\"h-6 w-6\" />\n            </button>\n          </div>\n          \n          {/* Content */}\n          <div className=\"p-6\">\n            {children}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Modal;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,QAA8B,CAAC,EACnC,MAAM,EACN,OAAO,EACP,KAAK,EACL,QAAQ,EACR,OAAO,IAAI,EACZ;IACC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,CAAC;YACpB,IAAI,EAAE,GAAG,KAAK,UAAU;gBACtB;YACF;QACF;QAEA,IAAI,QAAQ;YACV,SAAS,gBAAgB,CAAC,WAAW;YACrC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QAEA,OAAO;YACL,SAAS,mBAAmB,CAAC,WAAW;YACxC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG;QAAC;QAAQ;KAAQ;IAEpB,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBACC,WAAU;oBACV,SAAS;;;;;;8BAIX,8OAAC;oBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gGACA,WAAW,CAAC,KAAK;;sCAInB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX;;;;;;8CAEH,8OAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKzB,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb;uCAEe", "debugId": null}}, {"offset": {"line": 366, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/app/bookmarks/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Employee, User } from '@/types';\nimport { transformUserToEmployee } from '@/lib/utils';\nimport { useBookmarks } from '@/hooks/useBookmarks';\nimport UserCard from '@/components/UserCard';\nimport Card from '@/components/ui/Card';\nimport Button from '@/components/ui/Button';\nimport { BookmarkIcon } from '@heroicons/react/24/outline';\n\nexport default function BookmarksPage() {\n  const [allEmployees, setAllEmployees] = useState<Employee[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  \n  const { bookmarks, addBookmark, removeBookmark, isBookmarked } = useBookmarks();\n\n  useEffect(() => {\n    const fetchEmployees = async () => {\n      try {\n        setLoading(true);\n        const response = await fetch('https://dummyjson.com/users?limit=20');\n        \n        if (!response.ok) {\n          throw new Error('Failed to fetch employees');\n        }\n        \n        const data = await response.json();\n        const transformedEmployees = data.users.map((user: User) => \n          transformUserToEmployee(user)\n        );\n        \n        setAllEmployees(transformedEmployees);\n      } catch (err) {\n        setError(err instanceof Error ? err.message : 'An error occurred');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchEmployees();\n  }, []);\n\n  const bookmarkedEmployees = allEmployees.filter(employee => \n    bookmarks.includes(employee.id)\n  );\n\n  const handleBookmark = (id: number) => {\n    if (isBookmarked(id)) {\n      removeBookmark(id);\n    } else {\n      addBookmark(id);\n    }\n  };\n\n  const handlePromote = (id: number) => {\n    // In a real app, this would make an API call\n    console.log(`Promoting employee with ID: ${id}`);\n  };\n\n  const clearAllBookmarks = () => {\n    bookmarks.forEach(id => removeBookmark(id));\n  };\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-8 bg-secondary-200 dark:bg-secondary-700 rounded w-1/4 mb-6\"></div>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n            {Array.from({ length: 4 }).map((_, index) => (\n              <div key={index} className=\"h-64 bg-secondary-200 dark:bg-secondary-700 rounded-lg\"></div>\n            ))}\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <Card>\n        <div className=\"text-center py-12\">\n          <div className=\"text-red-500 text-lg font-medium mb-2\">Error Loading Bookmarks</div>\n          <p className=\"text-secondary-600 dark:text-secondary-400 mb-4\">{error}</p>\n          <button\n            onClick={() => window.location.reload()}\n            className=\"px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\"\n          >\n            Try Again\n          </button>\n        </div>\n      </Card>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-secondary-900 dark:text-white mb-2\">\n            Bookmarked Employees\n          </h1>\n          <p className=\"text-secondary-600 dark:text-secondary-400\">\n            Your saved employees for quick access\n          </p>\n        </div>\n        \n        {bookmarkedEmployees.length > 0 && (\n          <Button\n            variant=\"outline\"\n            onClick={clearAllBookmarks}\n            className=\"text-red-600 border-red-300 hover:bg-red-50 dark:text-red-400 dark:border-red-600 dark:hover:bg-red-900/20\"\n          >\n            Clear All\n          </Button>\n        )}\n      </div>\n\n      {/* Stats */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <Card>\n          <div className=\"flex items-center\">\n            <div className=\"p-3 rounded-full bg-primary-100 dark:bg-primary-900\">\n              <BookmarkIcon className=\"h-6 w-6 text-primary-600 dark:text-primary-400\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-secondary-600 dark:text-secondary-400\">\n                Total Bookmarks\n              </p>\n              <p className=\"text-2xl font-bold text-secondary-900 dark:text-white\">\n                {bookmarkedEmployees.length}\n              </p>\n            </div>\n          </div>\n        </Card>\n        \n        <Card>\n          <div className=\"flex items-center\">\n            <div className=\"p-3 rounded-full bg-green-100 dark:bg-green-900\">\n              <svg className=\"h-6 w-6 text-green-600 dark:text-green-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-secondary-600 dark:text-secondary-400\">\n                High Performers\n              </p>\n              <p className=\"text-2xl font-bold text-secondary-900 dark:text-white\">\n                {bookmarkedEmployees.filter(emp => emp.performanceRating >= 4).length}\n              </p>\n            </div>\n          </div>\n        </Card>\n        \n        <Card>\n          <div className=\"flex items-center\">\n            <div className=\"p-3 rounded-full bg-blue-100 dark:bg-blue-900\">\n              <svg className=\"h-6 w-6 text-blue-600 dark:text-blue-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n              </svg>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-secondary-600 dark:text-secondary-400\">\n                Departments\n              </p>\n              <p className=\"text-2xl font-bold text-secondary-900 dark:text-white\">\n                {new Set(bookmarkedEmployees.map(emp => emp.department)).size}\n              </p>\n            </div>\n          </div>\n        </Card>\n      </div>\n\n      {/* Bookmarked Employees Grid */}\n      {bookmarkedEmployees.length === 0 ? (\n        <Card>\n          <div className=\"text-center py-16\">\n            <BookmarkIcon className=\"mx-auto h-16 w-16 text-secondary-300 dark:text-secondary-600 mb-4\" />\n            <h3 className=\"text-lg font-medium text-secondary-900 dark:text-white mb-2\">\n              No bookmarks yet\n            </h3>\n            <p className=\"text-secondary-500 dark:text-secondary-400 mb-6 max-w-md mx-auto\">\n              Start bookmarking employees from the main dashboard to keep track of your favorite team members.\n            </p>\n            <Button\n              variant=\"primary\"\n              onClick={() => window.location.href = '/'}\n            >\n              Go to Dashboard\n            </Button>\n          </div>\n        </Card>\n      ) : (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n          {bookmarkedEmployees.map((employee) => (\n            <UserCard\n              key={employee.id}\n              employee={employee}\n              onView={(id) => console.log(`Viewing employee ${id}`)}\n              onBookmark={handleBookmark}\n              onPromote={handlePromote}\n              isBookmarked={true}\n            />\n          ))}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;AAWe,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,cAAc,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAE5E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB;YACrB,IAAI;gBACF,WAAW;gBACX,MAAM,WAAW,MAAM,MAAM;gBAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,uBAAuB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,OAC3C,CAAA,GAAA,mHAAA,CAAA,0BAAuB,AAAD,EAAE;gBAG1B,gBAAgB;YAClB,EAAE,OAAO,KAAK;gBACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAChD,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,sBAAsB,aAAa,MAAM,CAAC,CAAA,WAC9C,UAAU,QAAQ,CAAC,SAAS,EAAE;IAGhC,MAAM,iBAAiB,CAAC;QACtB,IAAI,aAAa,KAAK;YACpB,eAAe;QACjB,OAAO;YACL,YAAY;QACd;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,6CAA6C;QAC7C,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,IAAI;IACjD;IAEA,MAAM,oBAAoB;QACxB,UAAU,OAAO,CAAC,CAAA,KAAM,eAAe;IACzC;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC;gCAAgB,WAAU;+BAAjB;;;;;;;;;;;;;;;;;;;;;IAMtB;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC,gIAAA,CAAA,UAAI;sBACH,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAwC;;;;;;kCACvD,8OAAC;wBAAE,WAAU;kCAAmD;;;;;;kCAChE,8OAAC;wBACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;wBACrC,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAA6D;;;;;;0CAG3E,8OAAC;gCAAE,WAAU;0CAA6C;;;;;;;;;;;;oBAK3D,oBAAoB,MAAM,GAAG,mBAC5B,8OAAC,kIAAA,CAAA,UAAM;wBACL,SAAQ;wBACR,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAOL,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,UAAI;kCACH,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,uNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;8CAE1B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAiE;;;;;;sDAG9E,8OAAC;4CAAE,WAAU;sDACV,oBAAoB,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAMnC,8OAAC,gIAAA,CAAA,UAAI;kCACH,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAA6C,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACpG,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAGzE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAiE;;;;;;sDAG9E,8OAAC;4CAAE,WAAU;sDACV,oBAAoB,MAAM,CAAC,CAAA,MAAO,IAAI,iBAAiB,IAAI,GAAG,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAM7E,8OAAC,gIAAA,CAAA,UAAI;kCACH,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAA2C,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAClG,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAGzE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAiE;;;;;;sDAG9E,8OAAC;4CAAE,WAAU;sDACV,IAAI,IAAI,oBAAoB,GAAG,CAAC,CAAA,MAAO,IAAI,UAAU,GAAG,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQtE,oBAAoB,MAAM,KAAK,kBAC9B,8OAAC,gIAAA,CAAA,UAAI;0BACH,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,uNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;sCACxB,8OAAC;4BAAG,WAAU;sCAA8D;;;;;;sCAG5E,8OAAC;4BAAE,WAAU;sCAAmE;;;;;;sCAGhF,8OAAC,kIAAA,CAAA,UAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;sCACvC;;;;;;;;;;;;;;;;qCAML,8OAAC;gBAAI,WAAU;0BACZ,oBAAoB,GAAG,CAAC,CAAC,yBACxB,8OAAC,8HAAA,CAAA,UAAQ;wBAEP,UAAU;wBACV,QAAQ,CAAC,KAAO,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,IAAI;wBACpD,YAAY;wBACZ,WAAW;wBACX,cAAc;uBALT,SAAS,EAAE;;;;;;;;;;;;;;;;AAY9B", "debugId": null}}]}