{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { cn } from '@/lib/utils';\nimport { ButtonProps } from '@/types';\n\nconst Button: React.FC<ButtonProps> = ({\n  children,\n  variant = 'primary',\n  size = 'md',\n  loading = false,\n  disabled = false,\n  onClick,\n  type = 'button',\n  className,\n  ...props\n}) => {\n  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none';\n  \n  const variants = {\n    primary: 'bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700 focus:ring-blue-500 shadow-lg hover:shadow-xl transform hover:scale-105',\n    secondary: 'bg-gradient-to-r from-slate-100 to-slate-200 text-slate-900 hover:from-slate-200 hover:to-slate-300 focus:ring-slate-500 dark:from-slate-800 dark:to-slate-700 dark:text-slate-100 dark:hover:from-slate-700 dark:hover:to-slate-600 shadow-md hover:shadow-lg',\n    outline: 'border border-slate-300 text-slate-700 hover:bg-white/50 focus:ring-slate-500 dark:border-slate-600 dark:text-slate-300 dark:hover:bg-slate-800/50 backdrop-blur-sm',\n    ghost: 'text-slate-700 hover:bg-white/50 focus:ring-slate-500 dark:text-slate-300 dark:hover:bg-slate-800/50 backdrop-blur-sm'\n  };\n\n  const sizes = {\n    sm: 'px-3 py-1.5 text-sm',\n    md: 'px-4 py-2 text-sm',\n    lg: 'px-6 py-3 text-base'\n  };\n\n  return (\n    <motion.button\n      type={type}\n      onClick={onClick}\n      disabled={disabled || loading}\n      whileHover={{ scale: disabled || loading ? 1 : 1.02 }}\n      whileTap={{ scale: disabled || loading ? 1 : 0.98 }}\n      transition={{ type: \"spring\", stiffness: 400, damping: 17 }}\n      className={cn(\n        baseClasses,\n        variants[variant],\n        sizes[size],\n        className\n      )}\n      {...props}\n    >\n      {loading && (\n        <svg\n          className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n          fill=\"none\"\n          viewBox=\"0 0 24 24\"\n        >\n          <circle\n            className=\"opacity-25\"\n            cx=\"12\"\n            cy=\"12\"\n            r=\"10\"\n            stroke=\"currentColor\"\n            strokeWidth=\"4\"\n          />\n          <path\n            className=\"opacity-75\"\n            fill=\"currentColor\"\n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n          />\n        </svg>\n      )}\n      {children}\n    </motion.button>\n  );\n};\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAGA,MAAM,SAAgC,CAAC,EACrC,QAAQ,EACR,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,OAAO,EACP,OAAO,QAAQ,EACf,SAAS,EACT,GAAG,OACJ;IACC,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,MAAM;QACN,SAAS;QACT,UAAU,YAAY;QACtB,YAAY;YAAE,OAAO,YAAY,UAAU,IAAI;QAAK;QACpD,UAAU;YAAE,OAAO,YAAY,UAAU,IAAI;QAAK;QAClD,YAAY;YAAE,MAAM;YAAU,WAAW;YAAK,SAAS;QAAG;QAC1D,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAED,GAAG,KAAK;;YAER,yBACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/app/login/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/contexts/AuthContext';\nimport Button from '@/components/ui/Button';\nimport { EyeIcon, EyeSlashIcon, UserIcon, LockClosedIcon } from '@heroicons/react/24/outline';\n\nexport default function LoginPage() {\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [showPassword, setShowPassword] = useState(false);\n  const [error, setError] = useState('');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  \n  const { login, isAuthenticated } = useAuth();\n  const router = useRouter();\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      router.push('/');\n    }\n  }, [isAuthenticated, router]);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError('');\n    setIsSubmitting(true);\n\n    if (!email || !password) {\n      setError('Please fill in all fields');\n      setIsSubmitting(false);\n      return;\n    }\n\n    const success = await login(email, password);\n    \n    if (success) {\n      router.push('/');\n    } else {\n      setError('Invalid email or password');\n    }\n    \n    setIsSubmitting(false);\n  };\n\n  const demoCredentials = [\n    { email: '<EMAIL>', password: 'admin123', role: 'Admin' },\n    { email: '<EMAIL>', password: 'hr123', role: 'HR Manager' },\n    { email: '<EMAIL>', password: 'manager123', role: 'Team Manager' }\n  ];\n\n  const fillDemo = (email: string, password: string) => {\n    setEmail(email);\n    setPassword(password);\n    setError('');\n  };\n\n  return (\n    <motion.div\n      initial={{ opacity: 0 }}\n      animate={{ opacity: 1 }}\n      transition={{ duration: 0.5 }}\n      className=\"min-h-screen flex items-center justify-center relative overflow-hidden\"\n    >\n      {/* Background */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900\"></div>\n      \n      {/* Animated background elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-purple-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob\"></div>\n        <div className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000\"></div>\n        <div className=\"absolute top-40 left-40 w-80 h-80 bg-pink-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000\"></div>\n      </div>\n\n      <motion.div\n        initial={{ y: 20, opacity: 0 }}\n        animate={{ y: 0, opacity: 1 }}\n        transition={{ delay: 0.2, duration: 0.5 }}\n        className=\"relative z-10 w-full max-w-md p-8\"\n      >\n        {/* Logo and Title */}\n        <div className=\"text-center mb-8\">\n          <div className=\"w-20 h-20 bg-gradient-to-br from-blue-600 to-purple-600 rounded-2xl mx-auto mb-4 flex items-center justify-center shadow-2xl\">\n            <UserIcon className=\"h-10 w-10 text-white\" />\n          </div>\n          <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white mb-2\">\n            Welcome Back\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            Sign in to your HR Elite Dashboard\n          </p>\n        </div>\n\n        {/* Login Form */}\n        <div className=\"glass backdrop-blur-xl bg-white/80 dark:bg-slate-900/80 border border-white/20 dark:border-slate-700/50 rounded-2xl shadow-2xl p-8\">\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            {/* Email Field */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Email Address\n              </label>\n              <div className=\"relative\">\n                <div className=\"absolute left-3 top-1/2 transform -translate-y-1/2\">\n                  <UserIcon className=\"h-5 w-5 text-gray-400\" />\n                </div>\n                <input\n                  type=\"email\"\n                  value={email}\n                  onChange={(e) => setEmail(e.target.value)}\n                  className=\"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white/50 dark:bg-gray-800/50 text-gray-900 dark:text-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\"\n                  placeholder=\"Enter your email\"\n                  required\n                />\n              </div>\n            </div>\n\n            {/* Password Field */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Password\n              </label>\n              <div className=\"relative\">\n                <div className=\"absolute left-3 top-1/2 transform -translate-y-1/2\">\n                  <LockClosedIcon className=\"h-5 w-5 text-gray-400\" />\n                </div>\n                <input\n                  type={showPassword ? 'text' : 'password'}\n                  value={password}\n                  onChange={(e) => setPassword(e.target.value)}\n                  className=\"w-full pl-10 pr-12 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white/50 dark:bg-gray-800/50 text-gray-900 dark:text-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\"\n                  placeholder=\"Enter your password\"\n                  required\n                />\n                <button\n                  type=\"button\"\n                  onClick={() => setShowPassword(!showPassword)}\n                  className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n                >\n                  {showPassword ? (\n                    <EyeSlashIcon className=\"h-5 w-5\" />\n                  ) : (\n                    <EyeIcon className=\"h-5 w-5\" />\n                  )}\n                </button>\n              </div>\n            </div>\n\n            {/* Error Message */}\n            {error && (\n              <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-3\">\n                <p className=\"text-red-600 dark:text-red-400 text-sm\">{error}</p>\n              </div>\n            )}\n\n            {/* Submit Button */}\n            <Button\n              type=\"submit\"\n              loading={isSubmitting}\n              className=\"w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200\"\n            >\n              {isSubmitting ? 'Signing In...' : 'Sign In'}\n            </Button>\n          </form>\n\n          {/* Demo Credentials */}\n          <div className=\"mt-8 pt-6 border-t border-gray-200/50 dark:border-gray-700/50\">\n            <p className=\"text-sm text-gray-600 dark:text-gray-400 text-center mb-4\">\n              Demo Credentials:\n            </p>\n            <div className=\"space-y-2\">\n              {demoCredentials.map((cred, index) => (\n                <button\n                  key={index}\n                  onClick={() => fillDemo(cred.email, cred.password)}\n                  className=\"w-full text-left p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50 hover:bg-gray-100 dark:hover:bg-gray-700/50 transition-colors duration-200 border border-gray-200 dark:border-gray-700\"\n                >\n                  <div className=\"flex justify-between items-center\">\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                        {cred.role}\n                      </p>\n                      <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                        {cred.email}\n                      </p>\n                    </div>\n                    <div className=\"text-xs text-blue-600 dark:text-blue-400 font-medium\">\n                      Click to fill\n                    </div>\n                  </div>\n                </button>\n              ))}\n            </div>\n          </div>\n        </div>\n      </motion.div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AASe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,EAAE,KAAK,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACzC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB;YACnB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAiB;KAAO;IAE5B,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,SAAS;QACT,gBAAgB;QAEhB,IAAI,CAAC,SAAS,CAAC,UAAU;YACvB,SAAS;YACT,gBAAgB;YAChB;QACF;QAEA,MAAM,UAAU,MAAM,MAAM,OAAO;QAEnC,IAAI,SAAS;YACX,OAAO,IAAI,CAAC;QACd,OAAO;YACL,SAAS;QACX;QAEA,gBAAgB;IAClB;IAEA,MAAM,kBAAkB;QACtB;YAAE,OAAO;YAAmB,UAAU;YAAY,MAAM;QAAQ;QAChE;YAAE,OAAO;YAAgB,UAAU;YAAS,MAAM;QAAa;QAC/D;YAAE,OAAO;YAAqB,UAAU;YAAc,MAAM;QAAe;KAC5E;IAED,MAAM,WAAW,CAAC,OAAe;QAC/B,SAAS;QACT,YAAY;QACZ,SAAS;IACX;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAU;;0BAGV,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,GAAG;oBAAI,SAAS;gBAAE;gBAC7B,SAAS;oBAAE,GAAG;oBAAG,SAAS;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;oBAAK,UAAU;gBAAI;gBACxC,WAAU;;kCAGV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,+MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAEtB,8OAAC;gCAAG,WAAU;0CAAwD;;;;;;0CAGtE,8OAAC;gCAAE,WAAU;0CAAmC;;;;;;;;;;;;kCAMlD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,UAAU;gCAAc,WAAU;;kDAEtC,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,+MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;kEAEtB,8OAAC;wDACC,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wDACxC,WAAU;wDACV,aAAY;wDACZ,QAAQ;;;;;;;;;;;;;;;;;;kDAMd,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,2NAAA,CAAA,iBAAc;4DAAC,WAAU;;;;;;;;;;;kEAE5B,8OAAC;wDACC,MAAM,eAAe,SAAS;wDAC9B,OAAO;wDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wDAC3C,WAAU;wDACV,aAAY;wDACZ,QAAQ;;;;;;kEAEV,8OAAC;wDACC,MAAK;wDACL,SAAS,IAAM,gBAAgB,CAAC;wDAChC,WAAU;kEAET,6BACC,8OAAC,uNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;iFAExB,8OAAC,6MAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;oCAO1B,uBACC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDAA0C;;;;;;;;;;;kDAK3D,8OAAC,kIAAA,CAAA,UAAM;wCACL,MAAK;wCACL,SAAS;wCACT,WAAU;kDAET,eAAe,kBAAkB;;;;;;;;;;;;0CAKtC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAA4D;;;;;;kDAGzE,8OAAC;wCAAI,WAAU;kDACZ,gBAAgB,GAAG,CAAC,CAAC,MAAM,sBAC1B,8OAAC;gDAEC,SAAS,IAAM,SAAS,KAAK,KAAK,EAAE,KAAK,QAAQ;gDACjD,WAAU;0DAEV,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EACV,KAAK,IAAI;;;;;;8EAEZ,8OAAC;oEAAE,WAAU;8EACV,KAAK,KAAK;;;;;;;;;;;;sEAGf,8OAAC;4DAAI,WAAU;sEAAuD;;;;;;;;;;;;+CAbnE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBvB", "debugId": null}}]}