{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_82ad035c._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_fb905aa2.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/login(\\\\.json)?[\\/#\\?]?$", "originalSource": "/login"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/dashboard(\\\\.json)?[\\/#\\?]?$", "originalSource": "/dashboard"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/bookmarks(\\\\.json)?[\\/#\\?]?$", "originalSource": "/bookmarks"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/analytics(\\\\.json)?[\\/#\\?]?$", "originalSource": "/analytics"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/employee(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/employee/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "1YdCMC+OfSJ4QuPZDVSpwcJ5/IdKgzgJy/NFRC946l4=", "__NEXT_PREVIEW_MODE_ID": "4dbbfca477471cdac1ba46fb268e83b8", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "09b9dacfdc34d8df0a74d0dd99592ff365b68ded8b6aeb22a927570378e8970c", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0c7f04b4ada3af129180632a84feb51ae323d08e76847ce961631b8b01098717"}}}, "sortedMiddleware": ["/"], "functions": {}}