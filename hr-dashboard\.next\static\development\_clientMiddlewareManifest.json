[{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/login(\\\\.json)?[\\/#\\?]?$", "originalSource": "/login"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/dashboard(\\\\.json)?[\\/#\\?]?$", "originalSource": "/dashboard"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/bookmarks(\\\\.json)?[\\/#\\?]?$", "originalSource": "/bookmarks"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/analytics(\\\\.json)?[\\/#\\?]?$", "originalSource": "/analytics"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/employee(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/employee/:path*"}]