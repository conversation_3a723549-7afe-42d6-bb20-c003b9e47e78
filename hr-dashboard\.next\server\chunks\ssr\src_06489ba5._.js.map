{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/hooks/useSearch.ts"], "sourcesContent": ["import { useState, useMemo } from 'react';\nimport { Employee, SearchFilters } from '@/types';\n\nexport const useSearch = (employees: Employee[]) => {\n  const [filters, setFilters] = useState<SearchFilters>({\n    query: '',\n    departments: [],\n    minRating: 0,\n    maxRating: 5\n  });\n\n  const filteredEmployees = useMemo(() => {\n    return employees.filter(employee => {\n      // Text search\n      const matchesQuery = filters.query === '' || \n        employee.firstName.toLowerCase().includes(filters.query.toLowerCase()) ||\n        employee.lastName.toLowerCase().includes(filters.query.toLowerCase()) ||\n        employee.email.toLowerCase().includes(filters.query.toLowerCase()) ||\n        employee.department.toLowerCase().includes(filters.query.toLowerCase());\n\n      // Department filter\n      const matchesDepartment = filters.departments.length === 0 || \n        filters.departments.includes(employee.department);\n\n      // Rating filter\n      const matchesRating = employee.performanceRating >= filters.minRating && \n        employee.performanceRating <= filters.maxRating;\n\n      return matchesQuery && matchesDepartment && matchesRating;\n    });\n  }, [employees, filters]);\n\n  const updateQuery = (query: string) => {\n    setFilters(prev => ({ ...prev, query }));\n  };\n\n  const updateDepartments = (departments: string[]) => {\n    setFilters(prev => ({ ...prev, departments }));\n  };\n\n  const updateRatingRange = (minRating: number, maxRating: number) => {\n    setFilters(prev => ({ ...prev, minRating, maxRating }));\n  };\n\n  const clearFilters = () => {\n    setFilters({\n      query: '',\n      departments: [],\n      minRating: 0,\n      maxRating: 5\n    });\n  };\n\n  const availableDepartments = useMemo(() => {\n    const departments = new Set(employees.map(emp => emp.department));\n    return Array.from(departments).sort();\n  }, [employees]);\n\n  return {\n    filters,\n    filteredEmployees,\n    updateQuery,\n    updateDepartments,\n    updateRatingRange,\n    clearFilters,\n    availableDepartments\n  };\n};\n"], "names": [], "mappings": ";;;AAAA;;AAGO,MAAM,YAAY,CAAC;IACxB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QACpD,OAAO;QACP,aAAa,EAAE;QACf,WAAW;QACX,WAAW;IACb;IAEA,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAChC,OAAO,UAAU,MAAM,CAAC,CAAA;YACtB,cAAc;YACd,MAAM,eAAe,QAAQ,KAAK,KAAK,MACrC,SAAS,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,KAAK,CAAC,WAAW,OACnE,SAAS,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,KAAK,CAAC,WAAW,OAClE,SAAS,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,KAAK,CAAC,WAAW,OAC/D,SAAS,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,KAAK,CAAC,WAAW;YAEtE,oBAAoB;YACpB,MAAM,oBAAoB,QAAQ,WAAW,CAAC,MAAM,KAAK,KACvD,QAAQ,WAAW,CAAC,QAAQ,CAAC,SAAS,UAAU;YAElD,gBAAgB;YAChB,MAAM,gBAAgB,SAAS,iBAAiB,IAAI,QAAQ,SAAS,IACnE,SAAS,iBAAiB,IAAI,QAAQ,SAAS;YAEjD,OAAO,gBAAgB,qBAAqB;QAC9C;IACF,GAAG;QAAC;QAAW;KAAQ;IAEvB,MAAM,cAAc,CAAC;QACnB,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE;YAAM,CAAC;IACxC;IAEA,MAAM,oBAAoB,CAAC;QACzB,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE;YAAY,CAAC;IAC9C;IAEA,MAAM,oBAAoB,CAAC,WAAmB;QAC5C,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE;gBAAW;YAAU,CAAC;IACvD;IAEA,MAAM,eAAe;QACnB,WAAW;YACT,OAAO;YACP,aAAa,EAAE;YACf,WAAW;YACX,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACnC,MAAM,cAAc,IAAI,IAAI,UAAU,GAAG,CAAC,CAAA,MAAO,IAAI,UAAU;QAC/D,OAAO,MAAM,IAAI,CAAC,aAAa,IAAI;IACrC,GAAG;QAAC;KAAU;IAEd,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/hooks/useBookmarks.ts"], "sourcesContent": ["import { useBookmarkContext } from '@/contexts/BookmarkContext';\n\nexport const useBookmarks = () => {\n  return useBookmarkContext();\n};\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,eAAe;IAC1B,OAAO,CAAA,GAAA,mIAAA,CAAA,qBAAkB,AAAD;AAC1B", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface CardProps {\n  children: React.ReactNode;\n  className?: string;\n  padding?: 'none' | 'sm' | 'md' | 'lg';\n  hover?: boolean;\n}\n\nconst Card: React.FC<CardProps> = ({\n  children,\n  className,\n  padding = 'md',\n  hover = false,\n  ...props\n}) => {\n  const baseClasses = 'glass backdrop-blur-xl bg-white/80 dark:bg-slate-900/80 border border-white/20 dark:border-slate-700/50 rounded-2xl shadow-xl';\n\n  const paddingClasses = {\n    none: '',\n    sm: 'p-4',\n    md: 'p-6',\n    lg: 'p-8'\n  };\n\n  const hoverClasses = hover ? 'hover:shadow-2xl hover:transform hover:scale-105 transition-all duration-300' : '';\n\n  return (\n    <div\n      className={cn(\n        baseClasses,\n        paddingClasses[padding],\n        hoverClasses,\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n};\n\nexport default Card;\n"], "names": [], "mappings": ";;;;AACA;;;AASA,MAAM,OAA4B,CAAC,EACjC,QAAQ,EACR,SAAS,EACT,UAAU,IAAI,EACd,QAAQ,KAAK,EACb,GAAG,OACJ;IACC,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,MAAM;QACN,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,eAAe,QAAQ,iFAAiF;IAE9G,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,cAAc,CAAC,QAAQ,EACvB,cACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/components/ui/Badge.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\nimport { BadgeProps } from '@/types';\n\nconst Badge: React.FC<BadgeProps> = ({\n  children,\n  variant,\n  size = 'md',\n  ...props\n}) => {\n  const baseClasses = 'inline-flex items-center font-medium rounded-full';\n  \n  const variants = {\n    primary: 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg',\n    secondary: 'bg-gradient-to-r from-slate-100 to-slate-200 text-slate-800 dark:from-slate-700 dark:to-slate-600 dark:text-slate-200 shadow-md',\n    success: 'bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-lg',\n    warning: 'bg-gradient-to-r from-yellow-500 to-orange-600 text-white shadow-lg',\n    danger: 'bg-gradient-to-r from-red-500 to-pink-600 text-white shadow-lg'\n  };\n\n  const sizes = {\n    sm: 'px-2 py-0.5 text-xs',\n    md: 'px-2.5 py-1 text-sm',\n    lg: 'px-3 py-1.5 text-base'\n  };\n\n  return (\n    <span\n      className={cn(\n        baseClasses,\n        variants[variant],\n        sizes[size]\n      )}\n      {...props}\n    >\n      {children}\n    </span>\n  );\n};\n\nexport default Badge;\n"], "names": [], "mappings": ";;;;AACA;;;AAGA,MAAM,QAA8B,CAAC,EACnC,QAAQ,EACR,OAAO,EACP,OAAO,IAAI,EACX,GAAG,OACJ;IACC,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,SAAS;QACT,QAAQ;IACV;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK;QAEZ,GAAG,KAAK;kBAER;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 157, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\nimport { ButtonProps } from '@/types';\n\nconst Button: React.FC<ButtonProps> = ({\n  children,\n  variant = 'primary',\n  size = 'md',\n  loading = false,\n  disabled = false,\n  onClick,\n  type = 'button',\n  className,\n  ...props\n}) => {\n  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none';\n  \n  const variants = {\n    primary: 'bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700 focus:ring-blue-500 shadow-lg hover:shadow-xl transform hover:scale-105',\n    secondary: 'bg-gradient-to-r from-slate-100 to-slate-200 text-slate-900 hover:from-slate-200 hover:to-slate-300 focus:ring-slate-500 dark:from-slate-800 dark:to-slate-700 dark:text-slate-100 dark:hover:from-slate-700 dark:hover:to-slate-600 shadow-md hover:shadow-lg',\n    outline: 'border border-slate-300 text-slate-700 hover:bg-white/50 focus:ring-slate-500 dark:border-slate-600 dark:text-slate-300 dark:hover:bg-slate-800/50 backdrop-blur-sm',\n    ghost: 'text-slate-700 hover:bg-white/50 focus:ring-slate-500 dark:text-slate-300 dark:hover:bg-slate-800/50 backdrop-blur-sm'\n  };\n\n  const sizes = {\n    sm: 'px-3 py-1.5 text-sm',\n    md: 'px-4 py-2 text-sm',\n    lg: 'px-6 py-3 text-base'\n  };\n\n  return (\n    <button\n      type={type}\n      onClick={onClick}\n      disabled={disabled || loading}\n      className={cn(\n        baseClasses,\n        variants[variant],\n        sizes[size],\n        className\n      )}\n      {...props}\n    >\n      {loading && (\n        <svg\n          className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n          fill=\"none\"\n          viewBox=\"0 0 24 24\"\n        >\n          <circle\n            className=\"opacity-25\"\n            cx=\"12\"\n            cy=\"12\"\n            r=\"10\"\n            stroke=\"currentColor\"\n            strokeWidth=\"4\"\n          />\n          <path\n            className=\"opacity-75\"\n            fill=\"currentColor\"\n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n          />\n        </svg>\n      )}\n      {children}\n    </button>\n  );\n};\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AACA;;;AAGA,MAAM,SAAgC,CAAC,EACrC,QAAQ,EACR,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,OAAO,EACP,OAAO,QAAQ,EACf,SAAS,EACT,GAAG,OACJ;IACC,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,MAAM;QACN,SAAS;QACT,UAAU,YAAY;QACtB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAED,GAAG,KAAK;;YAER,yBACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 230, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/components/ui/StarRating.tsx"], "sourcesContent": ["import React from 'react';\nimport { StarIcon } from '@heroicons/react/24/solid';\nimport { StarIcon as StarOutlineIcon } from '@heroicons/react/24/outline';\nimport { cn } from '@/lib/utils';\n\ninterface StarRatingProps {\n  rating: number;\n  maxRating?: number;\n  size?: 'sm' | 'md' | 'lg';\n  showValue?: boolean;\n  interactive?: boolean;\n  onRatingChange?: (rating: number) => void;\n  className?: string;\n}\n\nconst StarRating: React.FC<StarRatingProps> = ({\n  rating,\n  maxRating = 5,\n  size = 'md',\n  showValue = false,\n  interactive = false,\n  onRatingChange,\n  className\n}) => {\n  const sizeClasses = {\n    sm: 'h-4 w-4',\n    md: 'h-5 w-5',\n    lg: 'h-6 w-6'\n  };\n\n  const handleStarClick = (starRating: number) => {\n    if (interactive && onRatingChange) {\n      onRatingChange(starRating);\n    }\n  };\n\n  return (\n    <div className={cn('flex items-center gap-1', className)}>\n      <div className=\"flex\">\n        {Array.from({ length: maxRating }, (_, index) => {\n          const starRating = index + 1;\n          const isFilled = starRating <= rating;\n          \n          return (\n            <button\n              key={index}\n              type=\"button\"\n              onClick={() => handleStarClick(starRating)}\n              disabled={!interactive}\n              className={cn(\n                'transition-colors',\n                interactive ? 'hover:scale-110 cursor-pointer' : 'cursor-default',\n                isFilled ? 'text-yellow-400' : 'text-secondary-300 dark:text-secondary-600'\n              )}\n            >\n              {isFilled ? (\n                <StarIcon className={sizeClasses[size]} />\n              ) : (\n                <StarOutlineIcon className={sizeClasses[size]} />\n              )}\n            </button>\n          );\n        })}\n      </div>\n      \n      {showValue && (\n        <span className=\"ml-2 text-sm text-secondary-600 dark:text-secondary-400\">\n          {rating.toFixed(1)}/{maxRating}\n        </span>\n      )}\n    </div>\n  );\n};\n\nexport default StarRating;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAYA,MAAM,aAAwC,CAAC,EAC7C,MAAM,EACN,YAAY,CAAC,EACb,OAAO,IAAI,EACX,YAAY,KAAK,EACjB,cAAc,KAAK,EACnB,cAAc,EACd,SAAS,EACV;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,eAAe,gBAAgB;YACjC,eAAe;QACjB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;;0BAC5C,8OAAC;gBAAI,WAAU;0BACZ,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAU,GAAG,CAAC,GAAG;oBACrC,MAAM,aAAa,QAAQ;oBAC3B,MAAM,WAAW,cAAc;oBAE/B,qBACE,8OAAC;wBAEC,MAAK;wBACL,SAAS,IAAM,gBAAgB;wBAC/B,UAAU,CAAC;wBACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qBACA,cAAc,mCAAmC,kBACjD,WAAW,oBAAoB;kCAGhC,yBACC,8OAAC,6MAAA,CAAA,WAAQ;4BAAC,WAAW,WAAW,CAAC,KAAK;;;;;qFAEtC,8OAAC,+MAAA,CAAA,WAAe;4BAAC,WAAW,WAAW,CAAC,KAAK;;;;;;uBAb1C;;;;;gBAiBX;;;;;;YAGD,2BACC,8OAAC;gBAAK,WAAU;;oBACb,OAAO,OAAO,CAAC;oBAAG;oBAAE;;;;;;;;;;;;;AAK/B;uCAEe", "debugId": null}}, {"offset": {"line": 315, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/components/ui/Modal.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { cn } from '@/lib/utils';\nimport { ModalProps } from '@/types';\nimport { XMarkIcon } from '@heroicons/react/24/outline';\n\nconst Modal: React.FC<ModalProps> = ({\n  isOpen,\n  onClose,\n  title,\n  children,\n  size = 'md'\n}) => {\n  useEffect(() => {\n    const handleEscape = (e: KeyboardEvent) => {\n      if (e.key === 'Escape') {\n        onClose();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'hidden';\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen, onClose]);\n\n  if (!isOpen) return null;\n\n  const sizeClasses = {\n    sm: 'max-w-md',\n    md: 'max-w-lg',\n    lg: 'max-w-2xl',\n    xl: 'max-w-4xl'\n  };\n\n  return (\n    <div className=\"fixed inset-0 z-50 overflow-y-auto\">\n      <div className=\"flex min-h-screen items-center justify-center p-4\">\n        {/* Backdrop */}\n        <div\n          className=\"fixed inset-0 bg-black bg-opacity-50 transition-opacity\"\n          onClick={onClose}\n        />\n        \n        {/* Modal */}\n        <div\n          className={cn(\n            'relative w-full bg-white dark:bg-secondary-800 rounded-lg shadow-xl transform transition-all',\n            sizeClasses[size]\n          )}\n        >\n          {/* Header */}\n          <div className=\"flex items-center justify-between p-6 border-b border-secondary-200 dark:border-secondary-700\">\n            <h3 className=\"text-lg font-semibold text-secondary-900 dark:text-secondary-100\">\n              {title}\n            </h3>\n            <button\n              onClick={onClose}\n              className=\"text-secondary-400 hover:text-secondary-600 dark:hover:text-secondary-300 transition-colors\"\n            >\n              <XMarkIcon className=\"h-6 w-6\" />\n            </button>\n          </div>\n          \n          {/* Content */}\n          <div className=\"p-6\">\n            {children}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Modal;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,QAA8B,CAAC,EACnC,MAAM,EACN,OAAO,EACP,KAAK,EACL,QAAQ,EACR,OAAO,IAAI,EACZ;IACC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,CAAC;YACpB,IAAI,EAAE,GAAG,KAAK,UAAU;gBACtB;YACF;QACF;QAEA,IAAI,QAAQ;YACV,SAAS,gBAAgB,CAAC,WAAW;YACrC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QAEA,OAAO;YACL,SAAS,mBAAmB,CAAC,WAAW;YACxC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG;QAAC;QAAQ;KAAQ;IAEpB,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBACC,WAAU;oBACV,SAAS;;;;;;8BAIX,8OAAC;oBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gGACA,WAAW,CAAC,KAAK;;sCAInB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX;;;;;;8CAEH,8OAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKzB,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb;uCAEe", "debugId": null}}, {"offset": {"line": 431, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/components/UserCard.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Image from 'next/image';\nimport { useRouter } from 'next/navigation';\nimport { UserCardProps } from '@/types';\nimport { getPerformanceBadgeVariant, getPerformanceLabel, cn } from '@/lib/utils';\nimport Card from '@/components/ui/Card';\nimport Badge from '@/components/ui/Badge';\nimport Button from '@/components/ui/Button';\nimport StarRating from '@/components/ui/StarRating';\nimport Modal from '@/components/ui/Modal';\nimport {\n  EyeIcon,\n  BookmarkIcon as BookmarkSolidIcon,\n  ArrowUpIcon\n} from '@heroicons/react/24/solid';\nimport {\n  BookmarkIcon as BookmarkOutlineIcon\n} from '@heroicons/react/24/outline';\n\nconst UserCard: React.FC<UserCardProps> = ({\n  employee,\n  onView,\n  onBookmark,\n  onPromote,\n  isBookmarked\n}) => {\n  const router = useRouter();\n  const [showPromoteModal, setShowPromoteModal] = useState(false);\n\n  const handleView = () => {\n    router.push(`/employee/${employee.id}`);\n  };\n\n  const handleBookmark = () => {\n    onBookmark(employee.id);\n  };\n\n  const handlePromote = () => {\n    setShowPromoteModal(true);\n  };\n\n  const confirmPromote = () => {\n    onPromote(employee.id);\n    setShowPromoteModal(false);\n  };\n\n  return (\n    <>\n      <Card hover className=\"animate-fade-in relative overflow-hidden group\">\n        {/* Gradient overlay */}\n        <div className=\"absolute inset-0 bg-gradient-to-br from-blue-50/50 to-purple-50/50 dark:from-slate-800/50 dark:to-slate-700/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n\n        <div className=\"flex flex-col h-full relative z-10\">\n          {/* Header with avatar and basic info */}\n          <div className=\"flex items-start space-x-4 mb-6\">\n            <div className=\"relative h-16 w-16 rounded-2xl overflow-hidden bg-gradient-to-br from-blue-100 to-purple-100 dark:from-slate-700 dark:to-slate-600 flex-shrink-0 ring-2 ring-white/50 dark:ring-slate-600/50 shadow-lg\">\n              <Image\n                src={employee.image}\n                alt={`${employee.firstName} ${employee.lastName}`}\n                fill\n                className=\"object-cover\"\n                sizes=\"64px\"\n              />\n            </div>\n\n            <div className=\"flex-1 min-w-0\">\n              <h3 className=\"text-lg font-bold text-slate-900 dark:text-white truncate mb-1\">\n                {employee.firstName} {employee.lastName}\n              </h3>\n              <p className=\"text-sm text-slate-600 dark:text-slate-400 truncate mb-2\">\n                {employee.email}\n              </p>\n              <div className=\"flex items-center space-x-2\">\n                <span className=\"text-xs text-slate-500 dark:text-slate-400 bg-slate-100 dark:bg-slate-800 px-2 py-1 rounded-full\">\n                  Age: {employee.age}\n                </span>\n                <Badge variant=\"secondary\" size=\"sm\" className=\"bg-gradient-to-r from-blue-500 to-purple-600 text-white border-0\">\n                  {employee.department}\n                </Badge>\n              </div>\n            </div>\n          </div>\n\n          {/* Performance rating */}\n          <div className=\"mb-6\">\n            <div className=\"flex items-center justify-between mb-3\">\n              <span className=\"text-sm font-semibold text-slate-700 dark:text-slate-300\">\n                Performance\n              </span>\n              <Badge\n                variant={getPerformanceBadgeVariant(employee.performanceRating)}\n                size=\"sm\"\n                className=\"shadow-md\"\n              >\n                {getPerformanceLabel(employee.performanceRating)}\n              </Badge>\n            </div>\n            <div className=\"bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-700 p-3 rounded-xl\">\n              <StarRating rating={employee.performanceRating} showValue />\n            </div>\n          </div>\n\n          {/* Action buttons */}\n          <div className=\"flex space-x-2 mt-auto\">\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={handleView}\n              className=\"flex-1 bg-gradient-to-r from-blue-500 to-purple-600 text-white border-0 hover:from-blue-600 hover:to-purple-700 shadow-lg\"\n            >\n              <EyeIcon className=\"h-4 w-4 mr-1\" />\n              View\n            </Button>\n\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={handleBookmark}\n              className={cn(\n                \"p-2 rounded-xl transition-all duration-200 hover:shadow-md\",\n                isBookmarked\n                  ? 'text-yellow-500 bg-yellow-50 dark:bg-yellow-900/20 hover:text-yellow-600'\n                  : 'text-slate-500 hover:text-yellow-500 hover:bg-yellow-50 dark:hover:bg-yellow-900/20'\n              )}\n            >\n              {isBookmarked ? (\n                <BookmarkSolidIcon className=\"h-4 w-4\" />\n              ) : (\n                <BookmarkOutlineIcon className=\"h-4 w-4\" />\n              )}\n            </Button>\n\n            <Button\n              variant=\"secondary\"\n              size=\"sm\"\n              onClick={handlePromote}\n              className=\"bg-gradient-to-r from-green-500 to-emerald-600 text-white border-0 hover:from-green-600 hover:to-emerald-700 shadow-lg\"\n            >\n              <ArrowUpIcon className=\"h-4 w-4 mr-1\" />\n              Promote\n            </Button>\n          </div>\n        </div>\n      </Card>\n\n      {/* Promote Modal */}\n      <Modal\n        isOpen={showPromoteModal}\n        onClose={() => setShowPromoteModal(false)}\n        title=\"Promote Employee\"\n        size=\"md\"\n      >\n        <div className=\"space-y-4\">\n          <p className=\"text-secondary-600 dark:text-secondary-400\">\n            Are you sure you want to promote{' '}\n            <span className=\"font-semibold text-secondary-900 dark:text-white\">\n              {employee.firstName} {employee.lastName}\n            </span>\n            ?\n          </p>\n          \n          <div className=\"bg-secondary-50 dark:bg-secondary-800 p-4 rounded-lg\">\n            <h4 className=\"font-medium text-secondary-900 dark:text-white mb-2\">\n              Current Details:\n            </h4>\n            <ul className=\"text-sm text-secondary-600 dark:text-secondary-400 space-y-1\">\n              <li>Department: {employee.department}</li>\n              <li>Performance Rating: {employee.performanceRating}/5</li>\n              <li>Email: {employee.email}</li>\n            </ul>\n          </div>\n          \n          <div className=\"flex space-x-3 pt-4\">\n            <Button\n              variant=\"outline\"\n              onClick={() => setShowPromoteModal(false)}\n              className=\"flex-1\"\n            >\n              Cancel\n            </Button>\n            <Button\n              variant=\"primary\"\n              onClick={confirmPromote}\n              className=\"flex-1\"\n            >\n              Confirm Promotion\n            </Button>\n          </div>\n        </div>\n      </Modal>\n    </>\n  );\n};\n\nexport default UserCard;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAKA;AAjBA;;;;;;;;;;;;;AAqBA,MAAM,WAAoC,CAAC,EACzC,QAAQ,EACR,MAAM,EACN,UAAU,EACV,SAAS,EACT,YAAY,EACb;IACC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,aAAa;QACjB,OAAO,IAAI,CAAC,CAAC,UAAU,EAAE,SAAS,EAAE,EAAE;IACxC;IAEA,MAAM,iBAAiB;QACrB,WAAW,SAAS,EAAE;IACxB;IAEA,MAAM,gBAAgB;QACpB,oBAAoB;IACtB;IAEA,MAAM,iBAAiB;QACrB,UAAU,SAAS,EAAE;QACrB,oBAAoB;IACtB;IAEA,qBACE;;0BACE,8OAAC,gIAAA,CAAA,UAAI;gBAAC,KAAK;gBAAC,WAAU;;kCAEpB,8OAAC;wBAAI,WAAU;;;;;;kCAEf,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAK,SAAS,KAAK;4CACnB,KAAK,GAAG,SAAS,SAAS,CAAC,CAAC,EAAE,SAAS,QAAQ,EAAE;4CACjD,IAAI;4CACJ,WAAU;4CACV,OAAM;;;;;;;;;;;kDAIV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;oDACX,SAAS,SAAS;oDAAC;oDAAE,SAAS,QAAQ;;;;;;;0DAEzC,8OAAC;gDAAE,WAAU;0DACV,SAAS,KAAK;;;;;;0DAEjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;;4DAAmG;4DAC3G,SAAS,GAAG;;;;;;;kEAEpB,8OAAC,iIAAA,CAAA,UAAK;wDAAC,SAAQ;wDAAY,MAAK;wDAAK,WAAU;kEAC5C,SAAS,UAAU;;;;;;;;;;;;;;;;;;;;;;;;0CAO5B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAA2D;;;;;;0DAG3E,8OAAC,iIAAA,CAAA,UAAK;gDACJ,SAAS,CAAA,GAAA,mHAAA,CAAA,6BAA0B,AAAD,EAAE,SAAS,iBAAiB;gDAC9D,MAAK;gDACL,WAAU;0DAET,CAAA,GAAA,mHAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS,iBAAiB;;;;;;;;;;;;kDAGnD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,sIAAA,CAAA,UAAU;4CAAC,QAAQ,SAAS,iBAAiB;4CAAE,SAAS;;;;;;;;;;;;;;;;;0CAK7D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,UAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,8OAAC,2MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAItC,8OAAC,kIAAA,CAAA,UAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA,eACI,6EACA;kDAGL,6BACC,8OAAC,qNAAA,CAAA,eAAiB;4CAAC,WAAU;;;;;qGAE7B,8OAAC,uNAAA,CAAA,eAAmB;4CAAC,WAAU;;;;;;;;;;;kDAInC,8OAAC,kIAAA,CAAA,UAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,8OAAC,mNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhD,8OAAC,iIAAA,CAAA,UAAK;gBACJ,QAAQ;gBACR,SAAS,IAAM,oBAAoB;gBACnC,OAAM;gBACN,MAAK;0BAEL,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;gCAA6C;gCACvB;8CACjC,8OAAC;oCAAK,WAAU;;wCACb,SAAS,SAAS;wCAAC;wCAAE,SAAS,QAAQ;;;;;;;gCAClC;;;;;;;sCAIT,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsD;;;;;;8CAGpE,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;;gDAAG;gDAAa,SAAS,UAAU;;;;;;;sDACpC,8OAAC;;gDAAG;gDAAqB,SAAS,iBAAiB;gDAAC;;;;;;;sDACpD,8OAAC;;gDAAG;gDAAQ,SAAS,KAAK;;;;;;;;;;;;;;;;;;;sCAI9B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,UAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,oBAAoB;oCACnC,WAAU;8CACX;;;;;;8CAGD,8OAAC,kIAAA,CAAA,UAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;uCAEe", "debugId": null}}, {"offset": {"line": 851, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/components/SearchAndFilters.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { MagnifyingGlassIcon, FunnelIcon, XMarkIcon } from '@heroicons/react/24/outline';\nimport { SearchFilters } from '@/types';\nimport Button from '@/components/ui/Button';\nimport Card from '@/components/ui/Card';\nimport { Listbox, Transition } from '@headlessui/react';\nimport { ChevronUpDownIcon, CheckIcon } from '@heroicons/react/20/solid';\n\ninterface SearchAndFiltersProps {\n  filters: SearchFilters;\n  onQueryChange: (query: string) => void;\n  onDepartmentsChange: (departments: string[]) => void;\n  onRatingRangeChange: (minRating: number, maxRating: number) => void;\n  onClearFilters: () => void;\n  availableDepartments: string[];\n}\n\nconst SearchAndFilters: React.FC<SearchAndFiltersProps> = ({\n  filters,\n  onQueryChange,\n  onDepartmentsChange,\n  onRatingRangeChange,\n  onClearFilters,\n  availableDepartments\n}) => {\n  const [showFilters, setShowFilters] = useState(false);\n\n  const hasActiveFilters = filters.departments.length > 0 || filters.minRating > 0 || filters.maxRating < 5;\n\n  return (\n    <div className=\"space-y-4\">\n      {/* Search Bar */}\n      <Card>\n        <div className=\"flex items-center space-x-4\">\n          <div className=\"flex-1 relative\">\n            <MagnifyingGlassIcon className=\"absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400\" />\n            <input\n              type=\"text\"\n              placeholder=\"Search employees by name, email, or department...\"\n              value={filters.query}\n              onChange={(e) => onQueryChange(e.target.value)}\n              className=\"w-full pl-12 pr-4 py-3 border-0 rounded-xl bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-700 text-slate-900 dark:text-white placeholder-slate-500 focus:ring-2 focus:ring-blue-500 focus:bg-white dark:focus:bg-slate-800 transition-all duration-200 shadow-inner\"\n            />\n          </div>\n\n          <Button\n            variant=\"outline\"\n            onClick={() => setShowFilters(!showFilters)}\n            className=\"relative bg-gradient-to-r from-blue-500 to-purple-600 text-white border-0 hover:from-blue-600 hover:to-purple-700 shadow-lg\"\n          >\n            <FunnelIcon className=\"h-5 w-5 mr-2\" />\n            Filters\n            {hasActiveFilters && (\n              <span className=\"absolute -top-1 -right-1 h-3 w-3 bg-gradient-to-r from-red-500 to-pink-500 rounded-full animate-pulse\"></span>\n            )}\n          </Button>\n        </div>\n      </Card>\n\n      {/* Filters Panel */}\n      <Transition\n        show={showFilters}\n        enter=\"transition ease-out duration-200\"\n        enterFrom=\"opacity-0 scale-95\"\n        enterTo=\"opacity-100 scale-100\"\n        leave=\"transition ease-in duration-150\"\n        leaveFrom=\"opacity-100 scale-100\"\n        leaveTo=\"opacity-0 scale-95\"\n      >\n        <Card>\n          <div className=\"space-y-6\">\n            <div className=\"flex items-center justify-between\">\n              <h3 className=\"text-lg font-medium text-secondary-900 dark:text-white\">\n                Filters\n              </h3>\n              {hasActiveFilters && (\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={onClearFilters}\n                >\n                  <XMarkIcon className=\"h-4 w-4 mr-1\" />\n                  Clear All\n                </Button>\n              )}\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {/* Department Filter */}\n              <div>\n                <label className=\"block text-sm font-medium text-secondary-700 dark:text-secondary-300 mb-2\">\n                  Departments\n                </label>\n                <Listbox value={filters.departments} onChange={onDepartmentsChange} multiple>\n                  <div className=\"relative\">\n                    <Listbox.Button className=\"relative w-full cursor-default rounded-lg bg-white dark:bg-secondary-800 py-2 pl-3 pr-10 text-left border border-secondary-300 dark:border-secondary-600 focus:outline-none focus:ring-2 focus:ring-primary-500\">\n                      <span className=\"block truncate\">\n                        {filters.departments.length === 0\n                          ? 'All Departments'\n                          : `${filters.departments.length} selected`}\n                      </span>\n                      <span className=\"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2\">\n                        <ChevronUpDownIcon className=\"h-5 w-5 text-secondary-400\" />\n                      </span>\n                    </Listbox.Button>\n                    <Transition\n                      leave=\"transition ease-in duration-100\"\n                      leaveFrom=\"opacity-100\"\n                      leaveTo=\"opacity-0\"\n                    >\n                      <Listbox.Options className=\"absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-secondary-800 py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\">\n                        {availableDepartments.map((department) => (\n                          <Listbox.Option\n                            key={department}\n                            value={department}\n                            className={({ active }) =>\n                              `relative cursor-default select-none py-2 pl-10 pr-4 ${\n                                active\n                                  ? 'bg-primary-100 text-primary-900 dark:bg-primary-900 dark:text-primary-100'\n                                  : 'text-secondary-900 dark:text-secondary-100'\n                              }`\n                            }\n                          >\n                            {({ selected }) => (\n                              <>\n                                <span className={`block truncate ${selected ? 'font-medium' : 'font-normal'}`}>\n                                  {department}\n                                </span>\n                                {selected && (\n                                  <span className=\"absolute inset-y-0 left-0 flex items-center pl-3 text-primary-600 dark:text-primary-400\">\n                                    <CheckIcon className=\"h-5 w-5\" />\n                                  </span>\n                                )}\n                              </>\n                            )}\n                          </Listbox.Option>\n                        ))}\n                      </Listbox.Options>\n                    </Transition>\n                  </div>\n                </Listbox>\n              </div>\n\n              {/* Rating Range Filter */}\n              <div>\n                <label className=\"block text-sm font-medium text-secondary-700 dark:text-secondary-300 mb-2\">\n                  Performance Rating Range\n                </label>\n                <div className=\"space-y-3\">\n                  <div>\n                    <label className=\"block text-xs text-secondary-500 dark:text-secondary-400 mb-1\">\n                      Minimum Rating: {filters.minRating}\n                    </label>\n                    <input\n                      type=\"range\"\n                      min=\"0\"\n                      max=\"5\"\n                      step=\"1\"\n                      value={filters.minRating}\n                      onChange={(e) => onRatingRangeChange(Number(e.target.value), filters.maxRating)}\n                      className=\"w-full h-2 bg-secondary-200 dark:bg-secondary-700 rounded-lg appearance-none cursor-pointer slider\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-xs text-secondary-500 dark:text-secondary-400 mb-1\">\n                      Maximum Rating: {filters.maxRating}\n                    </label>\n                    <input\n                      type=\"range\"\n                      min=\"0\"\n                      max=\"5\"\n                      step=\"1\"\n                      value={filters.maxRating}\n                      onChange={(e) => onRatingRangeChange(filters.minRating, Number(e.target.value))}\n                      className=\"w-full h-2 bg-secondary-200 dark:bg-secondary-700 rounded-lg appearance-none cursor-pointer slider\"\n                    />\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </Card>\n      </Transition>\n\n      {/* Active Filters Display */}\n      {hasActiveFilters && (\n        <div className=\"flex flex-wrap gap-2\">\n          {filters.departments.map((dept) => (\n            <span\n              key={dept}\n              className=\"inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200\"\n            >\n              {dept}\n              <button\n                onClick={() => onDepartmentsChange(filters.departments.filter(d => d !== dept))}\n                className=\"ml-2 hover:text-primary-600 dark:hover:text-primary-400\"\n              >\n                <XMarkIcon className=\"h-4 w-4\" />\n              </button>\n            </span>\n          ))}\n          \n          {(filters.minRating > 0 || filters.maxRating < 5) && (\n            <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm bg-secondary-100 text-secondary-800 dark:bg-secondary-800 dark:text-secondary-200\">\n              Rating: {filters.minRating}-{filters.maxRating}\n              <button\n                onClick={() => onRatingRangeChange(0, 5)}\n                className=\"ml-2 hover:text-secondary-600 dark:hover:text-secondary-400\"\n              >\n                <XMarkIcon className=\"h-4 w-4\" />\n              </button>\n            </span>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default SearchAndFilters;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AAAA;AACA;AAAA;AARA;;;;;;;;AAmBA,MAAM,mBAAoD,CAAC,EACzD,OAAO,EACP,aAAa,EACb,mBAAmB,EACnB,mBAAmB,EACnB,cAAc,EACd,oBAAoB,EACrB;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,mBAAmB,QAAQ,WAAW,CAAC,MAAM,GAAG,KAAK,QAAQ,SAAS,GAAG,KAAK,QAAQ,SAAS,GAAG;IAExG,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,gIAAA,CAAA,UAAI;0BACH,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,qOAAA,CAAA,sBAAmB;oCAAC,WAAU;;;;;;8CAC/B,8OAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO,QAAQ,KAAK;oCACpB,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,WAAU;;;;;;;;;;;;sCAId,8OAAC,kIAAA,CAAA,UAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,eAAe,CAAC;4BAC/B,WAAU;;8CAEV,8OAAC,mNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAAiB;gCAEtC,kCACC,8OAAC;oCAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAOxB,8OAAC,uLAAA,CAAA,aAAU;gBACT,MAAM;gBACN,OAAM;gBACN,WAAU;gBACV,SAAQ;gBACR,OAAM;gBACN,WAAU;gBACV,SAAQ;0BAER,cAAA,8OAAC,gIAAA,CAAA,UAAI;8BACH,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyD;;;;;;oCAGtE,kCACC,8OAAC,kIAAA,CAAA,UAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;;0DAET,8OAAC,iNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;0CAM5C,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA4E;;;;;;0DAG7F,8OAAC,iLAAA,CAAA,UAAO;gDAAC,OAAO,QAAQ,WAAW;gDAAE,UAAU;gDAAqB,QAAQ;0DAC1E,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iLAAA,CAAA,UAAO,CAAC,MAAM;4DAAC,WAAU;;8EACxB,8OAAC;oEAAK,WAAU;8EACb,QAAQ,WAAW,CAAC,MAAM,KAAK,IAC5B,oBACA,GAAG,QAAQ,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC;;;;;;8EAE9C,8OAAC;oEAAK,WAAU;8EACd,cAAA,8OAAC,+NAAA,CAAA,oBAAiB;wEAAC,WAAU;;;;;;;;;;;;;;;;;sEAGjC,8OAAC,uLAAA,CAAA,aAAU;4DACT,OAAM;4DACN,WAAU;4DACV,SAAQ;sEAER,cAAA,8OAAC,iLAAA,CAAA,UAAO,CAAC,OAAO;gEAAC,WAAU;0EACxB,qBAAqB,GAAG,CAAC,CAAC,2BACzB,8OAAC,iLAAA,CAAA,UAAO,CAAC,MAAM;wEAEb,OAAO;wEACP,WAAW,CAAC,EAAE,MAAM,EAAE,GACpB,CAAC,oDAAoD,EACnD,SACI,8EACA,8CACJ;kFAGH,CAAC,EAAE,QAAQ,EAAE,iBACZ;;kGACE,8OAAC;wFAAK,WAAW,CAAC,eAAe,EAAE,WAAW,gBAAgB,eAAe;kGAC1E;;;;;;oFAEF,0BACC,8OAAC;wFAAK,WAAU;kGACd,cAAA,8OAAC,+MAAA,CAAA,YAAS;4FAAC,WAAU;;;;;;;;;;;;;uEAjBxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDA+BnB,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA4E;;;;;;0DAG7F,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;;oEAAgE;oEAC9D,QAAQ,SAAS;;;;;;;0EAEpC,8OAAC;gEACC,MAAK;gEACL,KAAI;gEACJ,KAAI;gEACJ,MAAK;gEACL,OAAO,QAAQ,SAAS;gEACxB,UAAU,CAAC,IAAM,oBAAoB,OAAO,EAAE,MAAM,CAAC,KAAK,GAAG,QAAQ,SAAS;gEAC9E,WAAU;;;;;;;;;;;;kEAGd,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;;oEAAgE;oEAC9D,QAAQ,SAAS;;;;;;;0EAEpC,8OAAC;gEACC,MAAK;gEACL,KAAI;gEACJ,KAAI;gEACJ,MAAK;gEACL,OAAO,QAAQ,SAAS;gEACxB,UAAU,CAAC,IAAM,oBAAoB,QAAQ,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gEAC7E,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAWzB,kCACC,8OAAC;gBAAI,WAAU;;oBACZ,QAAQ,WAAW,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;4BAEC,WAAU;;gCAET;8CACD,8OAAC;oCACC,SAAS,IAAM,oBAAoB,QAAQ,WAAW,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM;oCACzE,WAAU;8CAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;2BARlB;;;;;oBAaR,CAAC,QAAQ,SAAS,GAAG,KAAK,QAAQ,SAAS,GAAG,CAAC,mBAC9C,8OAAC;wBAAK,WAAU;;4BAA4I;4BACjJ,QAAQ,SAAS;4BAAC;4BAAE,QAAQ,SAAS;0CAC9C,8OAAC;gCACC,SAAS,IAAM,oBAAoB,GAAG;gCACtC,WAAU;0CAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrC;uCAEe", "debugId": null}}, {"offset": {"line": 1298, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Employee, User } from '@/types';\nimport { transformUserToEmployee } from '@/lib/utils';\nimport { useSearch } from '@/hooks/useSearch';\nimport { useBookmarks } from '@/hooks/useBookmarks';\nimport UserCard from '@/components/UserCard';\nimport SearchAndFilters from '@/components/SearchAndFilters';\nimport Card from '@/components/ui/Card';\nimport SkeletonCard from '@/components/ui/SkeletonCard';\n\nexport default function Dashboard() {\n  const [employees, setEmployees] = useState<Employee[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  const { bookmarks, addBookmark, removeBookmark, isBookmarked } = useBookmarks();\n  const {\n    filters,\n    filteredEmployees,\n    updateQuery,\n    updateDepartments,\n    updateRatingRange,\n    clearFilters,\n    availableDepartments\n  } = useSearch(employees);\n\n  useEffect(() => {\n    const fetchEmployees = async () => {\n      try {\n        setLoading(true);\n        const response = await fetch('https://dummyjson.com/users?limit=20');\n\n        if (!response.ok) {\n          throw new Error('Failed to fetch employees');\n        }\n\n        const data = await response.json();\n        const transformedEmployees = data.users.map((user: User) =>\n          transformUserToEmployee(user)\n        );\n\n        setEmployees(transformedEmployees);\n      } catch (err) {\n        setError(err instanceof Error ? err.message : 'An error occurred');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchEmployees();\n  }, []);\n\n  const handleBookmark = (id: number) => {\n    if (isBookmarked(id)) {\n      removeBookmark(id);\n    } else {\n      addBookmark(id);\n    }\n  };\n\n  const handlePromote = (id: number) => {\n    // In a real app, this would make an API call\n    console.log(`Promoting employee with ID: ${id}`);\n    // You could show a success toast here\n  };\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-8 bg-secondary-200 dark:bg-secondary-700 rounded w-1/4 mb-6\"></div>\n          <div className=\"h-20 bg-secondary-200 dark:bg-secondary-700 rounded mb-6\"></div>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n            {Array.from({ length: 8 }).map((_, index) => (\n              <div key={index} className=\"h-64 bg-secondary-200 dark:bg-secondary-700 rounded-lg\"></div>\n            ))}\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <Card>\n        <div className=\"text-center py-12\">\n          <div className=\"text-red-500 text-lg font-medium mb-2\">Error Loading Employees</div>\n          <p className=\"text-secondary-600 dark:text-secondary-400 mb-4\">{error}</p>\n          <button\n            onClick={() => window.location.reload()}\n            className=\"px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\"\n          >\n            Try Again\n          </button>\n        </div>\n      </Card>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"text-center mb-8\">\n        <h1 className=\"text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent mb-4\">\n          Employee Dashboard\n        </h1>\n        <p className=\"text-lg text-slate-600 dark:text-slate-400 max-w-2xl mx-auto\">\n          Manage and view employee performance across your organization with our modern HR analytics platform\n        </p>\n      </div>\n\n      {/* Search and Filters */}\n      <SearchAndFilters\n        filters={filters}\n        onQueryChange={updateQuery}\n        onDepartmentsChange={updateDepartments}\n        onRatingRangeChange={updateRatingRange}\n        onClearFilters={clearFilters}\n        availableDepartments={availableDepartments}\n      />\n\n      {/* Results Summary */}\n      <Card padding=\"sm\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-3 h-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full\"></div>\n              <p className=\"text-sm font-medium text-slate-700 dark:text-slate-300\">\n                Showing {filteredEmployees.length} of {employees.length} employees\n              </p>\n            </div>\n\n            {filteredEmployees.length > 0 && (\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-3 h-3 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full\"></div>\n                <div className=\"text-sm font-medium text-slate-600 dark:text-slate-400\">\n                  {bookmarks.length} bookmarked\n                </div>\n              </div>\n            )}\n          </div>\n\n          <div className=\"text-xs text-slate-500 dark:text-slate-400 bg-slate-100 dark:bg-slate-800 px-3 py-1 rounded-full\">\n            Live Data\n          </div>\n        </div>\n      </Card>\n\n      {/* Employee Grid */}\n      {filteredEmployees.length === 0 ? (\n        <Card>\n          <div className=\"text-center py-12\">\n            <div className=\"text-secondary-500 text-lg font-medium mb-2\">No employees found</div>\n            <p className=\"text-secondary-400 mb-4\">\n              Try adjusting your search criteria or filters\n            </p>\n            <button\n              onClick={clearFilters}\n              className=\"px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\"\n            >\n              Clear Filters\n            </button>\n          </div>\n        </Card>\n      ) : (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n          {filteredEmployees.map((employee) => (\n            <UserCard\n              key={employee.id}\n              employee={employee}\n              onView={(id) => console.log(`Viewing employee ${id}`)}\n              onBookmark={handleBookmark}\n              onPromote={handlePromote}\n              isBookmarked={isBookmarked(employee.id)}\n            />\n          ))}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;AAYe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,cAAc,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAC5E,MAAM,EACJ,OAAO,EACP,iBAAiB,EACjB,WAAW,EACX,iBAAiB,EACjB,iBAAiB,EACjB,YAAY,EACZ,oBAAoB,EACrB,GAAG,CAAA,GAAA,yHAAA,CAAA,YAAS,AAAD,EAAE;IAEd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB;YACrB,IAAI;gBACF,WAAW;gBACX,MAAM,WAAW,MAAM,MAAM;gBAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,uBAAuB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,OAC3C,CAAA,GAAA,mHAAA,CAAA,0BAAuB,AAAD,EAAE;gBAG1B,aAAa;YACf,EAAE,OAAO,KAAK;gBACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAChD,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAC;QACtB,IAAI,aAAa,KAAK;YACpB,eAAe;QACjB,OAAO;YACL,YAAY;QACd;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,6CAA6C;QAC7C,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,IAAI;IAC/C,sCAAsC;IACxC;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC;gCAAgB,WAAU;+BAAjB;;;;;;;;;;;;;;;;;;;;;IAMtB;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC,gIAAA,CAAA,UAAI;sBACH,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAwC;;;;;;kCACvD,8OAAC;wBAAE,WAAU;kCAAmD;;;;;;kCAChE,8OAAC;wBACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;wBACrC,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAoH;;;;;;kCAGlI,8OAAC;wBAAE,WAAU;kCAA+D;;;;;;;;;;;;0BAM9E,8OAAC,sIAAA,CAAA,UAAgB;gBACf,SAAS;gBACT,eAAe;gBACf,qBAAqB;gBACrB,qBAAqB;gBACrB,gBAAgB;gBAChB,sBAAsB;;;;;;0BAIxB,8OAAC,gIAAA,CAAA,UAAI;gBAAC,SAAQ;0BACZ,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAE,WAAU;;gDAAyD;gDAC3D,kBAAkB,MAAM;gDAAC;gDAAK,UAAU,MAAM;gDAAC;;;;;;;;;;;;;gCAI3D,kBAAkB,MAAM,GAAG,mBAC1B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;gDACZ,UAAU,MAAM;gDAAC;;;;;;;;;;;;;;;;;;;sCAM1B,8OAAC;4BAAI,WAAU;sCAAmG;;;;;;;;;;;;;;;;;YAOrH,kBAAkB,MAAM,KAAK,kBAC5B,8OAAC,gIAAA,CAAA,UAAI;0BACH,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAA8C;;;;;;sCAC7D,8OAAC;4BAAE,WAAU;sCAA0B;;;;;;sCAGvC,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;qCAML,8OAAC;gBAAI,WAAU;0BACZ,kBAAkB,GAAG,CAAC,CAAC,yBACtB,8OAAC,8HAAA,CAAA,UAAQ;wBAEP,UAAU;wBACV,QAAQ,CAAC,KAAO,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,IAAI;wBACpD,YAAY;wBACZ,WAAW;wBACX,cAAc,aAAa,SAAS,EAAE;uBALjC,SAAS,EAAE;;;;;;;;;;;;;;;;AAY9B", "debugId": null}}]}