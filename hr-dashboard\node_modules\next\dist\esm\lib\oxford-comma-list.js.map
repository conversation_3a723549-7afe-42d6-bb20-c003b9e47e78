{"version": 3, "sources": ["../../src/lib/oxford-comma-list.ts"], "sourcesContent": ["export function getOxfordCommaList(items: string[]): string {\n  return items\n    .map(\n      (v, index, { length }) =>\n        (index > 0\n          ? index === length - 1\n            ? length > 2\n              ? ', and '\n              : ' and '\n            : ', '\n          : '') + v\n    )\n    .join('')\n}\n"], "names": ["getOxfordCommaList", "items", "map", "v", "index", "length", "join"], "mappings": "AAAA,OAAO,SAASA,mBAAmBC,KAAe;IAChD,OAAOA,MACJC,GAAG,CACF,CAACC,GAAGC,OAAO,EAAEC,MAAM,EAAE,GACnB,AAACD,CAAAA,QAAQ,IACLA,UAAUC,SAAS,IACjBA,SAAS,IACP,WACA,UACF,OACF,EAAC,IAAKF,GAEbG,IAAI,CAAC;AACV", "ignoreList": [0]}