{"version": 3, "sources": ["../../../src/server/request/pathname.ts"], "sourcesContent": ["import type { WorkStore } from '../app-render/work-async-storage.external'\n\nimport {\n  postponeWithTracking,\n  type DynamicTrackingState,\n} from '../app-render/dynamic-rendering'\n\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStore,\n} from '../app-render/work-unit-async-storage.external'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nexport function createServerPathnameForMetadata(\n  underlyingPathname: string,\n  workStore: WorkStore\n): Promise<string> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy': {\n        return createPrerenderPathname(\n          underlyingPathname,\n          workStore,\n          workUnitStore\n        )\n      }\n      default:\n      // fallthrough\n    }\n  }\n  return createRenderPathname(underlyingPathname)\n}\n\nfunction createPrerenderPathname(\n  underlyingPathname: string,\n  workStore: WorkStore,\n  prerenderStore: PrerenderStore\n): Promise<string> {\n  const fallbackParams = workStore.fallbackRouteParams\n  if (fallbackParams && fallbackParams.size > 0) {\n    switch (prerenderStore.type) {\n      case 'prerender':\n        return makeHangingPromise<string>(\n          prerenderStore.renderSignal,\n          '`pathname`'\n        )\n      case 'prerender-client':\n        throw new InvariantError(\n          'createPrerenderPathname was called inside a client component scope.'\n        )\n      case 'prerender-ppr':\n        return makeErroringPathname(workStore, prerenderStore.dynamicTracking)\n        break\n      default:\n        return makeErroringPathname(workStore, null)\n    }\n  }\n\n  // We don't have any fallback params so we have an entirely static safe params object\n  return Promise.resolve(underlyingPathname)\n}\n\nfunction makeErroringPathname<T>(\n  workStore: WorkStore,\n  dynamicTracking: null | DynamicTrackingState\n): Promise<T> {\n  let reject: null | ((reason: unknown) => void) = null\n  const promise = new Promise<T>((_, re) => {\n    reject = re\n  })\n\n  const originalThen = promise.then.bind(promise)\n\n  // We instrument .then so that we can generate a tracking event only if you actually\n  // await this promise, not just that it is created.\n  promise.then = (onfulfilled, onrejected) => {\n    if (reject) {\n      try {\n        postponeWithTracking(\n          workStore.route,\n          'metadata relative url resolving',\n          dynamicTracking\n        )\n      } catch (error) {\n        reject(error)\n        reject = null\n      }\n    }\n    return originalThen(onfulfilled, onrejected)\n  }\n\n  // We wrap in a noop proxy to trick the runtime into thinking it\n  // isn't a native promise (it's not really). This is so that awaiting\n  // the promise will call the `then` property triggering the lazy postpone\n  return new Proxy(promise, {})\n}\n\nfunction createRenderPathname(underlyingPathname: string): Promise<string> {\n  return Promise.resolve(underlyingPathname)\n}\n"], "names": ["postponeWithTracking", "workUnitAsyncStorage", "makeHangingPromise", "InvariantError", "createServerPathnameForMetadata", "underlyingPathname", "workStore", "workUnitStore", "getStore", "type", "createPrerenderPathname", "createRenderPathname", "prerenderStore", "fallbackP<PERSON><PERSON>", "fallbackRouteParams", "size", "renderSignal", "makeErroringPathname", "dynamicTracking", "Promise", "resolve", "reject", "promise", "_", "re", "originalThen", "then", "bind", "onfulfilled", "onrejected", "route", "error", "Proxy"], "mappings": "AAEA,SACEA,oBAAoB,QAEf,kCAAiC;AAExC,SACEC,oBAAoB,QAEf,iDAAgD;AACvD,SAASC,kBAAkB,QAAQ,6BAA4B;AAC/D,SAASC,cAAc,QAAQ,mCAAkC;AAEjE,OAAO,SAASC,gCACdC,kBAA0B,EAC1BC,SAAoB;IAEpB,MAAMC,gBAAgBN,qBAAqBO,QAAQ;IACnD,IAAID,eAAe;QACjB,OAAQA,cAAcE,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBAAoB;oBACvB,OAAOC,wBACLL,oBACAC,WACAC;gBAEJ;YACA;QAEF;IACF;IACA,OAAOI,qBAAqBN;AAC9B;AAEA,SAASK,wBACPL,kBAA0B,EAC1BC,SAAoB,EACpBM,cAA8B;IAE9B,MAAMC,iBAAiBP,UAAUQ,mBAAmB;IACpD,IAAID,kBAAkBA,eAAeE,IAAI,GAAG,GAAG;QAC7C,OAAQH,eAAeH,IAAI;YACzB,KAAK;gBACH,OAAOP,mBACLU,eAAeI,YAAY,EAC3B;YAEJ,KAAK;gBACH,MAAM,qBAEL,CAFK,IAAIb,eACR,wEADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,OAAOc,qBAAqBX,WAAWM,eAAeM,eAAe;gBACrE;YACF;gBACE,OAAOD,qBAAqBX,WAAW;QAC3C;IACF;IAEA,qFAAqF;IACrF,OAAOa,QAAQC,OAAO,CAACf;AACzB;AAEA,SAASY,qBACPX,SAAoB,EACpBY,eAA4C;IAE5C,IAAIG,SAA6C;IACjD,MAAMC,UAAU,IAAIH,QAAW,CAACI,GAAGC;QACjCH,SAASG;IACX;IAEA,MAAMC,eAAeH,QAAQI,IAAI,CAACC,IAAI,CAACL;IAEvC,oFAAoF;IACpF,mDAAmD;IACnDA,QAAQI,IAAI,GAAG,CAACE,aAAaC;QAC3B,IAAIR,QAAQ;YACV,IAAI;gBACFrB,qBACEM,UAAUwB,KAAK,EACf,mCACAZ;YAEJ,EAAE,OAAOa,OAAO;gBACdV,OAAOU;gBACPV,SAAS;YACX;QACF;QACA,OAAOI,aAAaG,aAAaC;IACnC;IAEA,gEAAgE;IAChE,qEAAqE;IACrE,yEAAyE;IACzE,OAAO,IAAIG,MAAMV,SAAS,CAAC;AAC7B;AAEA,SAASX,qBAAqBN,kBAA0B;IACtD,OAAOc,QAAQC,OAAO,CAACf;AACzB", "ignoreList": [0]}