{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/contexts/ThemeContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { Theme, ThemeContextType } from '@/types';\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\ninterface ThemeProviderProps {\n  children: React.ReactNode;\n}\n\nexport const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {\n  const [theme, setTheme] = useState<Theme>('light');\n\n  useEffect(() => {\n    // Check for saved theme preference or default to 'light'\n    const savedTheme = localStorage.getItem('theme') as Theme;\n    if (savedTheme) {\n      setTheme(savedTheme);\n    } else {\n      // Check system preference\n      const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n      setTheme(systemPrefersDark ? 'dark' : 'light');\n    }\n  }, []);\n\n  useEffect(() => {\n    // Apply theme to document\n    const root = document.documentElement;\n    if (theme === 'dark') {\n      root.classList.add('dark');\n    } else {\n      root.classList.remove('dark');\n    }\n    \n    // Save theme preference\n    localStorage.setItem('theme', theme);\n  }, [theme]);\n\n  const toggleTheme = () => {\n    setTheme(prev => prev === 'light' ? 'dark' : 'light');\n  };\n\n  const value: ThemeContextType = {\n    theme,\n    toggleTheme\n  };\n\n  return (\n    <ThemeContext.Provider value={value}>\n      {children}\n    </ThemeContext.Provider>\n  );\n};\n\nexport const useThemeContext = () => {\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('useThemeContext must be used within a ThemeProvider');\n  }\n  return context;\n};\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAKA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAM1D,MAAM,gBAA8C,CAAC,EAAE,QAAQ,EAAE;IACtE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS;IAE1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,yDAAyD;QACzD,MAAM,aAAa,aAAa,OAAO,CAAC;QACxC,IAAI,YAAY;YACd,SAAS;QACX,OAAO;YACL,0BAA0B;YAC1B,MAAM,oBAAoB,OAAO,UAAU,CAAC,gCAAgC,OAAO;YACnF,SAAS,oBAAoB,SAAS;QACxC;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,0BAA0B;QAC1B,MAAM,OAAO,SAAS,eAAe;QACrC,IAAI,UAAU,QAAQ;YACpB,KAAK,SAAS,CAAC,GAAG,CAAC;QACrB,OAAO;YACL,KAAK,SAAS,CAAC,MAAM,CAAC;QACxB;QAEA,wBAAwB;QACxB,aAAa,OAAO,CAAC,SAAS;IAChC,GAAG;QAAC;KAAM;IAEV,MAAM,cAAc;QAClB,SAAS,CAAA,OAAQ,SAAS,UAAU,SAAS;IAC/C;IAEA,MAAM,QAA0B;QAC9B;QACA;IACF;IAEA,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;kBAC3B;;;;;;AAGP;AAEO,MAAM,kBAAkB;IAC7B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/contexts/BookmarkContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { BookmarkContextType } from '@/types';\n\nconst BookmarkContext = createContext<BookmarkContextType | undefined>(undefined);\n\ninterface BookmarkProviderProps {\n  children: React.ReactNode;\n}\n\nexport const BookmarkProvider: React.FC<BookmarkProviderProps> = ({ children }) => {\n  const [bookmarks, setBookmarks] = useState<number[]>([]);\n\n  useEffect(() => {\n    // Load bookmarks from localStorage on mount\n    const savedBookmarks = localStorage.getItem('hr-bookmarks');\n    if (savedBookmarks) {\n      try {\n        const parsed = JSON.parse(savedBookmarks);\n        if (Array.isArray(parsed)) {\n          setBookmarks(parsed);\n        }\n      } catch (error) {\n        console.error('Failed to parse saved bookmarks:', error);\n      }\n    }\n  }, []);\n\n  useEffect(() => {\n    // Save bookmarks to localStorage whenever they change\n    localStorage.setItem('hr-bookmarks', JSON.stringify(bookmarks));\n  }, [bookmarks]);\n\n  const addBookmark = (id: number) => {\n    setBookmarks(prev => {\n      if (!prev.includes(id)) {\n        return [...prev, id];\n      }\n      return prev;\n    });\n  };\n\n  const removeBookmark = (id: number) => {\n    setBookmarks(prev => prev.filter(bookmarkId => bookmarkId !== id));\n  };\n\n  const isBookmarked = (id: number) => {\n    return bookmarks.includes(id);\n  };\n\n  const value: BookmarkContextType = {\n    bookmarks,\n    addBookmark,\n    removeBookmark,\n    isBookmarked\n  };\n\n  return (\n    <BookmarkContext.Provider value={value}>\n      {children}\n    </BookmarkContext.Provider>\n  );\n};\n\nexport const useBookmarkContext = () => {\n  const context = useContext(BookmarkContext);\n  if (context === undefined) {\n    throw new Error('useBookmarkContext must be used within a BookmarkProvider');\n  }\n  return context;\n};\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAKA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAmC;AAMhE,MAAM,mBAAoD,CAAC,EAAE,QAAQ,EAAE;IAC5E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEvD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,4CAA4C;QAC5C,MAAM,iBAAiB,aAAa,OAAO,CAAC;QAC5C,IAAI,gBAAgB;YAClB,IAAI;gBACF,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,IAAI,MAAM,OAAO,CAAC,SAAS;oBACzB,aAAa;gBACf;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;YACpD;QACF;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,sDAAsD;QACtD,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;IACtD,GAAG;QAAC;KAAU;IAEd,MAAM,cAAc,CAAC;QACnB,aAAa,CAAA;YACX,IAAI,CAAC,KAAK,QAAQ,CAAC,KAAK;gBACtB,OAAO;uBAAI;oBAAM;iBAAG;YACtB;YACA,OAAO;QACT;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,aAAa,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,aAAc,eAAe;IAChE;IAEA,MAAM,eAAe,CAAC;QACpB,OAAO,UAAU,QAAQ,CAAC;IAC5B;IAEA,MAAM,QAA6B;QACjC;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;kBAC9B;;;;;;AAGP;AAEO,MAAM,qBAAqB;IAChC,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\nimport { Employee, User, Project, Feedback, PerformanceRecord } from '@/types';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// Department options for random assignment\nconst DEPARTMENTS = [\n  'Engineering',\n  'Human Resources',\n  'Sales',\n  'Marketing',\n  'Finance',\n  'Operations',\n  'Customer Support',\n  'Product Management',\n  'Design',\n  'Legal'\n];\n\n// Generate random department\nexport function getRandomDepartment(): string {\n  return DEPARTMENTS[Math.floor(Math.random() * DEPARTMENTS.length)];\n}\n\n// Generate random performance rating (1-5)\nexport function getRandomRating(): number {\n  return Math.floor(Math.random() * 5) + 1;\n}\n\n// Generate random bio\nexport function generateBio(firstName: string, lastName: string, department: string): string {\n  const bios = [\n    `${firstName} ${lastName} is a dedicated professional in the ${department} department with a passion for excellence and innovation.`,\n    `With years of experience in ${department}, ${firstName} brings valuable expertise and leadership to our team.`,\n    `${firstName} is known for their collaborative approach and commitment to delivering high-quality results in ${department}.`,\n    `A results-driven professional, ${firstName} ${lastName} consistently exceeds expectations in their role within ${department}.`,\n    `${firstName} combines technical expertise with strong communication skills, making them a valuable asset to the ${department} team.`\n  ];\n  return bios[Math.floor(Math.random() * bios.length)];\n}\n\n// Generate mock projects\nexport function generateProjects(): Project[] {\n  const projectNames = [\n    'Customer Portal Redesign',\n    'Mobile App Development',\n    'Data Analytics Platform',\n    'Security Audit',\n    'Performance Optimization',\n    'User Experience Research',\n    'API Integration',\n    'Cloud Migration',\n    'Automation Framework',\n    'Quality Assurance'\n  ];\n\n  const roles = ['Lead Developer', 'Project Manager', 'Designer', 'Analyst', 'Consultant', 'Coordinator'];\n  const statuses: ('active' | 'completed' | 'on-hold')[] = ['active', 'completed', 'on-hold'];\n\n  const numProjects = Math.floor(Math.random() * 4) + 1; // 1-4 projects\n  const projects: Project[] = [];\n\n  for (let i = 0; i < numProjects; i++) {\n    projects.push({\n      id: `proj-${Math.random().toString(36).substr(2, 9)}`,\n      name: projectNames[Math.floor(Math.random() * projectNames.length)],\n      role: roles[Math.floor(Math.random() * roles.length)],\n      status: statuses[Math.floor(Math.random() * statuses.length)],\n      startDate: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],\n      endDate: Math.random() > 0.5 ? new Date(Date.now() + Math.random() * 180 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] : undefined\n    });\n  }\n\n  return projects;\n}\n\n// Generate mock feedback\nexport function generateFeedback(): Feedback[] {\n  const feedbackComments = [\n    'Excellent work on the recent project. Great attention to detail.',\n    'Shows strong leadership skills and helps team members grow.',\n    'Consistently delivers high-quality work on time.',\n    'Great communication skills and collaborative approach.',\n    'Innovative thinking and problem-solving abilities.',\n    'Reliable team player who goes above and beyond.',\n    'Strong technical skills and willingness to learn.',\n    'Positive attitude and great work ethic.'\n  ];\n\n  const names = ['John Smith', 'Sarah Johnson', 'Mike Davis', 'Emily Brown', 'David Wilson', 'Lisa Garcia'];\n\n  const numFeedback = Math.floor(Math.random() * 3) + 1; // 1-3 feedback items\n  const feedback: Feedback[] = [];\n\n  for (let i = 0; i < numFeedback; i++) {\n    feedback.push({\n      id: `feedback-${Math.random().toString(36).substr(2, 9)}`,\n      from: names[Math.floor(Math.random() * names.length)],\n      comment: feedbackComments[Math.floor(Math.random() * feedbackComments.length)],\n      rating: Math.floor(Math.random() * 5) + 1,\n      date: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]\n    });\n  }\n\n  return feedback;\n}\n\n// Generate performance history\nexport function generatePerformanceHistory(): PerformanceRecord[] {\n  const quarters = ['Q1 2024', 'Q2 2024', 'Q3 2024', 'Q4 2023'];\n  const goals = [\n    'Improve code quality',\n    'Enhance team collaboration',\n    'Complete certification',\n    'Mentor junior developers',\n    'Optimize system performance',\n    'Implement new features'\n  ];\n  const achievements = [\n    'Reduced bug count by 30%',\n    'Led successful project delivery',\n    'Improved team productivity',\n    'Implemented new process',\n    'Received client commendation',\n    'Completed training program'\n  ];\n\n  return quarters.map(quarter => ({\n    quarter,\n    rating: Math.floor(Math.random() * 5) + 1,\n    goals: goals.slice(0, Math.floor(Math.random() * 3) + 1),\n    achievements: achievements.slice(0, Math.floor(Math.random() * 3) + 1)\n  }));\n}\n\n// Transform User to Employee with additional HR data\nexport function transformUserToEmployee(user: User): Employee {\n  const department = getRandomDepartment();\n  const performanceRating = getRandomRating();\n\n  return {\n    ...user,\n    department,\n    performanceRating,\n    bio: generateBio(user.firstName, user.lastName, department),\n    projects: generateProjects(),\n    feedback: generateFeedback(),\n    performanceHistory: generatePerformanceHistory()\n  };\n}\n\n// Get performance badge variant based on rating\nexport function getPerformanceBadgeVariant(rating: number): 'primary' | 'secondary' | 'success' | 'warning' | 'danger' {\n  if (rating >= 5) return 'success';\n  if (rating >= 4) return 'primary';\n  if (rating >= 3) return 'warning';\n  if (rating >= 2) return 'secondary';\n  return 'danger';\n}\n\n// Get performance label based on rating\nexport function getPerformanceLabel(rating: number): string {\n  if (rating >= 5) return 'Outstanding';\n  if (rating >= 4) return 'Excellent';\n  if (rating >= 3) return 'Good';\n  if (rating >= 2) return 'Fair';\n  return 'Needs Improvement';\n}\n\n// Format date for display\nexport function formatDate(dateString: string): string {\n  return new Date(dateString).toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric'\n  });\n}\n\n// Debounce function for search\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n// Calculate department statistics\nexport function calculateDepartmentStats(employees: Employee[]) {\n  const departmentMap = new Map<string, { totalRating: number; count: number }>();\n\n  employees.forEach(employee => {\n    const current = departmentMap.get(employee.department) || { totalRating: 0, count: 0 };\n    departmentMap.set(employee.department, {\n      totalRating: current.totalRating + employee.performanceRating,\n      count: current.count + 1\n    });\n  });\n\n  return Array.from(departmentMap.entries()).map(([department, stats]) => ({\n    department,\n    averageRating: stats.totalRating / stats.count,\n    employeeCount: stats.count\n  }));\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;AAGO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEA,2CAA2C;AAC3C,MAAM,cAAc;IAClB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAGM,SAAS;IACd,OAAO,WAAW,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,YAAY,MAAM,EAAE;AACpE;AAGO,SAAS;IACd,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK;AACzC;AAGO,SAAS,YAAY,SAAiB,EAAE,QAAgB,EAAE,UAAkB;IACjF,MAAM,OAAO;QACX,GAAG,UAAU,CAAC,EAAE,SAAS,oCAAoC,EAAE,WAAW,yDAAyD,CAAC;QACpI,CAAC,4BAA4B,EAAE,WAAW,EAAE,EAAE,UAAU,sDAAsD,CAAC;QAC/G,GAAG,UAAU,gGAAgG,EAAE,WAAW,CAAC,CAAC;QAC5H,CAAC,+BAA+B,EAAE,UAAU,CAAC,EAAE,SAAS,wDAAwD,EAAE,WAAW,CAAC,CAAC;QAC/H,GAAG,UAAU,oGAAoG,EAAE,WAAW,MAAM,CAAC;KACtI;IACD,OAAO,IAAI,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK,MAAM,EAAE;AACtD;AAGO,SAAS;IACd,MAAM,eAAe;QACnB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,QAAQ;QAAC;QAAkB;QAAmB;QAAY;QAAW;QAAc;KAAc;IACvG,MAAM,WAAmD;QAAC;QAAU;QAAa;KAAU;IAE3F,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK,GAAG,eAAe;IACtE,MAAM,WAAsB,EAAE;IAE9B,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,IAAK;QACpC,SAAS,IAAI,CAAC;YACZ,IAAI,CAAC,KAAK,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;YACrD,MAAM,YAAY,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,aAAa,MAAM,EAAE;YACnE,MAAM,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM,EAAE;YACrD,QAAQ,QAAQ,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS,MAAM,EAAE;YAC7D,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,MAAM,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YACvG,SAAS,KAAK,MAAM,KAAK,MAAM,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,MAAM,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;QAChI;IACF;IAEA,OAAO;AACT;AAGO,SAAS;IACd,MAAM,mBAAmB;QACvB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,QAAQ;QAAC;QAAc;QAAiB;QAAc;QAAe;QAAgB;KAAc;IAEzG,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK,GAAG,qBAAqB;IAC5E,MAAM,WAAuB,EAAE;IAE/B,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,IAAK;QACpC,SAAS,IAAI,CAAC;YACZ,IAAI,CAAC,SAAS,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;YACzD,MAAM,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM,EAAE;YACrD,SAAS,gBAAgB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,iBAAiB,MAAM,EAAE;YAC9E,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK;YACxC,MAAM,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACnG;IACF;IAEA,OAAO;AACT;AAGO,SAAS;IACd,MAAM,WAAW;QAAC;QAAW;QAAW;QAAW;KAAU;IAC7D,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,eAAe;QACnB;QACA;QACA;QACA;QACA;QACA;KACD;IAED,OAAO,SAAS,GAAG,CAAC,CAAA,UAAW,CAAC;YAC9B;YACA,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK;YACxC,OAAO,MAAM,KAAK,CAAC,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK;YACtD,cAAc,aAAa,KAAK,CAAC,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK;QACtE,CAAC;AACH;AAGO,SAAS,wBAAwB,IAAU;IAChD,MAAM,aAAa;IACnB,MAAM,oBAAoB;IAE1B,OAAO;QACL,GAAG,IAAI;QACP;QACA;QACA,KAAK,YAAY,KAAK,SAAS,EAAE,KAAK,QAAQ,EAAE;QAChD,UAAU;QACV,UAAU;QACV,oBAAoB;IACtB;AACF;AAGO,SAAS,2BAA2B,MAAc;IACvD,IAAI,UAAU,GAAG,OAAO;IACxB,IAAI,UAAU,GAAG,OAAO;IACxB,IAAI,UAAU,GAAG,OAAO;IACxB,IAAI,UAAU,GAAG,OAAO;IACxB,OAAO;AACT;AAGO,SAAS,oBAAoB,MAAc;IAChD,IAAI,UAAU,GAAG,OAAO;IACxB,IAAI,UAAU,GAAG,OAAO;IACxB,IAAI,UAAU,GAAG,OAAO;IACxB,IAAI,UAAU,GAAG,OAAO;IACxB,OAAO;AACT;AAGO,SAAS,WAAW,UAAkB;IAC3C,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;QACtD,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS,yBAAyB,SAAqB;IAC5D,MAAM,gBAAgB,IAAI;IAE1B,UAAU,OAAO,CAAC,CAAA;QAChB,MAAM,UAAU,cAAc,GAAG,CAAC,SAAS,UAAU,KAAK;YAAE,aAAa;YAAG,OAAO;QAAE;QACrF,cAAc,GAAG,CAAC,SAAS,UAAU,EAAE;YACrC,aAAa,QAAQ,WAAW,GAAG,SAAS,iBAAiB;YAC7D,OAAO,QAAQ,KAAK,GAAG;QACzB;IACF;IAEA,OAAO,MAAM,IAAI,CAAC,cAAc,OAAO,IAAI,GAAG,CAAC,CAAC,CAAC,YAAY,MAAM,GAAK,CAAC;YACvE;YACA,eAAe,MAAM,WAAW,GAAG,MAAM,KAAK;YAC9C,eAAe,MAAM,KAAK;QAC5B,CAAC;AACH", "debugId": null}}, {"offset": {"line": 391, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/hooks/useTheme.ts"], "sourcesContent": ["import { useThemeContext } from '@/contexts/ThemeContext';\n\nexport const useTheme = () => {\n  return useThemeContext();\n};\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,WAAW;IACtB,OAAO,CAAA,GAAA,gIAAA,CAAA,kBAAe,AAAD;AACvB", "debugId": null}}, {"offset": {"line": 403, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { cn } from '@/lib/utils';\nimport {\n  HomeIcon,\n  UserGroupIcon,\n  BookmarkIcon,\n  ChartBarIcon,\n  SunIcon,\n  MoonIcon\n} from '@heroicons/react/24/outline';\nimport { useTheme } from '@/hooks/useTheme';\n\nconst navigation = [\n  { name: 'Dashboard', href: '/', icon: HomeIcon },\n  { name: 'Employees', href: '/', icon: UserGroupIcon },\n  { name: 'Bookmarks', href: '/bookmarks', icon: BookmarkIcon },\n  { name: 'Analytics', href: '/analytics', icon: ChartBarIcon },\n];\n\nconst Sidebar: React.FC = () => {\n  const pathname = usePathname();\n  const { theme, toggleTheme } = useTheme();\n\n  return (\n    <div className=\"flex flex-col w-64 glass backdrop-blur-xl bg-white/80 dark:bg-slate-900/80 border-r border-white/20 dark:border-slate-700/50 h-screen shadow-2xl\">\n      {/* Logo */}\n      <div className=\"flex items-center justify-center h-16 px-4 border-b border-white/20 dark:border-slate-700/50\">\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\">\n            <span className=\"text-white font-bold text-sm\">HR</span>\n          </div>\n          <h1 className=\"text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">\n            Dashboard\n          </h1>\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"flex-1 px-4 py-6 space-y-2\">\n        {navigation.map((item) => {\n          const isActive = pathname === item.href;\n          return (\n            <Link\n              key={item.name}\n              href={item.href}\n              className={cn(\n                'flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 group',\n                isActive\n                  ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg transform scale-105'\n                  : 'text-slate-600 hover:bg-white/50 hover:text-slate-900 dark:text-slate-300 dark:hover:bg-slate-800/50 dark:hover:text-white hover:shadow-md hover:transform hover:scale-105'\n              )}\n            >\n              <item.icon className={cn(\n                \"mr-3 h-5 w-5 transition-transform duration-200\",\n                isActive ? \"text-white\" : \"group-hover:scale-110\"\n              )} />\n              {item.name}\n            </Link>\n          );\n        })}\n      </nav>\n\n      {/* Theme Toggle */}\n      <div className=\"p-4 border-t border-white/20 dark:border-slate-700/50\">\n        <button\n          onClick={toggleTheme}\n          className=\"flex items-center w-full px-4 py-3 text-sm font-medium text-slate-600 hover:bg-white/50 hover:text-slate-900 dark:text-slate-300 dark:hover:bg-slate-800/50 dark:hover:text-white rounded-xl transition-all duration-200 hover:shadow-md hover:transform hover:scale-105 group\"\n        >\n          {theme === 'dark' ? (\n            <SunIcon className=\"mr-3 h-5 w-5 group-hover:scale-110 transition-transform duration-200\" />\n          ) : (\n            <MoonIcon className=\"mr-3 h-5 w-5 group-hover:scale-110 transition-transform duration-200\" />\n          )}\n          {theme === 'dark' ? 'Light Mode' : 'Dark Mode'}\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default Sidebar;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AAdA;;;;;;;AAgBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAK,MAAM,+MAAA,CAAA,WAAQ;IAAC;IAC/C;QAAE,MAAM;QAAa,MAAM;QAAK,MAAM,yNAAA,CAAA,gBAAa;IAAC;IACpD;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,uNAAA,CAAA,eAAY;IAAC;IAC5D;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,uNAAA,CAAA,eAAY;IAAC;CAC7D;AAED,MAAM,UAAoB;IACxB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAEtC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CAA+B;;;;;;;;;;;sCAEjD,8OAAC;4BAAG,WAAU;sCAA+F;;;;;;;;;;;;;;;;;0BAOjH,8OAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC;oBACf,MAAM,WAAW,aAAa,KAAK,IAAI;oBACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;wBAEH,MAAM,KAAK,IAAI;wBACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gGACA,WACI,0FACA;;0CAGN,8OAAC,KAAK,IAAI;gCAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACrB,kDACA,WAAW,eAAe;;;;;;4BAE3B,KAAK,IAAI;;uBAbL,KAAK,IAAI;;;;;gBAgBpB;;;;;;0BAIF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,SAAS;oBACT,WAAU;;wBAET,UAAU,uBACT,8OAAC,6MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;qFAEnB,8OAAC,+MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAErB,UAAU,SAAS,eAAe;;;;;;;;;;;;;;;;;;AAK7C;uCAEe", "debugId": null}}]}