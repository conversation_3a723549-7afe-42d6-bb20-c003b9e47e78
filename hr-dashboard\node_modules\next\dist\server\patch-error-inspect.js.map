{"version": 3, "sources": ["../../src/server/patch-error-inspect.ts"], "sourcesContent": ["import { findSourceMap as nativeFindSourceMap } from 'module'\nimport * as path from 'path'\nimport * as url from 'url'\nimport type * as util from 'util'\nimport { SourceMapConsumer as SyncSourceMapConsumer } from 'next/dist/compiled/source-map'\nimport type { StackFrame } from 'next/dist/compiled/stacktrace-parser'\nimport {\n  type ModernSourceMapPayload,\n  findApplicableSourceMapPayload,\n  ignoreListAnonymousStackFramesIfSandwiched as ignoreListAnonymousStackFramesIfSandwichedGeneric,\n  sourceMapIgnoreListsEverything,\n} from './lib/source-maps'\nimport { parseStack } from './lib/parse-stack'\nimport { getOriginalCodeFrame } from '../next-devtools/server/shared'\nimport { workUnitAsyncStorage } from './app-render/work-unit-async-storage.external'\nimport { dim } from '../lib/picocolors'\n\ntype FindSourceMapPayload = (\n  sourceURL: string\n) => ModernSourceMapPayload | undefined\n// Find a source map using the bundler's API.\n// This is only a fallback for when Node.js fails to due to bugs e.g. https://github.com/nodejs/node/issues/52102\n// TODO: Remove once all supported Node.js versions are fixed.\n// TODO(veil): Set from Webpack as well\nlet bundlerFindSourceMapPayload: FindSourceMapPayload = () => undefined\n\nexport function setBundlerFindSourceMapImplementation(\n  findSourceMapImplementation: FindSourceMapPayload\n): void {\n  bundlerFindSourceMapPayload = findSourceMapImplementation\n}\n\ninterface IgnoreableStackFrame extends StackFrame {\n  ignored: boolean\n}\n\ntype SourceMapCache = Map<\n  string,\n  null | { map: SyncSourceMapConsumer; payload: ModernSourceMapPayload }\n>\n\nfunction frameToString(frame: StackFrame): string {\n  let sourceLocation = frame.lineNumber !== null ? `:${frame.lineNumber}` : ''\n  if (frame.column !== null && sourceLocation !== '') {\n    sourceLocation += `:${frame.column}`\n  }\n\n  let fileLocation: string | null\n  if (\n    frame.file !== null &&\n    frame.file.startsWith('file://') &&\n    URL.canParse(frame.file)\n  ) {\n    // If not relative to CWD, the path is ambiguous to IDEs and clicking will prompt to select the file first.\n    // In a multi-app repo, this leads to potentially larger file names but will make clicking snappy.\n    // There's no tradeoff for the cases where `dir` in `next dev [dir]` is omitted\n    // since relative to cwd is both the shortest and snappiest.\n    fileLocation = path.relative(process.cwd(), url.fileURLToPath(frame.file))\n  } else if (frame.file !== null && frame.file.startsWith('/')) {\n    fileLocation = path.relative(process.cwd(), frame.file)\n  } else {\n    fileLocation = frame.file\n  }\n\n  return frame.methodName\n    ? `    at ${frame.methodName} (${fileLocation}${sourceLocation})`\n    : `    at ${fileLocation}${sourceLocation}`\n}\n\nfunction computeErrorName(error: Error): string {\n  // TODO: Node.js seems to use a different algorithm\n  // class ReadonlyRequestCookiesError extends Error {}` would read `ReadonlyRequestCookiesError: [...]`\n  // in the stack i.e. seems like under certain conditions it favors the constructor name.\n  return error.name || 'Error'\n}\n\nfunction prepareUnsourcemappedStackTrace(\n  error: Error,\n  structuredStackTrace: any[]\n): string {\n  const name = computeErrorName(error)\n  const message = error.message || ''\n  let stack = name + ': ' + message\n  for (let i = 0; i < structuredStackTrace.length; i++) {\n    stack += '\\n    at ' + structuredStackTrace[i].toString()\n  }\n  return stack\n}\n\nfunction shouldIgnoreListGeneratedFrame(file: string): boolean {\n  return file.startsWith('node:') || file.includes('node_modules')\n}\n\nfunction shouldIgnoreListOriginalFrame(file: string): boolean {\n  return file.includes('node_modules')\n}\n\ninterface SourcemappableStackFrame extends StackFrame {\n  file: NonNullable<StackFrame['file']>\n}\n\ninterface SourceMappedFrame {\n  stack: IgnoreableStackFrame\n  // DEV only\n  code: string | null\n}\n\nfunction createUnsourcemappedFrame(\n  frame: SourcemappableStackFrame\n): SourceMappedFrame {\n  return {\n    stack: {\n      arguments: frame.arguments,\n      column: frame.column,\n      file: frame.file,\n      lineNumber: frame.lineNumber,\n      methodName: frame.methodName,\n      ignored: shouldIgnoreListGeneratedFrame(frame.file),\n    },\n    code: null,\n  }\n}\n\nfunction ignoreListAnonymousStackFramesIfSandwiched(\n  sourceMappedFrames: Array<{\n    stack: IgnoreableStackFrame\n    code: string | null\n  }>\n) {\n  return ignoreListAnonymousStackFramesIfSandwichedGeneric(\n    sourceMappedFrames,\n    (frame) => frame.stack.file === '<anonymous>',\n    (frame) => frame.stack.ignored,\n    (frame) => frame.stack.methodName,\n    (frame) => {\n      frame.stack.ignored = true\n    }\n  )\n}\n\n/**\n * @param frame\n * @param sourceMapCache\n * @returns The original frame if not sourcemapped.\n */\nfunction getSourcemappedFrameIfPossible(\n  frame: SourcemappableStackFrame,\n  sourceMapCache: SourceMapCache,\n  inspectOptions: util.InspectOptions\n): {\n  stack: IgnoreableStackFrame\n  code: string | null\n} {\n  const sourceMapCacheEntry = sourceMapCache.get(frame.file)\n  let sourceMapConsumer: SyncSourceMapConsumer\n  let sourceMapPayload: ModernSourceMapPayload\n  if (sourceMapCacheEntry === undefined) {\n    let sourceURL = frame.file\n    // e.g. \"/APP/.next/server/chunks/ssr/[root-of-the-server]__2934a0._.js\"\n    // will be keyed by Node.js as \"file:///APP/.next/server/chunks/ssr/[root-of-the-server]__2934a0._.js\".\n    // This is likely caused by `callsite.toString()` in `Error.prepareStackTrace converting file URLs to paths.\n    if (sourceURL.startsWith('/')) {\n      sourceURL = url.pathToFileURL(frame.file).toString()\n    }\n    let maybeSourceMapPayload: ModernSourceMapPayload | undefined\n    try {\n      const sourceMap = nativeFindSourceMap(sourceURL)\n      maybeSourceMapPayload = sourceMap?.payload\n    } catch (cause) {\n      // We should not log an actual error instance here because that will re-enter\n      // this codepath during error inspection and could lead to infinite recursion.\n      console.error(\n        `${sourceURL}: Invalid source map. Only conformant source maps can be used to find the original code. Cause: ${cause}`\n      )\n      // If loading fails once, it'll fail every time.\n      // So set the cache to avoid duplicate errors.\n      sourceMapCache.set(frame.file, null)\n      // Don't even fall back to the bundler because it might be not as strict\n      // with regards to parsing and then we fail later once we consume the\n      // source map payload.\n      // This essentially avoids a redundant error where we fail here and then\n      // later on consumption because the bundler just handed back an invalid\n      // source map.\n      return createUnsourcemappedFrame(frame)\n    }\n    if (maybeSourceMapPayload === undefined) {\n      maybeSourceMapPayload = bundlerFindSourceMapPayload(sourceURL)\n    }\n\n    if (maybeSourceMapPayload === undefined) {\n      return createUnsourcemappedFrame(frame)\n    }\n    sourceMapPayload = maybeSourceMapPayload\n    try {\n      sourceMapConsumer = new SyncSourceMapConsumer(\n        // @ts-expect-error -- Module.SourceMap['version'] is number but SyncSourceMapConsumer wants a string\n        sourceMapPayload\n      )\n    } catch (cause) {\n      // We should not log an actual error instance here because that will re-enter\n      // this codepath during error inspection and could lead to infinite recursion.\n      console.error(\n        `${sourceURL}: Invalid source map. Only conformant source maps can be used to find the original code. Cause: ${cause}`\n      )\n      // If creating the consumer fails once, it'll fail every time.\n      // So set the cache to avoid duplicate errors.\n      sourceMapCache.set(frame.file, null)\n      return createUnsourcemappedFrame(frame)\n    }\n    sourceMapCache.set(frame.file, {\n      map: sourceMapConsumer,\n      payload: sourceMapPayload,\n    })\n  } else if (sourceMapCacheEntry === null) {\n    // We failed earlier getting the payload or consumer.\n    // Just return an unsourcemapped frame.\n    // Errors will already be logged.\n    return createUnsourcemappedFrame(frame)\n  } else {\n    sourceMapConsumer = sourceMapCacheEntry.map\n    sourceMapPayload = sourceMapCacheEntry.payload\n  }\n\n  const sourcePosition = sourceMapConsumer.originalPositionFor({\n    column: frame.column ?? 0,\n    line: frame.lineNumber ?? 1,\n  })\n\n  const applicableSourceMap = findApplicableSourceMapPayload(\n    frame.lineNumber ?? 0,\n    frame.column ?? 0,\n    sourceMapPayload\n  )\n  let ignored =\n    applicableSourceMap !== undefined &&\n    sourceMapIgnoreListsEverything(applicableSourceMap)\n  if (sourcePosition.source === null) {\n    return {\n      stack: {\n        arguments: frame.arguments,\n        column: frame.column,\n        file: frame.file,\n        lineNumber: frame.lineNumber,\n        methodName: frame.methodName,\n        ignored: ignored || shouldIgnoreListGeneratedFrame(frame.file),\n      },\n      code: null,\n    }\n  }\n\n  // TODO(veil): Upstream a method to sourcemap consumer that immediately says if a frame is ignored or not.\n  if (applicableSourceMap === undefined) {\n    console.error('No applicable source map found in sections for frame', frame)\n  } else if (!ignored && shouldIgnoreListOriginalFrame(sourcePosition.source)) {\n    // Externals may be libraries that don't ship ignoreLists.\n    // This is really taking control away from libraries.\n    // They should still ship `ignoreList` so that attached debuggers ignore-list their frames.\n    // TODO: Maybe only ignore library sourcemaps if `ignoreList` is absent?\n    // Though keep in mind that Turbopack omits empty `ignoreList`.\n    // So if we establish this convention, we should communicate it to the ecosystem.\n    ignored = true\n  } else if (!ignored) {\n    // TODO: O(n^2). Consider moving `ignoreList` into a Set\n    const sourceIndex = applicableSourceMap.sources.indexOf(\n      sourcePosition.source\n    )\n    ignored = applicableSourceMap.ignoreList?.includes(sourceIndex) ?? false\n  }\n\n  const originalFrame: IgnoreableStackFrame = {\n    // We ignore the sourcemapped name since it won't be the correct name.\n    // The callsite will point to the column of the variable name instead of the\n    // name of the enclosing function.\n    // TODO(NDX-531): Spy on prepareStackTrace to get the enclosing line number for method name mapping.\n    methodName: frame.methodName\n      ?.replace('__WEBPACK_DEFAULT_EXPORT__', 'default')\n      ?.replace('__webpack_exports__.', ''),\n    column: sourcePosition.column,\n    file: sourcePosition.source,\n    lineNumber: sourcePosition.line,\n    // TODO: c&p from async createOriginalStackFrame but why not frame.arguments?\n    arguments: [],\n    ignored,\n  }\n\n  /** undefined = not yet computed*/\n  let codeFrame: string | null | undefined\n\n  return Object.defineProperty(\n    {\n      stack: originalFrame,\n      code: null,\n    },\n    'code',\n    {\n      get: () => {\n        if (codeFrame === undefined) {\n          const sourceContent: string | null =\n            sourceMapConsumer.sourceContentFor(\n              sourcePosition.source,\n              /* returnNullOnMissing */ true\n            ) ?? null\n          codeFrame = getOriginalCodeFrame(\n            originalFrame,\n            sourceContent,\n            inspectOptions.colors\n          )\n        }\n        return codeFrame\n      },\n    }\n  )\n}\n\nfunction parseAndSourceMap(\n  error: Error,\n  inspectOptions: util.InspectOptions\n): string {\n  // TODO(veil): Expose as CLI arg or config option. Useful for local debugging.\n  const showIgnoreListed = false\n  // We overwrote Error.prepareStackTrace earlier so error.stack is not sourcemapped.\n  let unparsedStack = String(error.stack)\n  // We could just read it from `error.stack`.\n  // This works around cases where a 3rd party `Error.prepareStackTrace` implementation\n  // doesn't implement the name computation correctly.\n  const errorName = computeErrorName(error)\n\n  let idx = unparsedStack.indexOf('react_stack_bottom_frame')\n  if (idx !== -1) {\n    idx = unparsedStack.lastIndexOf('\\n', idx)\n  } else {\n    idx = unparsedStack.indexOf('react-stack-bottom-frame')\n    if (idx !== -1) {\n      idx = unparsedStack.lastIndexOf('\\n', idx)\n    }\n  }\n  if (idx !== -1 && !showIgnoreListed) {\n    // Cut off everything after the bottom frame since it'll be React internals.\n    unparsedStack = unparsedStack.slice(0, idx)\n  }\n\n  const unsourcemappedStack = parseStack(unparsedStack)\n  const sourceMapCache: SourceMapCache = new Map()\n\n  const sourceMappedFrames: Array<{\n    stack: IgnoreableStackFrame\n    code: string | null\n  }> = []\n  let sourceFrame: null | string = null\n  for (const frame of unsourcemappedStack) {\n    if (frame.file === null) {\n      sourceMappedFrames.push({\n        code: null,\n        stack: {\n          arguments: frame.arguments,\n          column: frame.column,\n          file: frame.file,\n          lineNumber: frame.lineNumber,\n          methodName: frame.methodName,\n          ignored: false,\n        },\n      })\n    } else {\n      const sourcemappedFrame = getSourcemappedFrameIfPossible(\n        // We narrowed this earlier by bailing if `frame.file` is null.\n        frame as SourcemappableStackFrame,\n        sourceMapCache,\n        inspectOptions\n      )\n      sourceMappedFrames.push(sourcemappedFrame)\n\n      // We can determine the sourceframe here.\n      // anonymous frames won't have a sourceframe so we don't need to scan\n      // all stacks again to check if they are sandwiched between ignored frames.\n      if (\n        sourceFrame === null &&\n        // TODO: Is this the right choice?\n        !sourcemappedFrame.stack.ignored &&\n        sourcemappedFrame.code !== null\n      ) {\n        sourceFrame = sourcemappedFrame.code\n      }\n    }\n  }\n\n  ignoreListAnonymousStackFramesIfSandwiched(sourceMappedFrames)\n\n  let sourceMappedStack = ''\n  for (let i = 0; i < sourceMappedFrames.length; i++) {\n    const frame = sourceMappedFrames[i]\n\n    if (!frame.stack.ignored) {\n      sourceMappedStack += '\\n' + frameToString(frame.stack)\n    } else if (showIgnoreListed) {\n      sourceMappedStack += '\\n' + dim(frameToString(frame.stack))\n    }\n  }\n\n  return (\n    errorName +\n    ': ' +\n    error.message +\n    sourceMappedStack +\n    (sourceFrame !== null ? '\\n' + sourceFrame : '')\n  )\n}\n\nfunction sourceMapError(\n  this: void,\n  error: Error,\n  inspectOptions: util.InspectOptions\n): Error {\n  // Create a new Error object with the source mapping applied and then use native\n  // Node.js formatting on the result.\n  const newError =\n    error.cause !== undefined\n      ? // Setting an undefined `cause` would print `[cause]: undefined`\n        new Error(error.message, { cause: error.cause })\n      : new Error(error.message)\n\n  // TODO: Ensure `class MyError extends Error {}` prints `MyError` as the name\n  newError.stack = parseAndSourceMap(error, inspectOptions)\n\n  for (const key in error) {\n    if (!Object.prototype.hasOwnProperty.call(newError, key)) {\n      // @ts-expect-error -- We're copying all enumerable properties.\n      // So they definitely exist on `this` and obviously have no type on `newError` (yet)\n      newError[key] = error[key]\n    }\n  }\n\n  return newError\n}\n\nexport function patchErrorInspectNodeJS(\n  errorConstructor: ErrorConstructor\n): void {\n  const inspectSymbol = Symbol.for('nodejs.util.inspect.custom')\n\n  errorConstructor.prepareStackTrace = prepareUnsourcemappedStackTrace\n\n  // @ts-expect-error -- TODO upstream types\n  // eslint-disable-next-line no-extend-native -- We're not extending but overriding.\n  errorConstructor.prototype[inspectSymbol] = function (\n    depth: number,\n    inspectOptions: util.InspectOptions,\n    inspect: typeof util.inspect\n  ): string {\n    // avoid false-positive dynamic i/o warnings e.g. due to usage of `Math.random` in `source-map`.\n    return workUnitAsyncStorage.exit(() => {\n      const newError = sourceMapError(this, inspectOptions)\n\n      const originalCustomInspect = (newError as any)[inspectSymbol]\n      // Prevent infinite recursion.\n      // { customInspect: false } would result in `error.cause` not using our inspect.\n      Object.defineProperty(newError, inspectSymbol, {\n        value: undefined,\n        enumerable: false,\n        writable: true,\n      })\n      try {\n        return inspect(newError, {\n          ...inspectOptions,\n          depth:\n            (inspectOptions.depth ??\n              // Default in Node.js\n              2) - depth,\n        })\n      } finally {\n        ;(newError as any)[inspectSymbol] = originalCustomInspect\n      }\n    })\n  }\n}\n\nexport function patchErrorInspectEdgeLite(\n  errorConstructor: ErrorConstructor\n): void {\n  const inspectSymbol = Symbol.for('edge-runtime.inspect.custom')\n\n  errorConstructor.prepareStackTrace = prepareUnsourcemappedStackTrace\n\n  // @ts-expect-error -- TODO upstream types\n  // eslint-disable-next-line no-extend-native -- We're not extending but overriding.\n  errorConstructor.prototype[inspectSymbol] = function ({\n    format,\n  }: {\n    format: (...args: unknown[]) => string\n  }): string {\n    // avoid false-positive dynamic i/o warnings e.g. due to usage of `Math.random` in `source-map`.\n    return workUnitAsyncStorage.exit(() => {\n      const newError = sourceMapError(this, {})\n\n      const originalCustomInspect = (newError as any)[inspectSymbol]\n      // Prevent infinite recursion.\n      Object.defineProperty(newError, inspectSymbol, {\n        value: undefined,\n        enumerable: false,\n        writable: true,\n      })\n      try {\n        return format(newError)\n      } finally {\n        ;(newError as any)[inspectSymbol] = originalCustomInspect\n      }\n    })\n  }\n}\n"], "names": ["patchErrorInspectEdgeLite", "patchErrorInspectNodeJS", "setBundlerFindSourceMapImplementation", "bundlerFindSourceMapPayload", "undefined", "findSourceMapImplementation", "frameToString", "frame", "sourceLocation", "lineNumber", "column", "fileLocation", "file", "startsWith", "URL", "canParse", "path", "relative", "process", "cwd", "url", "fileURLToPath", "methodName", "computeErrorName", "error", "name", "prepareUnsourcemappedStackTrace", "structuredStackTrace", "message", "stack", "i", "length", "toString", "shouldIgnoreListGeneratedFrame", "includes", "shouldIgnoreListOriginalFrame", "createUnsourcemappedFrame", "arguments", "ignored", "code", "ignoreListAnonymousStackFramesIfSandwiched", "sourceMappedFrames", "ignoreListAnonymousStackFramesIfSandwichedGeneric", "getSourcemappedFrameIfPossible", "sourceMapCache", "inspectOptions", "sourceMapCacheEntry", "get", "sourceMapConsumer", "sourceMapPayload", "sourceURL", "pathToFileURL", "maybeSourceMapPayload", "sourceMap", "nativeFindSourceMap", "payload", "cause", "console", "set", "SyncSourceMapConsumer", "map", "sourcePosition", "originalPositionFor", "line", "applicableSourceMap", "findApplicableSourceMapPayload", "sourceMapIgnoreListsEverything", "source", "sourceIndex", "sources", "indexOf", "ignoreList", "originalFrame", "replace", "codeFrame", "Object", "defineProperty", "sourceContent", "sourceContentFor", "getOriginalCodeFrame", "colors", "parseAndSourceMap", "showIgnoreListed", "unparsedStack", "String", "errorName", "idx", "lastIndexOf", "slice", "unsourcemappedStack", "parseStack", "Map", "sourceFrame", "push", "sourcemappedFrame", "sourceMappedStack", "dim", "sourceMapError", "newError", "Error", "key", "prototype", "hasOwnProperty", "call", "errorConstructor", "inspectSymbol", "Symbol", "for", "prepareStackTrace", "depth", "inspect", "workUnitAsyncStorage", "exit", "originalCustomInspect", "value", "enumerable", "writable", "format"], "mappings": ";;;;;;;;;;;;;;;;IA2dgBA,yBAAyB;eAAzBA;;IAzCAC,uBAAuB;eAAvBA;;IAxZAC,qCAAqC;eAArCA;;;wBA1BqC;8DAC/B;6DACD;2BAEsC;4BAOpD;4BACoB;wBACU;8CACA;4BACjB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKpB,6CAA6C;AAC7C,iHAAiH;AACjH,8DAA8D;AAC9D,uCAAuC;AACvC,IAAIC,8BAAoD,IAAMC;AAEvD,SAASF,sCACdG,2BAAiD;IAEjDF,8BAA8BE;AAChC;AAWA,SAASC,cAAcC,KAAiB;IACtC,IAAIC,iBAAiBD,MAAME,UAAU,KAAK,OAAO,CAAC,CAAC,EAAEF,MAAME,UAAU,EAAE,GAAG;IAC1E,IAAIF,MAAMG,MAAM,KAAK,QAAQF,mBAAmB,IAAI;QAClDA,kBAAkB,CAAC,CAAC,EAAED,MAAMG,MAAM,EAAE;IACtC;IAEA,IAAIC;IACJ,IACEJ,MAAMK,IAAI,KAAK,QACfL,MAAMK,IAAI,CAACC,UAAU,CAAC,cACtBC,IAAIC,QAAQ,CAACR,MAAMK,IAAI,GACvB;QACA,2GAA2G;QAC3G,kGAAkG;QAClG,+EAA+E;QAC/E,4DAA4D;QAC5DD,eAAeK,MAAKC,QAAQ,CAACC,QAAQC,GAAG,IAAIC,KAAIC,aAAa,CAACd,MAAMK,IAAI;IAC1E,OAAO,IAAIL,MAAMK,IAAI,KAAK,QAAQL,MAAMK,IAAI,CAACC,UAAU,CAAC,MAAM;QAC5DF,eAAeK,MAAKC,QAAQ,CAACC,QAAQC,GAAG,IAAIZ,MAAMK,IAAI;IACxD,OAAO;QACLD,eAAeJ,MAAMK,IAAI;IAC3B;IAEA,OAAOL,MAAMe,UAAU,GACnB,CAAC,OAAO,EAAEf,MAAMe,UAAU,CAAC,EAAE,EAAEX,eAAeH,eAAe,CAAC,CAAC,GAC/D,CAAC,OAAO,EAAEG,eAAeH,gBAAgB;AAC/C;AAEA,SAASe,iBAAiBC,KAAY;IACpC,mDAAmD;IACnD,sGAAsG;IACtG,wFAAwF;IACxF,OAAOA,MAAMC,IAAI,IAAI;AACvB;AAEA,SAASC,gCACPF,KAAY,EACZG,oBAA2B;IAE3B,MAAMF,OAAOF,iBAAiBC;IAC9B,MAAMI,UAAUJ,MAAMI,OAAO,IAAI;IACjC,IAAIC,QAAQJ,OAAO,OAAOG;IAC1B,IAAK,IAAIE,IAAI,GAAGA,IAAIH,qBAAqBI,MAAM,EAAED,IAAK;QACpDD,SAAS,cAAcF,oBAAoB,CAACG,EAAE,CAACE,QAAQ;IACzD;IACA,OAAOH;AACT;AAEA,SAASI,+BAA+BrB,IAAY;IAClD,OAAOA,KAAKC,UAAU,CAAC,YAAYD,KAAKsB,QAAQ,CAAC;AACnD;AAEA,SAASC,8BAA8BvB,IAAY;IACjD,OAAOA,KAAKsB,QAAQ,CAAC;AACvB;AAYA,SAASE,0BACP7B,KAA+B;IAE/B,OAAO;QACLsB,OAAO;YACLQ,WAAW9B,MAAM8B,SAAS;YAC1B3B,QAAQH,MAAMG,MAAM;YACpBE,MAAML,MAAMK,IAAI;YAChBH,YAAYF,MAAME,UAAU;YAC5Ba,YAAYf,MAAMe,UAAU;YAC5BgB,SAASL,+BAA+B1B,MAAMK,IAAI;QACpD;QACA2B,MAAM;IACR;AACF;AAEA,SAASC,2CACPC,kBAGE;IAEF,OAAOC,IAAAA,sDAAiD,EACtDD,oBACA,CAAClC,QAAUA,MAAMsB,KAAK,CAACjB,IAAI,KAAK,eAChC,CAACL,QAAUA,MAAMsB,KAAK,CAACS,OAAO,EAC9B,CAAC/B,QAAUA,MAAMsB,KAAK,CAACP,UAAU,EACjC,CAACf;QACCA,MAAMsB,KAAK,CAACS,OAAO,GAAG;IACxB;AAEJ;AAEA;;;;CAIC,GACD,SAASK,+BACPpC,KAA+B,EAC/BqC,cAA8B,EAC9BC,cAAmC;QA8HrBtC,2BAAAA;IAzHd,MAAMuC,sBAAsBF,eAAeG,GAAG,CAACxC,MAAMK,IAAI;IACzD,IAAIoC;IACJ,IAAIC;IACJ,IAAIH,wBAAwB1C,WAAW;QACrC,IAAI8C,YAAY3C,MAAMK,IAAI;QAC1B,wEAAwE;QACxE,uGAAuG;QACvG,4GAA4G;QAC5G,IAAIsC,UAAUrC,UAAU,CAAC,MAAM;YAC7BqC,YAAY9B,KAAI+B,aAAa,CAAC5C,MAAMK,IAAI,EAAEoB,QAAQ;QACpD;QACA,IAAIoB;QACJ,IAAI;YACF,MAAMC,YAAYC,IAAAA,qBAAmB,EAACJ;YACtCE,wBAAwBC,6BAAAA,UAAWE,OAAO;QAC5C,EAAE,OAAOC,OAAO;YACd,6EAA6E;YAC7E,8EAA8E;YAC9EC,QAAQjC,KAAK,CACX,GAAG0B,UAAU,gGAAgG,EAAEM,OAAO;YAExH,gDAAgD;YAChD,8CAA8C;YAC9CZ,eAAec,GAAG,CAACnD,MAAMK,IAAI,EAAE;YAC/B,wEAAwE;YACxE,qEAAqE;YACrE,sBAAsB;YACtB,wEAAwE;YACxE,uEAAuE;YACvE,cAAc;YACd,OAAOwB,0BAA0B7B;QACnC;QACA,IAAI6C,0BAA0BhD,WAAW;YACvCgD,wBAAwBjD,4BAA4B+C;QACtD;QAEA,IAAIE,0BAA0BhD,WAAW;YACvC,OAAOgC,0BAA0B7B;QACnC;QACA0C,mBAAmBG;QACnB,IAAI;YACFJ,oBAAoB,IAAIW,4BAAqB,CAC3C,qGAAqG;YACrGV;QAEJ,EAAE,OAAOO,OAAO;YACd,6EAA6E;YAC7E,8EAA8E;YAC9EC,QAAQjC,KAAK,CACX,GAAG0B,UAAU,gGAAgG,EAAEM,OAAO;YAExH,8DAA8D;YAC9D,8CAA8C;YAC9CZ,eAAec,GAAG,CAACnD,MAAMK,IAAI,EAAE;YAC/B,OAAOwB,0BAA0B7B;QACnC;QACAqC,eAAec,GAAG,CAACnD,MAAMK,IAAI,EAAE;YAC7BgD,KAAKZ;YACLO,SAASN;QACX;IACF,OAAO,IAAIH,wBAAwB,MAAM;QACvC,qDAAqD;QACrD,uCAAuC;QACvC,iCAAiC;QACjC,OAAOV,0BAA0B7B;IACnC,OAAO;QACLyC,oBAAoBF,oBAAoBc,GAAG;QAC3CX,mBAAmBH,oBAAoBS,OAAO;IAChD;IAEA,MAAMM,iBAAiBb,kBAAkBc,mBAAmB,CAAC;QAC3DpD,QAAQH,MAAMG,MAAM,IAAI;QACxBqD,MAAMxD,MAAME,UAAU,IAAI;IAC5B;IAEA,MAAMuD,sBAAsBC,IAAAA,0CAA8B,EACxD1D,MAAME,UAAU,IAAI,GACpBF,MAAMG,MAAM,IAAI,GAChBuC;IAEF,IAAIX,UACF0B,wBAAwB5D,aACxB8D,IAAAA,0CAA8B,EAACF;IACjC,IAAIH,eAAeM,MAAM,KAAK,MAAM;QAClC,OAAO;YACLtC,OAAO;gBACLQ,WAAW9B,MAAM8B,SAAS;gBAC1B3B,QAAQH,MAAMG,MAAM;gBACpBE,MAAML,MAAMK,IAAI;gBAChBH,YAAYF,MAAME,UAAU;gBAC5Ba,YAAYf,MAAMe,UAAU;gBAC5BgB,SAASA,WAAWL,+BAA+B1B,MAAMK,IAAI;YAC/D;YACA2B,MAAM;QACR;IACF;IAEA,0GAA0G;IAC1G,IAAIyB,wBAAwB5D,WAAW;QACrCqD,QAAQjC,KAAK,CAAC,wDAAwDjB;IACxE,OAAO,IAAI,CAAC+B,WAAWH,8BAA8B0B,eAAeM,MAAM,GAAG;QAC3E,0DAA0D;QAC1D,qDAAqD;QACrD,2FAA2F;QAC3F,wEAAwE;QACxE,+DAA+D;QAC/D,iFAAiF;QACjF7B,UAAU;IACZ,OAAO,IAAI,CAACA,SAAS;YAKT0B;QAJV,wDAAwD;QACxD,MAAMI,cAAcJ,oBAAoBK,OAAO,CAACC,OAAO,CACrDT,eAAeM,MAAM;QAEvB7B,UAAU0B,EAAAA,kCAAAA,oBAAoBO,UAAU,qBAA9BP,gCAAgC9B,QAAQ,CAACkC,iBAAgB;IACrE;IAEA,MAAMI,gBAAsC;QAC1C,sEAAsE;QACtE,4EAA4E;QAC5E,kCAAkC;QAClC,oGAAoG;QACpGlD,UAAU,GAAEf,oBAAAA,MAAMe,UAAU,sBAAhBf,4BAAAA,kBACRkE,OAAO,CAAC,8BAA8B,+BAD9BlE,0BAERkE,OAAO,CAAC,wBAAwB;QACpC/D,QAAQmD,eAAenD,MAAM;QAC7BE,MAAMiD,eAAeM,MAAM;QAC3B1D,YAAYoD,eAAeE,IAAI;QAC/B,6EAA6E;QAC7E1B,WAAW,EAAE;QACbC;IACF;IAEA,gCAAgC,GAChC,IAAIoC;IAEJ,OAAOC,OAAOC,cAAc,CAC1B;QACE/C,OAAO2C;QACPjC,MAAM;IACR,GACA,QACA;QACEQ,KAAK;YACH,IAAI2B,cAActE,WAAW;gBAC3B,MAAMyE,gBACJ7B,kBAAkB8B,gBAAgB,CAChCjB,eAAeM,MAAM,EACrB,uBAAuB,GAAG,SACvB;gBACPO,YAAYK,IAAAA,4BAAoB,EAC9BP,eACAK,eACAhC,eAAemC,MAAM;YAEzB;YACA,OAAON;QACT;IACF;AAEJ;AAEA,SAASO,kBACPzD,KAAY,EACZqB,cAAmC;IAEnC,8EAA8E;IAC9E,MAAMqC,mBAAmB;IACzB,mFAAmF;IACnF,IAAIC,gBAAgBC,OAAO5D,MAAMK,KAAK;IACtC,4CAA4C;IAC5C,qFAAqF;IACrF,oDAAoD;IACpD,MAAMwD,YAAY9D,iBAAiBC;IAEnC,IAAI8D,MAAMH,cAAcb,OAAO,CAAC;IAChC,IAAIgB,QAAQ,CAAC,GAAG;QACdA,MAAMH,cAAcI,WAAW,CAAC,MAAMD;IACxC,OAAO;QACLA,MAAMH,cAAcb,OAAO,CAAC;QAC5B,IAAIgB,QAAQ,CAAC,GAAG;YACdA,MAAMH,cAAcI,WAAW,CAAC,MAAMD;QACxC;IACF;IACA,IAAIA,QAAQ,CAAC,KAAK,CAACJ,kBAAkB;QACnC,4EAA4E;QAC5EC,gBAAgBA,cAAcK,KAAK,CAAC,GAAGF;IACzC;IAEA,MAAMG,sBAAsBC,IAAAA,sBAAU,EAACP;IACvC,MAAMvC,iBAAiC,IAAI+C;IAE3C,MAAMlD,qBAGD,EAAE;IACP,IAAImD,cAA6B;IACjC,KAAK,MAAMrF,SAASkF,oBAAqB;QACvC,IAAIlF,MAAMK,IAAI,KAAK,MAAM;YACvB6B,mBAAmBoD,IAAI,CAAC;gBACtBtD,MAAM;gBACNV,OAAO;oBACLQ,WAAW9B,MAAM8B,SAAS;oBAC1B3B,QAAQH,MAAMG,MAAM;oBACpBE,MAAML,MAAMK,IAAI;oBAChBH,YAAYF,MAAME,UAAU;oBAC5Ba,YAAYf,MAAMe,UAAU;oBAC5BgB,SAAS;gBACX;YACF;QACF,OAAO;YACL,MAAMwD,oBAAoBnD,+BACxB,+DAA+D;YAC/DpC,OACAqC,gBACAC;YAEFJ,mBAAmBoD,IAAI,CAACC;YAExB,yCAAyC;YACzC,qEAAqE;YACrE,2EAA2E;YAC3E,IACEF,gBAAgB,QAChB,kCAAkC;YAClC,CAACE,kBAAkBjE,KAAK,CAACS,OAAO,IAChCwD,kBAAkBvD,IAAI,KAAK,MAC3B;gBACAqD,cAAcE,kBAAkBvD,IAAI;YACtC;QACF;IACF;IAEAC,2CAA2CC;IAE3C,IAAIsD,oBAAoB;IACxB,IAAK,IAAIjE,IAAI,GAAGA,IAAIW,mBAAmBV,MAAM,EAAED,IAAK;QAClD,MAAMvB,QAAQkC,kBAAkB,CAACX,EAAE;QAEnC,IAAI,CAACvB,MAAMsB,KAAK,CAACS,OAAO,EAAE;YACxByD,qBAAqB,OAAOzF,cAAcC,MAAMsB,KAAK;QACvD,OAAO,IAAIqD,kBAAkB;YAC3Ba,qBAAqB,OAAOC,IAAAA,eAAG,EAAC1F,cAAcC,MAAMsB,KAAK;QAC3D;IACF;IAEA,OACEwD,YACA,OACA7D,MAAMI,OAAO,GACbmE,oBACCH,CAAAA,gBAAgB,OAAO,OAAOA,cAAc,EAAC;AAElD;AAEA,SAASK,eAEPzE,KAAY,EACZqB,cAAmC;IAEnC,gFAAgF;IAChF,oCAAoC;IACpC,MAAMqD,WACJ1E,MAAMgC,KAAK,KAAKpD,YAEZ,qBAAgD,CAAhD,IAAI+F,MAAM3E,MAAMI,OAAO,EAAE;QAAE4B,OAAOhC,MAAMgC,KAAK;IAAC,IAA9C,qBAAA;eAAA;oBAAA;sBAAA;IAA+C,KAC/C,qBAAwB,CAAxB,IAAI2C,MAAM3E,MAAMI,OAAO,GAAvB,qBAAA;eAAA;oBAAA;sBAAA;IAAuB;IAE7B,6EAA6E;IAC7EsE,SAASrE,KAAK,GAAGoD,kBAAkBzD,OAAOqB;IAE1C,IAAK,MAAMuD,OAAO5E,MAAO;QACvB,IAAI,CAACmD,OAAO0B,SAAS,CAACC,cAAc,CAACC,IAAI,CAACL,UAAUE,MAAM;YACxD,+DAA+D;YAC/D,oFAAoF;YACpFF,QAAQ,CAACE,IAAI,GAAG5E,KAAK,CAAC4E,IAAI;QAC5B;IACF;IAEA,OAAOF;AACT;AAEO,SAASjG,wBACduG,gBAAkC;IAElC,MAAMC,gBAAgBC,OAAOC,GAAG,CAAC;IAEjCH,iBAAiBI,iBAAiB,GAAGlF;IAErC,0CAA0C;IAC1C,mFAAmF;IACnF8E,iBAAiBH,SAAS,CAACI,cAAc,GAAG,SAC1CI,KAAa,EACbhE,cAAmC,EACnCiE,OAA4B;QAE5B,gGAAgG;QAChG,OAAOC,kDAAoB,CAACC,IAAI,CAAC;YAC/B,MAAMd,WAAWD,eAAe,IAAI,EAAEpD;YAEtC,MAAMoE,wBAAwB,AAACf,QAAgB,CAACO,cAAc;YAC9D,8BAA8B;YAC9B,gFAAgF;YAChF9B,OAAOC,cAAc,CAACsB,UAAUO,eAAe;gBAC7CS,OAAO9G;gBACP+G,YAAY;gBACZC,UAAU;YACZ;YACA,IAAI;gBACF,OAAON,QAAQZ,UAAU;oBACvB,GAAGrD,cAAc;oBACjBgE,OACE,AAAChE,CAAAA,eAAegE,KAAK,IACnB,qBAAqB;oBACrB,CAAA,IAAKA;gBACX;YACF,SAAU;;gBACNX,QAAgB,CAACO,cAAc,GAAGQ;YACtC;QACF;IACF;AACF;AAEO,SAASjH,0BACdwG,gBAAkC;IAElC,MAAMC,gBAAgBC,OAAOC,GAAG,CAAC;IAEjCH,iBAAiBI,iBAAiB,GAAGlF;IAErC,0CAA0C;IAC1C,mFAAmF;IACnF8E,iBAAiBH,SAAS,CAACI,cAAc,GAAG,SAAU,EACpDY,MAAM,EAGP;QACC,gGAAgG;QAChG,OAAON,kDAAoB,CAACC,IAAI,CAAC;YAC/B,MAAMd,WAAWD,eAAe,IAAI,EAAE,CAAC;YAEvC,MAAMgB,wBAAwB,AAACf,QAAgB,CAACO,cAAc;YAC9D,8BAA8B;YAC9B9B,OAAOC,cAAc,CAACsB,UAAUO,eAAe;gBAC7CS,OAAO9G;gBACP+G,YAAY;gBACZC,UAAU;YACZ;YACA,IAAI;gBACF,OAAOC,OAAOnB;YAChB,SAAU;;gBACNA,QAAgB,CAACO,cAAc,GAAGQ;YACtC;QACF;IACF;AACF", "ignoreList": [0]}