{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/hooks/useSearch.ts"], "sourcesContent": ["import { useState, useMemo } from 'react';\nimport { Employee, SearchFilters } from '@/types';\n\nexport const useSearch = (employees: Employee[]) => {\n  const [filters, setFilters] = useState<SearchFilters>({\n    query: '',\n    departments: [],\n    minRating: 0,\n    maxRating: 5\n  });\n\n  const filteredEmployees = useMemo(() => {\n    return employees.filter(employee => {\n      // Text search\n      const matchesQuery = filters.query === '' || \n        employee.firstName.toLowerCase().includes(filters.query.toLowerCase()) ||\n        employee.lastName.toLowerCase().includes(filters.query.toLowerCase()) ||\n        employee.email.toLowerCase().includes(filters.query.toLowerCase()) ||\n        employee.department.toLowerCase().includes(filters.query.toLowerCase());\n\n      // Department filter\n      const matchesDepartment = filters.departments.length === 0 || \n        filters.departments.includes(employee.department);\n\n      // Rating filter\n      const matchesRating = employee.performanceRating >= filters.minRating && \n        employee.performanceRating <= filters.maxRating;\n\n      return matchesQuery && matchesDepartment && matchesRating;\n    });\n  }, [employees, filters]);\n\n  const updateQuery = (query: string) => {\n    setFilters(prev => ({ ...prev, query }));\n  };\n\n  const updateDepartments = (departments: string[]) => {\n    setFilters(prev => ({ ...prev, departments }));\n  };\n\n  const updateRatingRange = (minRating: number, maxRating: number) => {\n    setFilters(prev => ({ ...prev, minRating, maxRating }));\n  };\n\n  const clearFilters = () => {\n    setFilters({\n      query: '',\n      departments: [],\n      minRating: 0,\n      maxRating: 5\n    });\n  };\n\n  const availableDepartments = useMemo(() => {\n    const departments = new Set(employees.map(emp => emp.department));\n    return Array.from(departments).sort();\n  }, [employees]);\n\n  return {\n    filters,\n    filteredEmployees,\n    updateQuery,\n    updateDepartments,\n    updateRatingRange,\n    clearFilters,\n    availableDepartments\n  };\n};\n"], "names": [], "mappings": ";;;AAAA;;;AAGO,MAAM,YAAY,CAAC;;IACxB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;QACpD,OAAO;QACP,aAAa,EAAE;QACf,WAAW;QACX,WAAW;IACb;IAEA,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;gDAAE;YAChC,OAAO,UAAU,MAAM;wDAAC,CAAA;oBACtB,cAAc;oBACd,MAAM,eAAe,QAAQ,KAAK,KAAK,MACrC,SAAS,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,KAAK,CAAC,WAAW,OACnE,SAAS,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,KAAK,CAAC,WAAW,OAClE,SAAS,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,KAAK,CAAC,WAAW,OAC/D,SAAS,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,KAAK,CAAC,WAAW;oBAEtE,oBAAoB;oBACpB,MAAM,oBAAoB,QAAQ,WAAW,CAAC,MAAM,KAAK,KACvD,QAAQ,WAAW,CAAC,QAAQ,CAAC,SAAS,UAAU;oBAElD,gBAAgB;oBAChB,MAAM,gBAAgB,SAAS,iBAAiB,IAAI,QAAQ,SAAS,IACnE,SAAS,iBAAiB,IAAI,QAAQ,SAAS;oBAEjD,OAAO,gBAAgB,qBAAqB;gBAC9C;;QACF;+CAAG;QAAC;QAAW;KAAQ;IAEvB,MAAM,cAAc,CAAC;QACnB,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE;YAAM,CAAC;IACxC;IAEA,MAAM,oBAAoB,CAAC;QACzB,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE;YAAY,CAAC;IAC9C;IAEA,MAAM,oBAAoB,CAAC,WAAmB;QAC5C,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE;gBAAW;YAAU,CAAC;IACvD;IAEA,MAAM,eAAe;QACnB,WAAW;YACT,OAAO;YACP,aAAa,EAAE;YACf,WAAW;YACX,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;mDAAE;YACnC,MAAM,cAAc,IAAI,IAAI,UAAU,GAAG;2DAAC,CAAA,MAAO,IAAI,UAAU;;YAC/D,OAAO,MAAM,IAAI,CAAC,aAAa,IAAI;QACrC;kDAAG;QAAC;KAAU;IAEd,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAhEa", "debugId": null}}, {"offset": {"line": 96, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/hooks/useBookmarks.ts"], "sourcesContent": ["import { useBookmarkContext } from '@/contexts/BookmarkContext';\n\nexport const useBookmarks = () => {\n  return useBookmarkContext();\n};\n"], "names": [], "mappings": ";;;AAAA;;;AAEO,MAAM,eAAe;;IAC1B,OAAO,CAAA,GAAA,sIAAA,CAAA,qBAAkB,AAAD;AAC1B;GAFa;;QACJ,sIAAA,CAAA,qBAAkB", "debugId": null}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/hooks/usePagination.ts"], "sourcesContent": ["import { useState, useMemo } from 'react';\n\ninterface UsePaginationProps<T> {\n  data: T[];\n  initialItemsPerPage?: number;\n}\n\ninterface UsePaginationReturn<T> {\n  currentPage: number;\n  totalPages: number;\n  itemsPerPage: number;\n  paginatedData: T[];\n  totalItems: number;\n  setCurrentPage: (page: number) => void;\n  setItemsPerPage: (itemsPerPage: number) => void;\n  goToNextPage: () => void;\n  goToPreviousPage: () => void;\n  goToFirstPage: () => void;\n  goToLastPage: () => void;\n  hasNextPage: boolean;\n  hasPreviousPage: boolean;\n}\n\nexport const usePagination = <T>({\n  data,\n  initialItemsPerPage = 12\n}: UsePaginationProps<T>): UsePaginationReturn<T> => {\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage, setItemsPerPage] = useState(initialItemsPerPage);\n\n  const totalItems = data.length;\n  const totalPages = Math.ceil(totalItems / itemsPerPage);\n\n  const paginatedData = useMemo(() => {\n    const startIndex = (currentPage - 1) * itemsPerPage;\n    const endIndex = startIndex + itemsPerPage;\n    return data.slice(startIndex, endIndex);\n  }, [data, currentPage, itemsPerPage]);\n\n  const hasNextPage = currentPage < totalPages;\n  const hasPreviousPage = currentPage > 1;\n\n  const handleSetCurrentPage = (page: number) => {\n    if (page >= 1 && page <= totalPages) {\n      setCurrentPage(page);\n    }\n  };\n\n  const handleSetItemsPerPage = (newItemsPerPage: number) => {\n    setItemsPerPage(newItemsPerPage);\n    // Reset to first page when changing items per page\n    setCurrentPage(1);\n  };\n\n  const goToNextPage = () => {\n    if (hasNextPage) {\n      setCurrentPage(currentPage + 1);\n    }\n  };\n\n  const goToPreviousPage = () => {\n    if (hasPreviousPage) {\n      setCurrentPage(currentPage - 1);\n    }\n  };\n\n  const goToFirstPage = () => {\n    setCurrentPage(1);\n  };\n\n  const goToLastPage = () => {\n    setCurrentPage(totalPages);\n  };\n\n  return {\n    currentPage,\n    totalPages,\n    itemsPerPage,\n    paginatedData,\n    totalItems,\n    setCurrentPage: handleSetCurrentPage,\n    setItemsPerPage: handleSetItemsPerPage,\n    goToNextPage,\n    goToPreviousPage,\n    goToFirstPage,\n    goToLastPage,\n    hasNextPage,\n    hasPreviousPage\n  };\n};\n"], "names": [], "mappings": ";;;AAAA;;;AAuBO,MAAM,gBAAgB;QAAI,EAC/B,IAAI,EACJ,sBAAsB,EAAE,EACF;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,aAAa,KAAK,MAAM;IAC9B,MAAM,aAAa,KAAK,IAAI,CAAC,aAAa;IAE1C,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;gDAAE;YAC5B,MAAM,aAAa,CAAC,cAAc,CAAC,IAAI;YACvC,MAAM,WAAW,aAAa;YAC9B,OAAO,KAAK,KAAK,CAAC,YAAY;QAChC;+CAAG;QAAC;QAAM;QAAa;KAAa;IAEpC,MAAM,cAAc,cAAc;IAClC,MAAM,kBAAkB,cAAc;IAEtC,MAAM,uBAAuB,CAAC;QAC5B,IAAI,QAAQ,KAAK,QAAQ,YAAY;YACnC,eAAe;QACjB;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,gBAAgB;QAChB,mDAAmD;QACnD,eAAe;IACjB;IAEA,MAAM,eAAe;QACnB,IAAI,aAAa;YACf,eAAe,cAAc;QAC/B;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,iBAAiB;YACnB,eAAe,cAAc;QAC/B;IACF;IAEA,MAAM,gBAAgB;QACpB,eAAe;IACjB;IAEA,MAAM,eAAe;QACnB,eAAe;IACjB;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA,gBAAgB;QAChB,iBAAiB;QACjB;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAlEa", "debugId": null}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/components/ui/Badge.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\nimport { BadgeProps } from '@/types';\n\nconst Badge: React.FC<BadgeProps> = ({\n  children,\n  variant,\n  size = 'md',\n  ...props\n}) => {\n  const baseClasses = 'inline-flex items-center font-medium rounded-full';\n  \n  const variants = {\n    primary: 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg',\n    secondary: 'bg-gradient-to-r from-slate-100 to-slate-200 text-slate-800 dark:from-slate-700 dark:to-slate-600 dark:text-slate-200 shadow-md',\n    success: 'bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-lg',\n    warning: 'bg-gradient-to-r from-yellow-500 to-orange-600 text-white shadow-lg',\n    danger: 'bg-gradient-to-r from-red-500 to-pink-600 text-white shadow-lg'\n  };\n\n  const sizes = {\n    sm: 'px-2 py-0.5 text-xs',\n    md: 'px-2.5 py-1 text-sm',\n    lg: 'px-3 py-1.5 text-base'\n  };\n\n  return (\n    <span\n      className={cn(\n        baseClasses,\n        variants[variant],\n        sizes[size]\n      )}\n      {...props}\n    >\n      {children}\n    </span>\n  );\n};\n\nexport default Badge;\n"], "names": [], "mappings": ";;;;AACA;;;AAGA,MAAM,QAA8B;QAAC,EACnC,QAAQ,EACR,OAAO,EACP,OAAO,IAAI,EACX,GAAG,OACJ;IACC,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,SAAS;QACT,QAAQ;IACV;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK;QAEZ,GAAG,KAAK;kBAER;;;;;;AAGP;KAlCM;uCAoCS", "debugId": null}}, {"offset": {"line": 243, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\nimport { ButtonProps } from '@/types';\n\nconst Button: React.FC<ButtonProps> = ({\n  children,\n  variant = 'primary',\n  size = 'md',\n  loading = false,\n  disabled = false,\n  onClick,\n  type = 'button',\n  className,\n  ...props\n}) => {\n  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none';\n  \n  const variants = {\n    primary: 'bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700 focus:ring-blue-500 shadow-lg hover:shadow-xl transform hover:scale-105',\n    secondary: 'bg-gradient-to-r from-slate-100 to-slate-200 text-slate-900 hover:from-slate-200 hover:to-slate-300 focus:ring-slate-500 dark:from-slate-800 dark:to-slate-700 dark:text-slate-100 dark:hover:from-slate-700 dark:hover:to-slate-600 shadow-md hover:shadow-lg',\n    outline: 'border border-slate-300 text-slate-700 hover:bg-white/50 focus:ring-slate-500 dark:border-slate-600 dark:text-slate-300 dark:hover:bg-slate-800/50 backdrop-blur-sm',\n    ghost: 'text-slate-700 hover:bg-white/50 focus:ring-slate-500 dark:text-slate-300 dark:hover:bg-slate-800/50 backdrop-blur-sm'\n  };\n\n  const sizes = {\n    sm: 'px-3 py-1.5 text-sm',\n    md: 'px-4 py-2 text-sm',\n    lg: 'px-6 py-3 text-base'\n  };\n\n  return (\n    <button\n      type={type}\n      onClick={onClick}\n      disabled={disabled || loading}\n      className={cn(\n        baseClasses,\n        variants[variant],\n        sizes[size],\n        className\n      )}\n      {...props}\n    >\n      {loading && (\n        <svg\n          className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n          fill=\"none\"\n          viewBox=\"0 0 24 24\"\n        >\n          <circle\n            className=\"opacity-25\"\n            cx=\"12\"\n            cy=\"12\"\n            r=\"10\"\n            stroke=\"currentColor\"\n            strokeWidth=\"4\"\n          />\n          <path\n            className=\"opacity-75\"\n            fill=\"currentColor\"\n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n          />\n        </svg>\n      )}\n      {children}\n    </button>\n  );\n};\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AACA;;;AAGA,MAAM,SAAgC;QAAC,EACrC,QAAQ,EACR,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,OAAO,EACP,OAAO,QAAQ,EACf,SAAS,EACT,GAAG,OACJ;IACC,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,MAAM;QACN,SAAS;QACT,UAAU,YAAY;QACtB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAED,GAAG,KAAK;;YAER,yBACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;KAhEM;uCAkES", "debugId": null}}, {"offset": {"line": 325, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/components/ui/StarRating.tsx"], "sourcesContent": ["import React from 'react';\nimport { StarIcon } from '@heroicons/react/24/solid';\nimport { StarIcon as StarOutlineIcon } from '@heroicons/react/24/outline';\nimport { cn } from '@/lib/utils';\n\ninterface StarRatingProps {\n  rating: number;\n  maxRating?: number;\n  size?: 'sm' | 'md' | 'lg';\n  showValue?: boolean;\n  interactive?: boolean;\n  onRatingChange?: (rating: number) => void;\n  className?: string;\n}\n\nconst StarRating: React.FC<StarRatingProps> = ({\n  rating,\n  maxRating = 5,\n  size = 'md',\n  showValue = false,\n  interactive = false,\n  onRatingChange,\n  className\n}) => {\n  const sizeClasses = {\n    sm: 'h-4 w-4',\n    md: 'h-5 w-5',\n    lg: 'h-6 w-6'\n  };\n\n  const handleStarClick = (starRating: number) => {\n    if (interactive && onRatingChange) {\n      onRatingChange(starRating);\n    }\n  };\n\n  return (\n    <div className={cn('flex items-center gap-1', className)}>\n      <div className=\"flex\">\n        {Array.from({ length: maxRating }, (_, index) => {\n          const starRating = index + 1;\n          const isFilled = starRating <= rating;\n          \n          return (\n            <button\n              key={index}\n              type=\"button\"\n              onClick={() => handleStarClick(starRating)}\n              disabled={!interactive}\n              className={cn(\n                'transition-colors',\n                interactive ? 'hover:scale-110 cursor-pointer' : 'cursor-default',\n                isFilled ? 'text-yellow-400' : 'text-secondary-300 dark:text-secondary-600'\n              )}\n            >\n              {isFilled ? (\n                <StarIcon className={sizeClasses[size]} />\n              ) : (\n                <StarOutlineIcon className={sizeClasses[size]} />\n              )}\n            </button>\n          );\n        })}\n      </div>\n      \n      {showValue && (\n        <span className=\"ml-2 text-sm text-secondary-600 dark:text-secondary-400\">\n          {rating.toFixed(1)}/{maxRating}\n        </span>\n      )}\n    </div>\n  );\n};\n\nexport default StarRating;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAYA,MAAM,aAAwC;QAAC,EAC7C,MAAM,EACN,YAAY,CAAC,EACb,OAAO,IAAI,EACX,YAAY,KAAK,EACjB,cAAc,KAAK,EACnB,cAAc,EACd,SAAS,EACV;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,eAAe,gBAAgB;YACjC,eAAe;QACjB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;;0BAC5C,6LAAC;gBAAI,WAAU;0BACZ,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAU,GAAG,CAAC,GAAG;oBACrC,MAAM,aAAa,QAAQ;oBAC3B,MAAM,WAAW,cAAc;oBAE/B,qBACE,6LAAC;wBAEC,MAAK;wBACL,SAAS,IAAM,gBAAgB;wBAC/B,UAAU,CAAC;wBACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qBACA,cAAc,mCAAmC,kBACjD,WAAW,oBAAoB;kCAGhC,yBACC,6LAAC,gNAAA,CAAA,WAAQ;4BAAC,WAAW,WAAW,CAAC,KAAK;;;;;qFAEtC,6LAAC,kNAAA,CAAA,WAAe;4BAAC,WAAW,WAAW,CAAC,KAAK;;;;;;uBAb1C;;;;;gBAiBX;;;;;;YAGD,2BACC,6LAAC;gBAAK,WAAU;;oBACb,OAAO,OAAO,CAAC;oBAAG;oBAAE;;;;;;;;;;;;;AAK/B;KAzDM;uCA2DS", "debugId": null}}, {"offset": {"line": 419, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/components/ui/Modal.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { cn } from '@/lib/utils';\nimport { ModalProps } from '@/types';\nimport { XMarkIcon } from '@heroicons/react/24/outline';\n\nconst Modal: React.FC<ModalProps> = ({\n  isOpen,\n  onClose,\n  title,\n  children,\n  size = 'md'\n}) => {\n  useEffect(() => {\n    const handleEscape = (e: KeyboardEvent) => {\n      if (e.key === 'Escape') {\n        onClose();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'hidden';\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen, onClose]);\n\n  if (!isOpen) return null;\n\n  const sizeClasses = {\n    sm: 'max-w-md',\n    md: 'max-w-lg',\n    lg: 'max-w-2xl',\n    xl: 'max-w-4xl'\n  };\n\n  return (\n    <div className=\"fixed inset-0 z-[9999] overflow-y-auto\">\n      <div className=\"flex min-h-screen items-center justify-center p-4\">\n        {/* Backdrop */}\n        <div\n          className=\"fixed inset-0 bg-black/60 backdrop-blur-sm transition-opacity\"\n          onClick={onClose}\n        />\n\n        {/* Modal */}\n        <div\n          className={cn(\n            'relative w-full glass backdrop-blur-xl bg-white/95 dark:bg-slate-900/95 border border-white/20 dark:border-slate-700/50 rounded-2xl shadow-2xl transform transition-all',\n            sizeClasses[size]\n          )}\n        >\n          {/* Header */}\n          <div className=\"flex items-center justify-between p-6 border-b border-gray-200/50 dark:border-gray-700/50\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n              {title}\n            </h3>\n            <button\n              onClick={onClose}\n              className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors p-1 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800\"\n            >\n              <XMarkIcon className=\"h-6 w-6\" />\n            </button>\n          </div>\n\n          {/* Content */}\n          <div className=\"p-6 text-gray-900 dark:text-white\">\n            {children}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Modal;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;;;;;;AAEA,MAAM,QAA8B;QAAC,EACnC,MAAM,EACN,OAAO,EACP,KAAK,EACL,QAAQ,EACR,OAAO,IAAI,EACZ;;IACC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACR,MAAM;gDAAe,CAAC;oBACpB,IAAI,EAAE,GAAG,KAAK,UAAU;wBACtB;oBACF;gBACF;;YAEA,IAAI,QAAQ;gBACV,SAAS,gBAAgB,CAAC,WAAW;gBACrC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;mCAAO;oBACL,SAAS,mBAAmB,CAAC,WAAW;oBACxC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;0BAAG;QAAC;QAAQ;KAAQ;IAEpB,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBACC,WAAU;oBACV,SAAS;;;;;;8BAIX,6LAAC;oBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2KACA,WAAW,CAAC,KAAK;;sCAInB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX;;;;;;8CAEH,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKzB,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb;GAvEM;KAAA;uCAyES", "debugId": null}}, {"offset": {"line": 553, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/components/UserCard.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Image from 'next/image';\nimport { useRouter } from 'next/navigation';\nimport { UserCardProps } from '@/types';\nimport { getPerformanceBadgeVariant, getPerformanceLabel, cn } from '@/lib/utils';\nimport Card from '@/components/ui/Card';\nimport Badge from '@/components/ui/Badge';\nimport Button from '@/components/ui/Button';\nimport StarRating from '@/components/ui/StarRating';\nimport Modal from '@/components/ui/Modal';\nimport {\n  EyeIcon,\n  BookmarkIcon as BookmarkSolidIcon,\n  ArrowUpIcon\n} from '@heroicons/react/24/solid';\nimport {\n  BookmarkIcon as BookmarkOutlineIcon\n} from '@heroicons/react/24/outline';\n\nconst UserCard: React.FC<UserCardProps> = ({\n  employee,\n  onView,\n  onBookmark,\n  onPromote,\n  isBookmarked\n}) => {\n  const router = useRouter();\n  const [showPromoteModal, setShowPromoteModal] = useState(false);\n\n  const handleView = () => {\n    router.push(`/employee/${employee.id}`);\n  };\n\n  const handleBookmark = () => {\n    onBookmark(employee.id);\n  };\n\n  const handlePromote = () => {\n    setShowPromoteModal(true);\n  };\n\n  const confirmPromote = () => {\n    onPromote(employee.id);\n    setShowPromoteModal(false);\n  };\n\n  return (\n    <>\n      <div className=\"glass-card p-6 rounded-3xl hover-lift group relative overflow-hidden animate-fade-in\">\n        {/* Premium Background Effects */}\n        <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500\"></div>\n        <div className=\"absolute top-0 right-0 w-32 h-32 bg-gradient-primary opacity-10 rounded-full blur-3xl group-hover:opacity-20 transition-opacity duration-500\"></div>\n\n        <div className=\"flex flex-col h-full relative z-10\">\n          {/* Bookmark Button */}\n          <button\n            onClick={handleBookmark}\n            className={cn(\n              \"absolute top-4 right-4 z-20 p-3 rounded-2xl transition-all duration-300 hover:scale-110 shadow-lg\",\n              isBookmarked\n                ? \"bg-gradient-warning text-white animate-glow\"\n                : \"glass text-gray-300 hover:bg-gradient-warning hover:text-white\"\n            )}\n          >\n            {isBookmarked ? (\n              <BookmarkSolidIcon className=\"h-5 w-5\" />\n            ) : (\n              <BookmarkOutlineIcon className=\"h-5 w-5\" />\n            )}\n          </button>\n\n          {/* Premium Profile Section */}\n          <div className=\"flex flex-col items-center mb-6\">\n            <div className=\"relative mb-4\">\n              <div className=\"w-24 h-24 rounded-3xl overflow-hidden border-4 border-white/20 shadow-2xl group-hover:scale-105 transition-transform duration-300\">\n                <Image\n                  src={employee.image}\n                  alt={`${employee.firstName} ${employee.lastName}`}\n                  fill\n                  className=\"object-cover\"\n                  sizes=\"96px\"\n                />\n              </div>\n              <div className=\"absolute -bottom-2 -right-2 w-8 h-8 bg-gradient-success rounded-full border-4 border-white/20 flex items-center justify-center animate-pulse\">\n                <div className=\"w-3 h-3 bg-white rounded-full\"></div>\n              </div>\n            </div>\n\n            <div className=\"text-center\">\n              <h3 className=\"text-xl font-bold text-premium mb-2\">\n                {employee.firstName} {employee.lastName}\n              </h3>\n              <p className=\"text-gray-400 font-medium mb-1 text-sm\">\n                {employee.email}\n              </p>\n              <div className=\"flex items-center justify-center space-x-3 mb-3\">\n                <span className=\"text-xs text-gray-400 bg-white/10 px-3 py-1 rounded-full font-medium\">\n                  Age: {employee.age}\n                </span>\n                <Badge variant=\"secondary\" size=\"sm\" className=\"bg-gradient-primary text-white border-0 font-bold\">\n                  {employee.department}\n                </Badge>\n              </div>\n            </div>\n          </div>\n\n          {/* Premium Performance Metrics */}\n          <div className=\"space-y-4 mb-6\">\n            <div className=\"glass p-4 rounded-2xl\">\n              <div className=\"flex items-center justify-between mb-3\">\n                <span className=\"text-sm font-semibold text-gray-300 uppercase tracking-wide\">\n                  Performance Rating\n                </span>\n                <Badge\n                  variant={getPerformanceBadgeVariant(employee.performanceRating)}\n                  size=\"sm\"\n                  className=\"shadow-lg font-bold\"\n                >\n                  {getPerformanceLabel(employee.performanceRating)}\n                </Badge>\n              </div>\n              <div className=\"bg-gradient-to-r from-white/5 to-white/10 p-4 rounded-xl border border-white/10\">\n                <StarRating rating={employee.performanceRating} showValue />\n              </div>\n            </div>\n          </div>\n\n          {/* Premium Action Buttons */}\n          <div className=\"flex space-x-3 mt-auto\">\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={handleView}\n              className=\"flex-1 glass border-white/20 text-gray-300 hover:text-white hover:bg-white/10 rounded-2xl py-3 font-semibold transition-all duration-300\"\n            >\n              <EyeIcon className=\"h-4 w-4 mr-2\" />\n              View Profile\n            </Button>\n\n            <Button\n              variant=\"secondary\"\n              size=\"sm\"\n              onClick={handlePromote}\n              className=\"flex-1 bg-gradient-success hover:shadow-2xl rounded-2xl py-3 font-semibold transition-all duration-300 hover:scale-105\"\n            >\n              <ArrowUpIcon className=\"h-4 w-4 mr-2\" />\n              Promote\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Promote Modal */}\n      <Modal\n        isOpen={showPromoteModal}\n        onClose={() => setShowPromoteModal(false)}\n        title=\"Promote Employee\"\n        size=\"md\"\n      >\n        <div className=\"space-y-4\">\n          <p className=\"text-gray-700 dark:text-gray-300\">\n            Are you sure you want to promote{' '}\n            <span className=\"font-semibold text-gray-900 dark:text-white\">\n              {employee.firstName} {employee.lastName}\n            </span>\n            ?\n          </p>\n\n          <div className=\"bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 p-4 rounded-xl border border-blue-200/50 dark:border-blue-700/50\">\n            <h4 className=\"font-medium text-gray-900 dark:text-white mb-3 flex items-center\">\n              <div className=\"w-2 h-2 bg-blue-500 rounded-full mr-2\"></div>\n              Current Details:\n            </h4>\n            <ul className=\"text-sm text-gray-700 dark:text-gray-300 space-y-2\">\n              <li className=\"flex items-center\">\n                <span className=\"font-medium text-blue-600 dark:text-blue-400 w-20\">Department:</span>\n                <span className=\"bg-blue-100 dark:bg-blue-900/50 px-2 py-1 rounded-lg text-blue-800 dark:text-blue-200\">{employee.department}</span>\n              </li>\n              <li className=\"flex items-center\">\n                <span className=\"font-medium text-purple-600 dark:text-purple-400 w-20\">Rating:</span>\n                <span className=\"bg-purple-100 dark:bg-purple-900/50 px-2 py-1 rounded-lg text-purple-800 dark:text-purple-200\">{employee.performanceRating}/5</span>\n              </li>\n              <li className=\"flex items-center\">\n                <span className=\"font-medium text-green-600 dark:text-green-400 w-20\">Email:</span>\n                <span className=\"bg-green-100 dark:bg-green-900/50 px-2 py-1 rounded-lg text-green-800 dark:text-green-200 text-xs\">{employee.email}</span>\n              </li>\n            </ul>\n          </div>\n          \n          <div className=\"flex space-x-3 pt-6 border-t border-gray-200/50 dark:border-gray-700/50\">\n            <Button\n              variant=\"outline\"\n              onClick={() => setShowPromoteModal(false)}\n              className=\"flex-1 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800\"\n            >\n              Cancel\n            </Button>\n            <Button\n              variant=\"primary\"\n              onClick={confirmPromote}\n              className=\"flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold\"\n            >\n              Confirm Promotion\n            </Button>\n          </div>\n        </div>\n      </Modal>\n    </>\n  );\n};\n\nexport default UserCard;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAKA;;;AAjBA;;;;;;;;;;;AAqBA,MAAM,WAAoC;QAAC,EACzC,QAAQ,EACR,MAAM,EACN,UAAU,EACV,SAAS,EACT,YAAY,EACb;;IACC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,aAAa;QACjB,OAAO,IAAI,CAAC,AAAC,aAAwB,OAAZ,SAAS,EAAE;IACtC;IAEA,MAAM,iBAAiB;QACrB,WAAW,SAAS,EAAE;IACxB;IAEA,MAAM,gBAAgB;QACpB,oBAAoB;IACtB;IAEA,MAAM,iBAAiB;QACrB,UAAU,SAAS,EAAE;QACrB,oBAAoB;IACtB;IAEA,qBACE;;0BACE,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCAEf,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCACC,SAAS;gCACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qGACA,eACI,gDACA;0CAGL,6BACC,6LAAC,wNAAA,CAAA,eAAiB;oCAAC,WAAU;;;;;6FAE7B,6LAAC,0NAAA,CAAA,eAAmB;oCAAC,WAAU;;;;;;;;;;;0CAKnC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAK,SAAS,KAAK;oDACnB,KAAK,AAAC,GAAwB,OAAtB,SAAS,SAAS,EAAC,KAAqB,OAAlB,SAAS,QAAQ;oDAC/C,IAAI;oDACJ,WAAU;oDACV,OAAM;;;;;;;;;;;0DAGV,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;kDAInB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;;oDACX,SAAS,SAAS;oDAAC;oDAAE,SAAS,QAAQ;;;;;;;0DAEzC,6LAAC;gDAAE,WAAU;0DACV,SAAS,KAAK;;;;;;0DAEjB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;;4DAAuE;4DAC/E,SAAS,GAAG;;;;;;;kEAEpB,6LAAC,oIAAA,CAAA,UAAK;wDAAC,SAAQ;wDAAY,MAAK;wDAAK,WAAU;kEAC5C,SAAS,UAAU;;;;;;;;;;;;;;;;;;;;;;;;0CAO5B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAA8D;;;;;;8DAG9E,6LAAC,oIAAA,CAAA,UAAK;oDACJ,SAAS,CAAA,GAAA,sHAAA,CAAA,6BAA0B,AAAD,EAAE,SAAS,iBAAiB;oDAC9D,MAAK;oDACL,WAAU;8DAET,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS,iBAAiB;;;;;;;;;;;;sDAGnD,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,yIAAA,CAAA,UAAU;gDAAC,QAAQ,SAAS,iBAAiB;gDAAE,SAAS;;;;;;;;;;;;;;;;;;;;;;0CAM/D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,UAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,6LAAC,8MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAItC,6LAAC,qIAAA,CAAA,UAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,6LAAC,sNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhD,6LAAC,oIAAA,CAAA,UAAK;gBACJ,QAAQ;gBACR,SAAS,IAAM,oBAAoB;gBACnC,OAAM;gBACN,MAAK;0BAEL,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;;gCAAmC;gCACb;8CACjC,6LAAC;oCAAK,WAAU;;wCACb,SAAS,SAAS;wCAAC;wCAAE,SAAS,QAAQ;;;;;;;gCAClC;;;;;;;sCAIT,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAI,WAAU;;;;;;wCAA8C;;;;;;;8CAG/D,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAAoD;;;;;;8DACpE,6LAAC;oDAAK,WAAU;8DAAyF,SAAS,UAAU;;;;;;;;;;;;sDAE9H,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAAwD;;;;;;8DACxE,6LAAC;oDAAK,WAAU;;wDAAiG,SAAS,iBAAiB;wDAAC;;;;;;;;;;;;;sDAE9I,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAAsD;;;;;;8DACtE,6LAAC;oDAAK,WAAU;8DAAqG,SAAS,KAAK;;;;;;;;;;;;;;;;;;;;;;;;sCAKzI,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,UAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,oBAAoB;oCACnC,WAAU;8CACX;;;;;;8CAGD,6LAAC,qIAAA,CAAA,UAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GA9LM;;QAOW,qIAAA,CAAA,YAAS;;;KAPpB;uCAgMS", "debugId": null}}, {"offset": {"line": 1077, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface CardProps {\n  children: React.ReactNode;\n  className?: string;\n  padding?: 'none' | 'sm' | 'md' | 'lg';\n  hover?: boolean;\n}\n\nconst Card: React.FC<CardProps> = ({\n  children,\n  className,\n  padding = 'md',\n  hover = false,\n  ...props\n}) => {\n  const baseClasses = 'glass backdrop-blur-xl bg-white/80 dark:bg-slate-900/80 border border-white/20 dark:border-slate-700/50 rounded-2xl shadow-xl';\n\n  const paddingClasses = {\n    none: '',\n    sm: 'p-4',\n    md: 'p-6',\n    lg: 'p-8'\n  };\n\n  const hoverClasses = hover ? 'hover:shadow-2xl hover:transform hover:scale-105 transition-all duration-300' : '';\n\n  return (\n    <div\n      className={cn(\n        baseClasses,\n        paddingClasses[padding],\n        hoverClasses,\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n};\n\nexport default Card;\n"], "names": [], "mappings": ";;;;AACA;;;AASA,MAAM,OAA4B;QAAC,EACjC,QAAQ,EACR,SAAS,EACT,UAAU,IAAI,EACd,QAAQ,KAAK,EACb,GAAG,OACJ;IACC,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,MAAM;QACN,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,eAAe,QAAQ,iFAAiF;IAE9G,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,aACA,cAAc,CAAC,QAAQ,EACvB,cACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;KA/BM;uCAiCS", "debugId": null}}, {"offset": {"line": 1117, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/components/SearchAndFilters.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { MagnifyingGlassIcon, FunnelIcon, XMarkIcon } from '@heroicons/react/24/outline';\nimport { SearchFilters } from '@/types';\nimport Button from '@/components/ui/Button';\nimport Card from '@/components/ui/Card';\nimport { Listbox, Transition } from '@headlessui/react';\nimport { ChevronUpDownIcon, CheckIcon } from '@heroicons/react/20/solid';\n\ninterface SearchAndFiltersProps {\n  filters: SearchFilters;\n  onQueryChange: (query: string) => void;\n  onDepartmentsChange: (departments: string[]) => void;\n  onRatingRangeChange: (minRating: number, maxRating: number) => void;\n  onClearFilters: () => void;\n  availableDepartments: string[];\n}\n\nconst SearchAndFilters: React.FC<SearchAndFiltersProps> = ({\n  filters,\n  onQueryChange,\n  onDepartmentsChange,\n  onRatingRangeChange,\n  onClearFilters,\n  availableDepartments\n}) => {\n  const [showFilters, setShowFilters] = useState(false);\n\n  const hasActiveFilters = filters.departments.length > 0 || filters.minRating > 0 || filters.maxRating < 5;\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Premium Search Bar */}\n      <div className=\"glass-card p-6 rounded-3xl animate-slide-up\">\n        <div className=\"flex items-center space-x-6\">\n          <div className=\"flex-1 relative\">\n            <div className=\"absolute left-6 top-1/2 transform -translate-y-1/2 w-6 h-6 bg-gradient-primary rounded-full flex items-center justify-center\">\n              <MagnifyingGlassIcon className=\"h-4 w-4 text-white\" />\n            </div>\n            <input\n              type=\"text\"\n              placeholder=\"Search elite professionals by name, email, or department...\"\n              value={filters.query}\n              onChange={(e) => onQueryChange(e.target.value)}\n              className=\"w-full pl-16 pr-6 py-4 border-0 rounded-2xl glass text-white placeholder-gray-400 focus:ring-2 focus:ring-purple-500/50 focus:bg-white/10 transition-all duration-300 text-lg font-medium shadow-inner backdrop-blur-xl\"\n            />\n          </div>\n\n          <Button\n            variant=\"outline\"\n            onClick={() => setShowFilters(!showFilters)}\n            className={`relative px-6 py-3 rounded-2xl transition-all duration-300 hover-lift font-semibold ${\n              hasActiveFilters\n                ? 'bg-gradient-primary text-white shadow-2xl animate-glow'\n                : 'glass border-white/20 text-gray-300 hover:text-white hover:bg-white/10'\n            }`}\n          >\n            <div className={`p-2 rounded-xl mr-3 ${hasActiveFilters ? 'bg-white/20' : 'bg-white/10'}`}>\n              <FunnelIcon className=\"h-5 w-5\" />\n            </div>\n            <span className=\"text-base\">\n              {hasActiveFilters ? `Active Filters (${filters.departments.length + (filters.minRating > 0 || filters.maxRating < 5 ? 1 : 0)})` : 'Advanced Filters'}\n            </span>\n            {hasActiveFilters && (\n              <div className=\"absolute -top-2 -right-2 w-6 h-6 bg-gradient-warning rounded-full animate-pulse flex items-center justify-center\">\n                <span className=\"text-xs font-bold text-white\">{filters.departments.length + (filters.minRating > 0 || filters.maxRating < 5 ? 1 : 0)}</span>\n              </div>\n            )}\n          </Button>\n\n          {hasActiveFilters && (\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={onClearFilters}\n              className=\"glass border-red-500/30 text-red-400 hover:text-red-300 hover:bg-red-500/10 rounded-2xl px-4 py-3 font-semibold transition-all duration-300\"\n            >\n              <XMarkIcon className=\"h-4 w-4 mr-2\" />\n              Reset All\n            </Button>\n          )}\n        </div>\n      </div>\n\n      {/* Filters Panel */}\n      <Transition\n        show={showFilters}\n        enter=\"transition ease-out duration-200\"\n        enterFrom=\"opacity-0 scale-95\"\n        enterTo=\"opacity-100 scale-100\"\n        leave=\"transition ease-in duration-150\"\n        leaveFrom=\"opacity-100 scale-100\"\n        leaveTo=\"opacity-0 scale-95\"\n      >\n        <Card className=\"relative z-[100]\">\n          <div className=\"space-y-6\">\n            <div className=\"flex items-center justify-between\">\n              <h3 className=\"text-lg font-medium text-secondary-900 dark:text-white\">\n                Filters\n              </h3>\n              {hasActiveFilters && (\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={onClearFilters}\n                >\n                  <XMarkIcon className=\"h-4 w-4 mr-1\" />\n                  Clear All\n                </Button>\n              )}\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {/* Department Filter */}\n              <div>\n                <label className=\"block text-sm font-semibold text-secondary-800 dark:text-white mb-3\">\n                  Departments\n                </label>\n                <Listbox value={filters.departments} onChange={onDepartmentsChange} multiple>\n                  <div className=\"relative z-[200]\">\n                    <Listbox.Button className=\"relative w-full cursor-default rounded-lg bg-white dark:bg-gray-800 py-3 pl-4 pr-10 text-left border-2 border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\">\n                      <span className=\"block truncate text-gray-900 dark:text-gray-100 font-semibold\">\n                        {filters.departments.length === 0\n                          ? 'All Departments'\n                          : `${filters.departments.length} selected`}\n                      </span>\n                      <span className=\"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3\">\n                        <ChevronUpDownIcon className=\"h-5 w-5 text-gray-600 dark:text-gray-300\" />\n                      </span>\n                    </Listbox.Button>\n                    <Transition\n                      leave=\"transition ease-in duration-100\"\n                      leaveFrom=\"opacity-100\"\n                      leaveTo=\"opacity-0\"\n                    >\n                      <Listbox.Options className=\"absolute z-[9999] mt-2 max-h-60 w-full overflow-auto rounded-lg bg-white dark:bg-gray-900 py-2 shadow-2xl ring-1 ring-black ring-opacity-10 focus:outline-none border-2 border-gray-300 dark:border-gray-600\">\n                        {availableDepartments.map((department) => (\n                          <Listbox.Option\n                            key={department}\n                            value={department}\n                            className={({ active }) =>\n                              `relative cursor-pointer select-none py-3 pl-10 pr-4 transition-colors ${\n                                active\n                                  ? 'bg-blue-100 text-blue-900 dark:bg-blue-800 dark:text-blue-100'\n                                  : 'text-gray-900 dark:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-800'\n                              }`\n                            }\n                          >\n                            {({ selected }) => (\n                              <>\n                                <span className={`block truncate ${selected ? 'font-bold text-blue-600 dark:text-blue-300' : 'font-medium text-gray-900 dark:text-gray-100'}`}>\n                                  {department}\n                                </span>\n                                {selected && (\n                                  <span className=\"absolute inset-y-0 left-0 flex items-center pl-3 text-blue-600 dark:text-blue-300\">\n                                    <CheckIcon className=\"h-5 w-5\" />\n                                  </span>\n                                )}\n                              </>\n                            )}\n                          </Listbox.Option>\n                        ))}\n                      </Listbox.Options>\n                    </Transition>\n                  </div>\n                </Listbox>\n              </div>\n\n              {/* Rating Range Filter */}\n              <div>\n                <label className=\"block text-sm font-semibold text-secondary-800 dark:text-white mb-3\">\n                  Performance Rating Range\n                </label>\n                <div className=\"space-y-3\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2\">\n                      Minimum Rating: <span className=\"font-bold text-blue-600 dark:text-blue-400\">{filters.minRating}</span>\n                    </label>\n                    <input\n                      type=\"range\"\n                      min=\"0\"\n                      max=\"5\"\n                      step=\"1\"\n                      value={filters.minRating}\n                      onChange={(e) => onRatingRangeChange(Number(e.target.value), filters.maxRating)}\n                      className=\"w-full h-3 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer slider\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2\">\n                      Maximum Rating: <span className=\"font-bold text-blue-600 dark:text-blue-400\">{filters.maxRating}</span>\n                    </label>\n                    <input\n                      type=\"range\"\n                      min=\"0\"\n                      max=\"5\"\n                      step=\"1\"\n                      value={filters.maxRating}\n                      onChange={(e) => onRatingRangeChange(filters.minRating, Number(e.target.value))}\n                      className=\"w-full h-3 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer slider\"\n                    />\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </Card>\n      </Transition>\n\n      {/* Active Filters Display */}\n      {hasActiveFilters && (\n        <div className=\"flex flex-wrap gap-2\">\n          {filters.departments.map((dept) => (\n            <span\n              key={dept}\n              className=\"inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200\"\n            >\n              {dept}\n              <button\n                onClick={() => onDepartmentsChange(filters.departments.filter(d => d !== dept))}\n                className=\"ml-2 hover:text-primary-600 dark:hover:text-primary-400\"\n              >\n                <XMarkIcon className=\"h-4 w-4\" />\n              </button>\n            </span>\n          ))}\n          \n          {(filters.minRating > 0 || filters.maxRating < 5) && (\n            <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm bg-secondary-100 text-secondary-800 dark:bg-secondary-800 dark:text-secondary-200\">\n              Rating: {filters.minRating}-{filters.maxRating}\n              <button\n                onClick={() => onRatingRangeChange(0, 5)}\n                className=\"ml-2 hover:text-secondary-600 dark:hover:text-secondary-400\"\n              >\n                <XMarkIcon className=\"h-4 w-4\" />\n              </button>\n            </span>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default SearchAndFilters;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AAAA;AACA;AAAA;;;AARA;;;;;;;AAmBA,MAAM,mBAAoD;QAAC,EACzD,OAAO,EACP,aAAa,EACb,mBAAmB,EACnB,mBAAmB,EACnB,cAAc,EACd,oBAAoB,EACrB;;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,mBAAmB,QAAQ,WAAW,CAAC,MAAM,GAAG,KAAK,QAAQ,SAAS,GAAG,KAAK,QAAQ,SAAS,GAAG;IAExG,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,wOAAA,CAAA,sBAAmB;wCAAC,WAAU;;;;;;;;;;;8CAEjC,6LAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO,QAAQ,KAAK;oCACpB,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,WAAU;;;;;;;;;;;;sCAId,6LAAC,qIAAA,CAAA,UAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,eAAe,CAAC;4BAC/B,WAAW,AAAC,uFAIX,OAHC,mBACI,2DACA;;8CAGN,6LAAC;oCAAI,WAAW,AAAC,uBAAuE,OAAjD,mBAAmB,gBAAgB;8CACxE,cAAA,6LAAC,sNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;8CAExB,6LAAC;oCAAK,WAAU;8CACb,mBAAmB,AAAC,mBAAwG,OAAtF,QAAQ,WAAW,CAAC,MAAM,GAAG,CAAC,QAAQ,SAAS,GAAG,KAAK,QAAQ,SAAS,GAAG,IAAI,IAAI,CAAC,GAAE,OAAK;;;;;;gCAEnI,kCACC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAAgC,QAAQ,WAAW,CAAC,MAAM,GAAG,CAAC,QAAQ,SAAS,GAAG,KAAK,QAAQ,SAAS,GAAG,IAAI,IAAI,CAAC;;;;;;;;;;;;;;;;;wBAKzI,kCACC,6LAAC,qIAAA,CAAA,UAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;;8CAEV,6LAAC,oNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;0BAQ9C,6LAAC,0LAAA,CAAA,aAAU;gBACT,MAAM;gBACN,OAAM;gBACN,WAAU;gBACV,SAAQ;gBACR,OAAM;gBACN,WAAU;gBACV,SAAQ;0BAER,cAAA,6LAAC,mIAAA,CAAA,UAAI;oBAAC,WAAU;8BACd,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAyD;;;;;;oCAGtE,kCACC,6LAAC,qIAAA,CAAA,UAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;;0DAET,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;0CAM5C,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAsE;;;;;;0DAGvF,6LAAC,oLAAA,CAAA,UAAO;gDAAC,OAAO,QAAQ,WAAW;gDAAE,UAAU;gDAAqB,QAAQ;0DAC1E,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oLAAA,CAAA,UAAO,CAAC,MAAM;4DAAC,WAAU;;8EACxB,6LAAC;oEAAK,WAAU;8EACb,QAAQ,WAAW,CAAC,MAAM,KAAK,IAC5B,oBACA,AAAC,GAA6B,OAA3B,QAAQ,WAAW,CAAC,MAAM,EAAC;;;;;;8EAEpC,6LAAC;oEAAK,WAAU;8EACd,cAAA,6LAAC,kOAAA,CAAA,oBAAiB;wEAAC,WAAU;;;;;;;;;;;;;;;;;sEAGjC,6LAAC,0LAAA,CAAA,aAAU;4DACT,OAAM;4DACN,WAAU;4DACV,SAAQ;sEAER,cAAA,6LAAC,oLAAA,CAAA,UAAO,CAAC,OAAO;gEAAC,WAAU;0EACxB,qBAAqB,GAAG,CAAC,CAAC,2BACzB,6LAAC,oLAAA,CAAA,UAAO,CAAC,MAAM;wEAEb,OAAO;wEACP,WAAW;gFAAC,EAAE,MAAM,EAAE;mFACpB,AAAC,yEAIA,OAHC,SACI,kEACA;;kFAIP;gFAAC,EAAE,QAAQ,EAAE;iGACZ;;kGACE,6LAAC;wFAAK,WAAW,AAAC,kBAA0H,OAAzG,WAAW,+CAA+C;kGAC1F;;;;;;oFAEF,0BACC,6LAAC;wFAAK,WAAU;kGACd,cAAA,6LAAC,kNAAA,CAAA,YAAS;4FAAC,WAAU;;;;;;;;;;;;;;uEAjBxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDA+BnB,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAsE;;;;;;0DAGvF,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;;oEAAkE;kFACjE,6LAAC;wEAAK,WAAU;kFAA8C,QAAQ,SAAS;;;;;;;;;;;;0EAEjG,6LAAC;gEACC,MAAK;gEACL,KAAI;gEACJ,KAAI;gEACJ,MAAK;gEACL,OAAO,QAAQ,SAAS;gEACxB,UAAU,CAAC,IAAM,oBAAoB,OAAO,EAAE,MAAM,CAAC,KAAK,GAAG,QAAQ,SAAS;gEAC9E,WAAU;;;;;;;;;;;;kEAGd,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;;oEAAkE;kFACjE,6LAAC;wEAAK,WAAU;kFAA8C,QAAQ,SAAS;;;;;;;;;;;;0EAEjG,6LAAC;gEACC,MAAK;gEACL,KAAI;gEACJ,KAAI;gEACJ,MAAK;gEACL,OAAO,QAAQ,SAAS;gEACxB,UAAU,CAAC,IAAM,oBAAoB,QAAQ,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gEAC7E,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAWzB,kCACC,6LAAC;gBAAI,WAAU;;oBACZ,QAAQ,WAAW,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;4BAEC,WAAU;;gCAET;8CACD,6LAAC;oCACC,SAAS,IAAM,oBAAoB,QAAQ,WAAW,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM;oCACzE,WAAU;8CAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;2BARlB;;;;;oBAaR,CAAC,QAAQ,SAAS,GAAG,KAAK,QAAQ,SAAS,GAAG,CAAC,mBAC9C,6LAAC;wBAAK,WAAU;;4BAA4I;4BACjJ,QAAQ,SAAS;4BAAC;4BAAE,QAAQ,SAAS;0CAC9C,6LAAC;gCACC,SAAS,IAAM,oBAAoB,GAAG;gCACtC,WAAU;0CAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrC;GAhOM;KAAA;uCAkOS", "debugId": null}}, {"offset": {"line": 1647, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/components/CreateUserModal.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Modal from '@/components/ui/Modal';\nimport Button from '@/components/ui/Button';\nimport { Employee } from '@/types';\nimport { UserPlusIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';\n\ninterface CreateUserModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onCreateUser: (user: Partial<Employee>) => void;\n}\n\ninterface FormData {\n  firstName: string;\n  lastName: string;\n  email: string;\n  phone: string;\n  department: string;\n  performanceRating: number;\n  bio: string;\n  age: number;\n  address: {\n    street: string;\n    city: string;\n    state: string;\n    zipCode: string;\n  };\n}\n\ninterface FormErrors {\n  [key: string]: string;\n}\n\nconst departments = [\n  'Engineering',\n  'Human Resources',\n  'Finance',\n  'Marketing',\n  'Sales',\n  'Customer Support',\n  'Operations',\n  'Design'\n];\n\nconst CreateUserModal: React.FC<CreateUserModalProps> = ({\n  isOpen,\n  onClose,\n  onCreateUser\n}) => {\n  const [formData, setFormData] = useState<FormData>({\n    firstName: '',\n    lastName: '',\n    email: '',\n    phone: '',\n    department: '',\n    performanceRating: 3,\n    bio: '',\n    age: 25,\n    address: {\n      street: '',\n      city: '',\n      state: '',\n      zipCode: ''\n    }\n  });\n\n  const [errors, setErrors] = useState<FormErrors>({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const validateForm = (): boolean => {\n    const newErrors: FormErrors = {};\n\n    // Required field validation\n    if (!formData.firstName.trim()) newErrors.firstName = 'First name is required';\n    if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required';\n    if (!formData.email.trim()) newErrors.email = 'Email is required';\n    if (!formData.phone.trim()) newErrors.phone = 'Phone is required';\n    if (!formData.department) newErrors.department = 'Department is required';\n    if (!formData.address.city.trim()) newErrors.city = 'City is required';\n    if (!formData.address.state.trim()) newErrors.state = 'State is required';\n\n    // Email validation\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    if (formData.email && !emailRegex.test(formData.email)) {\n      newErrors.email = 'Please enter a valid email address';\n    }\n\n    // Phone validation\n    const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/;\n    if (formData.phone && !phoneRegex.test(formData.phone.replace(/[\\s\\-\\(\\)]/g, ''))) {\n      newErrors.phone = 'Please enter a valid phone number';\n    }\n\n    // Age validation\n    if (formData.age < 18 || formData.age > 100) {\n      newErrors.age = 'Age must be between 18 and 100';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!validateForm()) return;\n\n    setIsSubmitting(true);\n\n    // Simulate API call\n    await new Promise(resolve => setTimeout(resolve, 1000));\n\n    const newUser: Partial<Employee> = {\n      id: Date.now(), // Mock ID\n      firstName: formData.firstName,\n      lastName: formData.lastName,\n      email: formData.email,\n      phone: formData.phone,\n      department: formData.department,\n      performanceRating: formData.performanceRating,\n      bio: formData.bio || `${formData.firstName} is a dedicated professional in the ${formData.department} department.`,\n      age: formData.age,\n      address: formData.address,\n      image: `https://dummyjson.com/icon/${formData.firstName.toLowerCase()}/128`,\n      projects: [],\n      feedback: [],\n      performanceHistory: [\n        {\n          quarter: 'Q1 2024',\n          rating: formData.performanceRating,\n          goals: ['Complete onboarding', 'Learn team processes'],\n          achievements: ['Successfully integrated with team', 'Completed initial training']\n        }\n      ]\n    };\n\n    onCreateUser(newUser);\n    setIsSubmitting(false);\n    handleClose();\n  };\n\n  const handleClose = () => {\n    setFormData({\n      firstName: '',\n      lastName: '',\n      email: '',\n      phone: '',\n      department: '',\n      performanceRating: 3,\n      bio: '',\n      age: 25,\n      address: {\n        street: '',\n        city: '',\n        state: '',\n        zipCode: ''\n      }\n    });\n    setErrors({});\n    onClose();\n  };\n\n  const updateFormData = (field: string, value: any) => {\n    if (field.includes('.')) {\n      const [parent, child] = field.split('.');\n      setFormData(prev => ({\n        ...prev,\n        [parent]: {\n          ...prev[parent as keyof FormData],\n          [child]: value\n        }\n      }));\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        [field]: value\n      }));\n    }\n    \n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: ''\n      }));\n    }\n  };\n\n  return (\n    <Modal\n      isOpen={isOpen}\n      onClose={handleClose}\n      title=\"Create New Employee\"\n      size=\"lg\"\n    >\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        {/* Personal Information */}\n        <div className=\"bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 p-4 rounded-xl border border-blue-200/50 dark:border-blue-700/50\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n            <UserPlusIcon className=\"h-5 w-5 mr-2 text-blue-600 dark:text-blue-400\" />\n            Personal Information\n          </h3>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                First Name *\n              </label>\n              <input\n                type=\"text\"\n                value={formData.firstName}\n                onChange={(e) => updateFormData('firstName', e.target.value)}\n                className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                  errors.firstName ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                }`}\n                placeholder=\"Enter first name\"\n              />\n              {errors.firstName && (\n                <p className=\"text-red-500 text-xs mt-1 flex items-center\">\n                  <ExclamationTriangleIcon className=\"h-3 w-3 mr-1\" />\n                  {errors.firstName}\n                </p>\n              )}\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                Last Name *\n              </label>\n              <input\n                type=\"text\"\n                value={formData.lastName}\n                onChange={(e) => updateFormData('lastName', e.target.value)}\n                className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                  errors.lastName ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                }`}\n                placeholder=\"Enter last name\"\n              />\n              {errors.lastName && (\n                <p className=\"text-red-500 text-xs mt-1 flex items-center\">\n                  <ExclamationTriangleIcon className=\"h-3 w-3 mr-1\" />\n                  {errors.lastName}\n                </p>\n              )}\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                Email *\n              </label>\n              <input\n                type=\"email\"\n                value={formData.email}\n                onChange={(e) => updateFormData('email', e.target.value)}\n                className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                  errors.email ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                }`}\n                placeholder=\"Enter email address\"\n              />\n              {errors.email && (\n                <p className=\"text-red-500 text-xs mt-1 flex items-center\">\n                  <ExclamationTriangleIcon className=\"h-3 w-3 mr-1\" />\n                  {errors.email}\n                </p>\n              )}\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                Phone *\n              </label>\n              <input\n                type=\"tel\"\n                value={formData.phone}\n                onChange={(e) => updateFormData('phone', e.target.value)}\n                className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                  errors.phone ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                }`}\n                placeholder=\"Enter phone number\"\n              />\n              {errors.phone && (\n                <p className=\"text-red-500 text-xs mt-1 flex items-center\">\n                  <ExclamationTriangleIcon className=\"h-3 w-3 mr-1\" />\n                  {errors.phone}\n                </p>\n              )}\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                Age *\n              </label>\n              <input\n                type=\"number\"\n                min=\"18\"\n                max=\"100\"\n                value={formData.age}\n                onChange={(e) => updateFormData('age', parseInt(e.target.value) || 25)}\n                className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                  errors.age ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                }`}\n              />\n              {errors.age && (\n                <p className=\"text-red-500 text-xs mt-1 flex items-center\">\n                  <ExclamationTriangleIcon className=\"h-3 w-3 mr-1\" />\n                  {errors.age}\n                </p>\n              )}\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                Department *\n              </label>\n              <select\n                value={formData.department}\n                onChange={(e) => updateFormData('department', e.target.value)}\n                className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                  errors.department ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                }`}\n              >\n                <option value=\"\">Select department</option>\n                {departments.map(dept => (\n                  <option key={dept} value={dept}>{dept}</option>\n                ))}\n              </select>\n              {errors.department && (\n                <p className=\"text-red-500 text-xs mt-1 flex items-center\">\n                  <ExclamationTriangleIcon className=\"h-3 w-3 mr-1\" />\n                  {errors.department}\n                </p>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Performance Rating */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            Initial Performance Rating: {formData.performanceRating}/5\n          </label>\n          <input\n            type=\"range\"\n            min=\"1\"\n            max=\"5\"\n            step=\"0.1\"\n            value={formData.performanceRating}\n            onChange={(e) => updateFormData('performanceRating', parseFloat(e.target.value))}\n            className=\"w-full h-3 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer slider\"\n          />\n          <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\n            <span>Poor (1)</span>\n            <span>Excellent (5)</span>\n          </div>\n        </div>\n\n        {/* Bio */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n            Bio (Optional)\n          </label>\n          <textarea\n            value={formData.bio}\n            onChange={(e) => updateFormData('bio', e.target.value)}\n            rows={3}\n            className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            placeholder=\"Enter a brief bio (optional)\"\n          />\n        </div>\n\n        {/* Address */}\n        <div className=\"bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 p-4 rounded-xl border border-green-200/50 dark:border-green-700/50\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            Address Information\n          </h3>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"md:col-span-2\">\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                Street Address\n              </label>\n              <input\n                type=\"text\"\n                value={formData.address.street}\n                onChange={(e) => updateFormData('address.street', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                placeholder=\"Enter street address\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                City *\n              </label>\n              <input\n                type=\"text\"\n                value={formData.address.city}\n                onChange={(e) => updateFormData('address.city', e.target.value)}\n                className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                  errors.city ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                }`}\n                placeholder=\"Enter city\"\n              />\n              {errors.city && (\n                <p className=\"text-red-500 text-xs mt-1 flex items-center\">\n                  <ExclamationTriangleIcon className=\"h-3 w-3 mr-1\" />\n                  {errors.city}\n                </p>\n              )}\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                State *\n              </label>\n              <input\n                type=\"text\"\n                value={formData.address.state}\n                onChange={(e) => updateFormData('address.state', e.target.value)}\n                className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                  errors.state ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                }`}\n                placeholder=\"Enter state\"\n              />\n              {errors.state && (\n                <p className=\"text-red-500 text-xs mt-1 flex items-center\">\n                  <ExclamationTriangleIcon className=\"h-3 w-3 mr-1\" />\n                  {errors.state}\n                </p>\n              )}\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                ZIP Code\n              </label>\n              <input\n                type=\"text\"\n                value={formData.address.zipCode}\n                onChange={(e) => updateFormData('address.zipCode', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                placeholder=\"Enter ZIP code\"\n              />\n            </div>\n          </div>\n        </div>\n\n        {/* Form Actions */}\n        <div className=\"flex space-x-3 pt-6 border-t border-gray-200/50 dark:border-gray-700/50\">\n          <Button\n            type=\"button\"\n            variant=\"outline\"\n            onClick={handleClose}\n            className=\"flex-1 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800\"\n          >\n            Cancel\n          </Button>\n          <Button\n            type=\"submit\"\n            loading={isSubmitting}\n            className=\"flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold\"\n          >\n            {isSubmitting ? 'Creating Employee...' : 'Create Employee'}\n          </Button>\n        </div>\n      </form>\n    </Modal>\n  );\n};\n\nexport default CreateUserModal;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAAA;;;AANA;;;;;AAmCA,MAAM,cAAc;IAClB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,kBAAkD;QAAC,EACvD,MAAM,EACN,OAAO,EACP,YAAY,EACb;;IACC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QACjD,WAAW;QACX,UAAU;QACV,OAAO;QACP,OAAO;QACP,YAAY;QACZ,mBAAmB;QACnB,KAAK;QACL,KAAK;QACL,SAAS;YACP,QAAQ;YACR,MAAM;YACN,OAAO;YACP,SAAS;QACX;IACF;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,CAAC;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,eAAe;QACnB,MAAM,YAAwB,CAAC;QAE/B,4BAA4B;QAC5B,IAAI,CAAC,SAAS,SAAS,CAAC,IAAI,IAAI,UAAU,SAAS,GAAG;QACtD,IAAI,CAAC,SAAS,QAAQ,CAAC,IAAI,IAAI,UAAU,QAAQ,GAAG;QACpD,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI,UAAU,KAAK,GAAG;QAC9C,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI,UAAU,KAAK,GAAG;QAC9C,IAAI,CAAC,SAAS,UAAU,EAAE,UAAU,UAAU,GAAG;QACjD,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,UAAU,IAAI,GAAG;QACpD,IAAI,CAAC,SAAS,OAAO,CAAC,KAAK,CAAC,IAAI,IAAI,UAAU,KAAK,GAAG;QAEtD,mBAAmB;QACnB,MAAM,aAAa;QACnB,IAAI,SAAS,KAAK,IAAI,CAAC,WAAW,IAAI,CAAC,SAAS,KAAK,GAAG;YACtD,UAAU,KAAK,GAAG;QACpB;QAEA,mBAAmB;QACnB,MAAM,aAAa;QACnB,IAAI,SAAS,KAAK,IAAI,CAAC,WAAW,IAAI,CAAC,SAAS,KAAK,CAAC,OAAO,CAAC,eAAe,MAAM;YACjF,UAAU,KAAK,GAAG;QACpB;QAEA,iBAAiB;QACjB,IAAI,SAAS,GAAG,GAAG,MAAM,SAAS,GAAG,GAAG,KAAK;YAC3C,UAAU,GAAG,GAAG;QAClB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;QAErB,gBAAgB;QAEhB,oBAAoB;QACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,MAAM,UAA6B;YACjC,IAAI,KAAK,GAAG;YACZ,WAAW,SAAS,SAAS;YAC7B,UAAU,SAAS,QAAQ;YAC3B,OAAO,SAAS,KAAK;YACrB,OAAO,SAAS,KAAK;YACrB,YAAY,SAAS,UAAU;YAC/B,mBAAmB,SAAS,iBAAiB;YAC7C,KAAK,SAAS,GAAG,IAAI,AAAC,GAA2D,OAAzD,SAAS,SAAS,EAAC,wCAA0D,OAApB,SAAS,UAAU,EAAC;YACrG,KAAK,SAAS,GAAG;YACjB,SAAS,SAAS,OAAO;YACzB,OAAO,AAAC,8BAA8D,OAAjC,SAAS,SAAS,CAAC,WAAW,IAAG;YACtE,UAAU,EAAE;YACZ,UAAU,EAAE;YACZ,oBAAoB;gBAClB;oBACE,SAAS;oBACT,QAAQ,SAAS,iBAAiB;oBAClC,OAAO;wBAAC;wBAAuB;qBAAuB;oBACtD,cAAc;wBAAC;wBAAqC;qBAA6B;gBACnF;aACD;QACH;QAEA,aAAa;QACb,gBAAgB;QAChB;IACF;IAEA,MAAM,cAAc;QAClB,YAAY;YACV,WAAW;YACX,UAAU;YACV,OAAO;YACP,OAAO;YACP,YAAY;YACZ,mBAAmB;YACnB,KAAK;YACL,KAAK;YACL,SAAS;gBACP,QAAQ;gBACR,MAAM;gBACN,OAAO;gBACP,SAAS;YACX;QACF;QACA,UAAU,CAAC;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC,OAAe;QACrC,IAAI,MAAM,QAAQ,CAAC,MAAM;YACvB,MAAM,CAAC,QAAQ,MAAM,GAAG,MAAM,KAAK,CAAC;YACpC,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,CAAC,OAAO,EAAE;wBACR,GAAG,IAAI,CAAC,OAAyB;wBACjC,CAAC,MAAM,EAAE;oBACX;gBACF,CAAC;QACH,OAAO;YACL,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,CAAC,MAAM,EAAE;gBACX,CAAC;QACH;QAEA,sCAAsC;QACtC,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAA,OAAQ,CAAC;oBACjB,GAAG,IAAI;oBACP,CAAC,MAAM,EAAE;gBACX,CAAC;QACH;IACF;IAEA,qBACE,6LAAC,oIAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS;QACT,OAAM;QACN,MAAK;kBAEL,cAAA,6LAAC;YAAK,UAAU;YAAc,WAAU;;8BAEtC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC,0NAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;gCAAkD;;;;;;;sCAI5E,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,MAAK;4CACL,OAAO,SAAS,SAAS;4CACzB,UAAU,CAAC,IAAM,eAAe,aAAa,EAAE,MAAM,CAAC,KAAK;4CAC3D,WAAW,AAAC,wJAEX,OADC,OAAO,SAAS,GAAG,mBAAmB;4CAExC,aAAY;;;;;;wCAEb,OAAO,SAAS,kBACf,6LAAC;4CAAE,WAAU;;8DACX,6LAAC,gPAAA,CAAA,0BAAuB;oDAAC,WAAU;;;;;;gDAClC,OAAO,SAAS;;;;;;;;;;;;;8CAKvB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,MAAK;4CACL,OAAO,SAAS,QAAQ;4CACxB,UAAU,CAAC,IAAM,eAAe,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC1D,WAAW,AAAC,wJAEX,OADC,OAAO,QAAQ,GAAG,mBAAmB;4CAEvC,aAAY;;;;;;wCAEb,OAAO,QAAQ,kBACd,6LAAC;4CAAE,WAAU;;8DACX,6LAAC,gPAAA,CAAA,0BAAuB;oDAAC,WAAU;;;;;;gDAClC,OAAO,QAAQ;;;;;;;;;;;;;8CAKtB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,MAAK;4CACL,OAAO,SAAS,KAAK;4CACrB,UAAU,CAAC,IAAM,eAAe,SAAS,EAAE,MAAM,CAAC,KAAK;4CACvD,WAAW,AAAC,wJAEX,OADC,OAAO,KAAK,GAAG,mBAAmB;4CAEpC,aAAY;;;;;;wCAEb,OAAO,KAAK,kBACX,6LAAC;4CAAE,WAAU;;8DACX,6LAAC,gPAAA,CAAA,0BAAuB;oDAAC,WAAU;;;;;;gDAClC,OAAO,KAAK;;;;;;;;;;;;;8CAKnB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,MAAK;4CACL,OAAO,SAAS,KAAK;4CACrB,UAAU,CAAC,IAAM,eAAe,SAAS,EAAE,MAAM,CAAC,KAAK;4CACvD,WAAW,AAAC,wJAEX,OADC,OAAO,KAAK,GAAG,mBAAmB;4CAEpC,aAAY;;;;;;wCAEb,OAAO,KAAK,kBACX,6LAAC;4CAAE,WAAU;;8DACX,6LAAC,gPAAA,CAAA,0BAAuB;oDAAC,WAAU;;;;;;gDAClC,OAAO,KAAK;;;;;;;;;;;;;8CAKnB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,MAAK;4CACL,KAAI;4CACJ,KAAI;4CACJ,OAAO,SAAS,GAAG;4CACnB,UAAU,CAAC,IAAM,eAAe,OAAO,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;4CACnE,WAAW,AAAC,wJAEX,OADC,OAAO,GAAG,GAAG,mBAAmB;;;;;;wCAGnC,OAAO,GAAG,kBACT,6LAAC;4CAAE,WAAU;;8DACX,6LAAC,gPAAA,CAAA,0BAAuB;oDAAC,WAAU;;;;;;gDAClC,OAAO,GAAG;;;;;;;;;;;;;8CAKjB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,OAAO,SAAS,UAAU;4CAC1B,UAAU,CAAC,IAAM,eAAe,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC5D,WAAW,AAAC,wJAEX,OADC,OAAO,UAAU,GAAG,mBAAmB;;8DAGzC,6LAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,YAAY,GAAG,CAAC,CAAA,qBACf,6LAAC;wDAAkB,OAAO;kEAAO;uDAApB;;;;;;;;;;;wCAGhB,OAAO,UAAU,kBAChB,6LAAC;4CAAE,WAAU;;8DACX,6LAAC,gPAAA,CAAA,0BAAuB;oDAAC,WAAU;;;;;;gDAClC,OAAO,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ5B,6LAAC;;sCACC,6LAAC;4BAAM,WAAU;;gCAAkE;gCACpD,SAAS,iBAAiB;gCAAC;;;;;;;sCAE1D,6LAAC;4BACC,MAAK;4BACL,KAAI;4BACJ,KAAI;4BACJ,MAAK;4BACL,OAAO,SAAS,iBAAiB;4BACjC,UAAU,CAAC,IAAM,eAAe,qBAAqB,WAAW,EAAE,MAAM,CAAC,KAAK;4BAC9E,WAAU;;;;;;sCAEZ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CAAK;;;;;;8CACN,6LAAC;8CAAK;;;;;;;;;;;;;;;;;;8BAKV,6LAAC;;sCACC,6LAAC;4BAAM,WAAU;sCAAkE;;;;;;sCAGnF,6LAAC;4BACC,OAAO,SAAS,GAAG;4BACnB,UAAU,CAAC,IAAM,eAAe,OAAO,EAAE,MAAM,CAAC,KAAK;4BACrD,MAAM;4BACN,WAAU;4BACV,aAAY;;;;;;;;;;;;8BAKhB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA2D;;;;;;sCAIzE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,MAAK;4CACL,OAAO,SAAS,OAAO,CAAC,MAAM;4CAC9B,UAAU,CAAC,IAAM,eAAe,kBAAkB,EAAE,MAAM,CAAC,KAAK;4CAChE,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,MAAK;4CACL,OAAO,SAAS,OAAO,CAAC,IAAI;4CAC5B,UAAU,CAAC,IAAM,eAAe,gBAAgB,EAAE,MAAM,CAAC,KAAK;4CAC9D,WAAW,AAAC,wJAEX,OADC,OAAO,IAAI,GAAG,mBAAmB;4CAEnC,aAAY;;;;;;wCAEb,OAAO,IAAI,kBACV,6LAAC;4CAAE,WAAU;;8DACX,6LAAC,gPAAA,CAAA,0BAAuB;oDAAC,WAAU;;;;;;gDAClC,OAAO,IAAI;;;;;;;;;;;;;8CAKlB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,MAAK;4CACL,OAAO,SAAS,OAAO,CAAC,KAAK;4CAC7B,UAAU,CAAC,IAAM,eAAe,iBAAiB,EAAE,MAAM,CAAC,KAAK;4CAC/D,WAAW,AAAC,wJAEX,OADC,OAAO,KAAK,GAAG,mBAAmB;4CAEpC,aAAY;;;;;;wCAEb,OAAO,KAAK,kBACX,6LAAC;4CAAE,WAAU;;8DACX,6LAAC,gPAAA,CAAA,0BAAuB;oDAAC,WAAU;;;;;;gDAClC,OAAO,KAAK;;;;;;;;;;;;;8CAKnB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,MAAK;4CACL,OAAO,SAAS,OAAO,CAAC,OAAO;4CAC/B,UAAU,CAAC,IAAM,eAAe,mBAAmB,EAAE,MAAM,CAAC,KAAK;4CACjE,WAAU;4CACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;8BAOpB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,UAAM;4BACL,MAAK;4BACL,SAAQ;4BACR,SAAS;4BACT,WAAU;sCACX;;;;;;sCAGD,6LAAC,qIAAA,CAAA,UAAM;4BACL,MAAK;4BACL,SAAS;4BACT,WAAU;sCAET,eAAe,yBAAyB;;;;;;;;;;;;;;;;;;;;;;;AAMrD;GAxaM;KAAA;uCA0aS", "debugId": null}}, {"offset": {"line": 2437, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/components/ui/Pagination.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';\nimport Button from '@/components/ui/Button';\n\ninterface PaginationProps {\n  currentPage: number;\n  totalPages: number;\n  totalItems: number;\n  itemsPerPage: number;\n  onPageChange: (page: number) => void;\n  onItemsPerPageChange: (itemsPerPage: number) => void;\n}\n\nconst Pagination: React.FC<PaginationProps> = ({\n  currentPage,\n  totalPages,\n  totalItems,\n  itemsPerPage,\n  onPageChange,\n  onItemsPerPageChange\n}) => {\n  const startItem = (currentPage - 1) * itemsPerPage + 1;\n  const endItem = Math.min(currentPage * itemsPerPage, totalItems);\n\n  const getVisiblePages = () => {\n    const delta = 2;\n    const range = [];\n    const rangeWithDots = [];\n\n    for (\n      let i = Math.max(2, currentPage - delta);\n      i <= Math.min(totalPages - 1, currentPage + delta);\n      i++\n    ) {\n      range.push(i);\n    }\n\n    if (currentPage - delta > 2) {\n      rangeWithDots.push(1, '...');\n    } else {\n      rangeWithDots.push(1);\n    }\n\n    rangeWithDots.push(...range);\n\n    if (currentPage + delta < totalPages - 1) {\n      rangeWithDots.push('...', totalPages);\n    } else {\n      rangeWithDots.push(totalPages);\n    }\n\n    return rangeWithDots;\n  };\n\n  if (totalPages <= 1) return null;\n\n  return (\n    <div className=\"glass backdrop-blur-xl bg-white/80 dark:bg-slate-900/80 border border-white/20 dark:border-slate-700/50 rounded-2xl shadow-xl p-6\">\n      <div className=\"flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0\">\n        {/* Items per page selector */}\n        <div className=\"flex items-center space-x-3\">\n          <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n            Show:\n          </span>\n          <select\n            value={itemsPerPage}\n            onChange={(e) => onItemsPerPageChange(Number(e.target.value))}\n            className=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\"\n          >\n            <option value={8}>8 per page</option>\n            <option value={12}>12 per page</option>\n            <option value={16}>16 per page</option>\n            <option value={20}>20 per page</option>\n          </select>\n        </div>\n\n        {/* Page info */}\n        <div className=\"text-sm text-gray-700 dark:text-gray-300\">\n          Showing <span className=\"font-semibold text-blue-600 dark:text-blue-400\">{startItem}</span> to{' '}\n          <span className=\"font-semibold text-blue-600 dark:text-blue-400\">{endItem}</span> of{' '}\n          <span className=\"font-semibold text-purple-600 dark:text-purple-400\">{totalItems}</span> results\n        </div>\n\n        {/* Pagination controls */}\n        <div className=\"flex items-center space-x-2\">\n          {/* Previous button */}\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => onPageChange(currentPage - 1)}\n            disabled={currentPage === 1}\n            className=\"flex items-center space-x-1 px-3 py-2 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            <ChevronLeftIcon className=\"h-4 w-4\" />\n            <span className=\"hidden sm:inline\">Previous</span>\n          </Button>\n\n          {/* Page numbers */}\n          <div className=\"flex items-center space-x-1\">\n            {getVisiblePages().map((page, index) => (\n              <React.Fragment key={index}>\n                {page === '...' ? (\n                  <span className=\"px-3 py-2 text-gray-500 dark:text-gray-400\">...</span>\n                ) : (\n                  <button\n                    onClick={() => onPageChange(page as number)}\n                    className={`px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${\n                      currentPage === page\n                        ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg transform scale-105'\n                        : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-blue-600 dark:hover:text-blue-400'\n                    }`}\n                  >\n                    {page}\n                  </button>\n                )}\n              </React.Fragment>\n            ))}\n          </div>\n\n          {/* Next button */}\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => onPageChange(currentPage + 1)}\n            disabled={currentPage === totalPages}\n            className=\"flex items-center space-x-1 px-3 py-2 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            <span className=\"hidden sm:inline\">Next</span>\n            <ChevronRightIcon className=\"h-4 w-4\" />\n          </Button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Pagination;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAeA,MAAM,aAAwC;QAAC,EAC7C,WAAW,EACX,UAAU,EACV,UAAU,EACV,YAAY,EACZ,YAAY,EACZ,oBAAoB,EACrB;IACC,MAAM,YAAY,CAAC,cAAc,CAAC,IAAI,eAAe;IACrD,MAAM,UAAU,KAAK,GAAG,CAAC,cAAc,cAAc;IAErD,MAAM,kBAAkB;QACtB,MAAM,QAAQ;QACd,MAAM,QAAQ,EAAE;QAChB,MAAM,gBAAgB,EAAE;QAExB,IACE,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,cAAc,QAClC,KAAK,KAAK,GAAG,CAAC,aAAa,GAAG,cAAc,QAC5C,IACA;YACA,MAAM,IAAI,CAAC;QACb;QAEA,IAAI,cAAc,QAAQ,GAAG;YAC3B,cAAc,IAAI,CAAC,GAAG;QACxB,OAAO;YACL,cAAc,IAAI,CAAC;QACrB;QAEA,cAAc,IAAI,IAAI;QAEtB,IAAI,cAAc,QAAQ,aAAa,GAAG;YACxC,cAAc,IAAI,CAAC,OAAO;QAC5B,OAAO;YACL,cAAc,IAAI,CAAC;QACrB;QAEA,OAAO;IACT;IAEA,IAAI,cAAc,GAAG,OAAO;IAE5B,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAU;sCAAuD;;;;;;sCAGvE,6LAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,qBAAqB,OAAO,EAAE,MAAM,CAAC,KAAK;4BAC3D,WAAU;;8CAEV,6LAAC;oCAAO,OAAO;8CAAG;;;;;;8CAClB,6LAAC;oCAAO,OAAO;8CAAI;;;;;;8CACnB,6LAAC;oCAAO,OAAO;8CAAI;;;;;;8CACnB,6LAAC;oCAAO,OAAO;8CAAI;;;;;;;;;;;;;;;;;;8BAKvB,6LAAC;oBAAI,WAAU;;wBAA2C;sCAChD,6LAAC;4BAAK,WAAU;sCAAkD;;;;;;wBAAiB;wBAAI;sCAC/F,6LAAC;4BAAK,WAAU;sCAAkD;;;;;;wBAAe;wBAAI;sCACrF,6LAAC;4BAAK,WAAU;sCAAsD;;;;;;wBAAkB;;;;;;;8BAI1F,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,qIAAA,CAAA,UAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS,IAAM,aAAa,cAAc;4BAC1C,UAAU,gBAAgB;4BAC1B,WAAU;;8CAEV,6LAAC,gOAAA,CAAA,kBAAe;oCAAC,WAAU;;;;;;8CAC3B,6LAAC;oCAAK,WAAU;8CAAmB;;;;;;;;;;;;sCAIrC,6LAAC;4BAAI,WAAU;sCACZ,kBAAkB,GAAG,CAAC,CAAC,MAAM,sBAC5B,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;8CACZ,SAAS,sBACR,6LAAC;wCAAK,WAAU;kDAA6C;;;;;iGAE7D,6LAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,wEAIX,OAHC,gBAAgB,OACZ,0FACA;kDAGL;;;;;;mCAZc;;;;;;;;;;sCAoBzB,6LAAC,qIAAA,CAAA,UAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS,IAAM,aAAa,cAAc;4BAC1C,UAAU,gBAAgB;4BAC1B,WAAU;;8CAEV,6LAAC;oCAAK,WAAU;8CAAmB;;;;;;8CACnC,6LAAC,kOAAA,CAAA,mBAAgB;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMxC;KAzHM;uCA2HS", "debugId": null}}, {"offset": {"line": 2698, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/components/ui/SkeletonCard.tsx"], "sourcesContent": ["import React from 'react';\nimport Card from './Card';\n\nconst SkeletonCard: React.FC = () => {\n  return (\n    <Card className=\"animate-pulse\">\n      <div className=\"flex items-start space-x-4 mb-6\">\n        {/* Avatar skeleton */}\n        <div className=\"h-16 w-16 bg-gradient-to-br from-slate-200 to-slate-300 dark:from-slate-700 dark:to-slate-600 rounded-2xl\"></div>\n        \n        {/* Content skeleton */}\n        <div className=\"flex-1 space-y-2\">\n          <div className=\"h-5 bg-gradient-to-r from-slate-200 to-slate-300 dark:from-slate-700 dark:to-slate-600 rounded-lg w-3/4\"></div>\n          <div className=\"h-4 bg-gradient-to-r from-slate-200 to-slate-300 dark:from-slate-700 dark:to-slate-600 rounded-lg w-1/2\"></div>\n          <div className=\"flex space-x-2\">\n            <div className=\"h-6 bg-gradient-to-r from-slate-200 to-slate-300 dark:from-slate-700 dark:to-slate-600 rounded-full w-16\"></div>\n            <div className=\"h-6 bg-gradient-to-r from-slate-200 to-slate-300 dark:from-slate-700 dark:to-slate-600 rounded-full w-20\"></div>\n          </div>\n        </div>\n      </div>\n      \n      {/* Performance section skeleton */}\n      <div className=\"mb-6\">\n        <div className=\"flex items-center justify-between mb-3\">\n          <div className=\"h-4 bg-gradient-to-r from-slate-200 to-slate-300 dark:from-slate-700 dark:to-slate-600 rounded w-20\"></div>\n          <div className=\"h-6 bg-gradient-to-r from-slate-200 to-slate-300 dark:from-slate-700 dark:to-slate-600 rounded-full w-16\"></div>\n        </div>\n        <div className=\"bg-gradient-to-r from-slate-100 to-slate-200 dark:from-slate-800 dark:to-slate-700 p-3 rounded-xl\">\n          <div className=\"flex space-x-1\">\n            {Array.from({ length: 5 }).map((_, i) => (\n              <div key={i} className=\"h-5 w-5 bg-gradient-to-r from-slate-200 to-slate-300 dark:from-slate-700 dark:to-slate-600 rounded\"></div>\n            ))}\n          </div>\n        </div>\n      </div>\n      \n      {/* Buttons skeleton */}\n      <div className=\"flex space-x-2\">\n        <div className=\"flex-1 h-10 bg-gradient-to-r from-slate-200 to-slate-300 dark:from-slate-700 dark:to-slate-600 rounded-xl\"></div>\n        <div className=\"h-10 w-10 bg-gradient-to-r from-slate-200 to-slate-300 dark:from-slate-700 dark:to-slate-600 rounded-xl\"></div>\n        <div className=\"h-10 w-20 bg-gradient-to-r from-slate-200 to-slate-300 dark:from-slate-700 dark:to-slate-600 rounded-xl\"></div>\n      </div>\n    </Card>\n  );\n};\n\nexport default SkeletonCard;\n"], "names": [], "mappings": ";;;;AACA;;;AAEA,MAAM,eAAyB;IAC7B,qBACE,6LAAC,mIAAA,CAAA,UAAI;QAAC,WAAU;;0BACd,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;0BAMrB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAEjB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,MAAM,IAAI,CAAC;gCAAE,QAAQ;4BAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC;oCAAY,WAAU;mCAAb;;;;;;;;;;;;;;;;;;;;;0BAOlB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;AAIvB;KAzCM;uCA2CS", "debugId": null}}, {"offset": {"line": 2876, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Employee, User } from '@/types';\nimport { transformUserToEmployee } from '@/lib/utils';\nimport { useSearch } from '@/hooks/useSearch';\nimport { useBookmarks } from '@/hooks/useBookmarks';\nimport { usePagination } from '@/hooks/usePagination';\nimport { useInfiniteScroll } from '@/hooks/useInfiniteScroll';\nimport { useAuth } from '@/contexts/AuthContext';\nimport UserCard from '@/components/UserCard';\nimport SearchAndFilters from '@/components/SearchAndFilters';\nimport CreateUserModal from '@/components/CreateUserModal';\nimport Card from '@/components/ui/Card';\nimport Button from '@/components/ui/Button';\nimport Pagination from '@/components/ui/Pagination';\nimport ViewModeToggle from '@/components/ui/ViewModeToggle';\nimport LoadMoreButton from '@/components/ui/LoadMoreButton';\nimport SkeletonCard from '@/components/ui/SkeletonCard';\nimport { UserPlusIcon } from '@heroicons/react/24/outline';\n\nexport default function Dashboard() {\n  const [employees, setEmployees] = useState<Employee[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [showCreateModal, setShowCreateModal] = useState(false);\n  const [viewMode, setViewMode] = useState<'pagination' | 'infinite'>('pagination');\n\n  const { bookmarks, addBookmark, removeBookmark, isBookmarked } = useBookmarks();\n  const {\n    filters,\n    filteredEmployees,\n    updateQuery,\n    updateDepartments,\n    updateRatingRange,\n    clearFilters,\n    availableDepartments\n  } = useSearch(employees);\n\n  const {\n    currentPage,\n    totalPages,\n    itemsPerPage,\n    paginatedData: paginatedEmployees,\n    totalItems,\n    setCurrentPage,\n    setItemsPerPage\n  } = usePagination({\n    data: filteredEmployees,\n    initialItemsPerPage: 12\n  });\n\n  useEffect(() => {\n    const fetchEmployees = async () => {\n      try {\n        setLoading(true);\n        const response = await fetch('https://dummyjson.com/users?limit=20');\n\n        if (!response.ok) {\n          throw new Error('Failed to fetch employees');\n        }\n\n        const data = await response.json();\n        const transformedEmployees = data.users.map((user: User) =>\n          transformUserToEmployee(user)\n        );\n\n        setEmployees(transformedEmployees);\n      } catch (err) {\n        setError(err instanceof Error ? err.message : 'An error occurred');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchEmployees();\n  }, []);\n\n  const handleBookmark = (id: number) => {\n    if (isBookmarked(id)) {\n      removeBookmark(id);\n    } else {\n      addBookmark(id);\n    }\n  };\n\n  const handlePromote = (id: number) => {\n    // In a real app, this would make an API call\n    console.log(`Promoting employee with ID: ${id}`);\n    // You could show a success toast here\n  };\n\n  const handleCreateUser = (newUser: Partial<Employee>) => {\n    const employee = newUser as Employee;\n    setEmployees(prev => [employee, ...prev]);\n    console.log('Created new employee:', employee);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        {/* Header skeleton */}\n        <div className=\"text-center mb-8 animate-pulse\">\n          <div className=\"h-10 bg-gradient-to-r from-slate-200 to-slate-300 dark:from-slate-700 dark:to-slate-600 rounded-lg w-80 mx-auto mb-4\"></div>\n          <div className=\"h-6 bg-gradient-to-r from-slate-200 to-slate-300 dark:from-slate-700 dark:to-slate-600 rounded-lg w-96 mx-auto\"></div>\n        </div>\n\n        {/* Search skeleton */}\n        <Card>\n          <div className=\"animate-pulse\">\n            <div className=\"h-12 bg-gradient-to-r from-slate-200 to-slate-300 dark:from-slate-700 dark:to-slate-600 rounded-xl\"></div>\n          </div>\n        </Card>\n\n        {/* Cards skeleton */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n          {Array.from({ length: 8 }).map((_, index) => (\n            <SkeletonCard key={index} />\n          ))}\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <Card>\n        <div className=\"text-center py-12\">\n          <div className=\"text-red-500 text-lg font-medium mb-2\">Error Loading Employees</div>\n          <p className=\"text-secondary-600 dark:text-secondary-400 mb-4\">{error}</p>\n          <button\n            onClick={() => window.location.reload()}\n            className=\"px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\"\n          >\n            Try Again\n          </button>\n        </div>\n      </Card>\n    );\n  }\n\n  return (\n    <div className=\"space-y-8 p-8 min-h-screen\">\n      {/* Header */}\n      <div className=\"text-center mb-12 animate-fade-in\">\n        <div className=\"flex items-center justify-center space-x-4 mb-6\">\n          <div className=\"w-16 h-16 bg-gradient-primary rounded-3xl flex items-center justify-center shadow-2xl animate-glow\">\n            <span className=\"text-white font-bold text-2xl\">HR</span>\n          </div>\n          <div className=\"text-left\">\n            <h1 className=\"text-6xl font-bold text-premium mb-2\">\n              Elite Dashboard\n            </h1>\n            <div className=\"h-1 w-24 bg-gradient-primary rounded-full\"></div>\n          </div>\n        </div>\n        <p className=\"text-xl text-gray-400 font-medium max-w-3xl mx-auto leading-relaxed\">\n          Experience premium HR management with our cutting-edge analytics platform designed for excellence\n        </p>\n      </div>\n\n      {/* Search and Filters */}\n      <SearchAndFilters\n        filters={filters}\n        onQueryChange={updateQuery}\n        onDepartmentsChange={updateDepartments}\n        onRatingRangeChange={updateRatingRange}\n        onClearFilters={clearFilters}\n        availableDepartments={availableDepartments}\n      />\n\n      {/* Premium Stats Summary */}\n      <div className=\"glass-card p-6 rounded-3xl animate-slide-up\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-6\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-4 h-4 bg-gradient-primary rounded-full animate-pulse\"></div>\n              <p className=\"text-lg font-semibold text-white\">\n                Showing {paginatedEmployees.length} of {totalItems} elite professionals\n                {totalPages > 1 && (\n                  <span className=\"text-sm text-gray-300 ml-2\">\n                    (Page {currentPage} of {totalPages})\n                  </span>\n                )}\n              </p>\n            </div>\n\n            {filteredEmployees.length > 0 && (\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-4 h-4 bg-gradient-warning rounded-full animate-pulse\"></div>\n                <div className=\"text-lg font-semibold text-gradient-accent\">\n                  {bookmarks.length} starred talents\n                </div>\n              </div>\n            )}\n          </div>\n\n          <div className=\"flex items-center space-x-4\">\n            <Button\n              onClick={() => setShowCreateModal(true)}\n              className=\"bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-semibold px-4 py-2 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 flex items-center space-x-2\"\n            >\n              <UserPlusIcon className=\"h-4 w-4\" />\n              <span>Add Employee</span>\n            </Button>\n\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-3 h-3 bg-green-400 rounded-full animate-pulse\"></div>\n              <span className=\"text-sm font-bold text-green-400 bg-green-400/10 px-4 py-2 rounded-full\">\n                Live Analytics\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Elite Team Grid */}\n      {filteredEmployees.length === 0 ? (\n        <div className=\"glass-card p-12 rounded-3xl text-center animate-fade-in\">\n          <div className=\"w-24 h-24 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-6 animate-glow\">\n            <span className=\"text-white text-3xl\">🔍</span>\n          </div>\n          <div className=\"text-white text-2xl font-bold mb-4\">No Elite Professionals Found</div>\n          <p className=\"text-gray-400 text-lg mb-8 max-w-md mx-auto\">\n            Refine your search criteria to discover exceptional talent in our premium database\n          </p>\n          <button\n            onClick={clearFilters}\n            className=\"px-8 py-4 bg-gradient-primary text-white rounded-2xl hover-lift font-semibold text-lg shadow-2xl\"\n          >\n            Reset Search\n          </button>\n        </div>\n      ) : (\n        <div>\n          <div className=\"flex items-center space-x-3 mb-8\">\n            <h2 className=\"text-3xl font-bold text-gradient-primary\">Elite Professionals</h2>\n            <div className=\"flex-1 h-px bg-gradient-to-r from-purple-500/50 to-transparent\"></div>\n          </div>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\">\n            {paginatedEmployees.map((employee, index) => (\n              <div\n                key={employee.id}\n                className=\"animate-scale-in\"\n                style={{ animationDelay: `${index * 100}ms` }}\n              >\n                <UserCard\n                  employee={employee}\n                  onView={(id) => console.log(`Viewing employee ${id}`)}\n                  onBookmark={handleBookmark}\n                  onPromote={handlePromote}\n                  isBookmarked={isBookmarked(employee.id)}\n                />\n              </div>\n            ))}\n          </div>\n\n          {/* Pagination */}\n          {totalPages > 1 && (\n            <div className=\"mt-8\">\n              <Pagination\n                currentPage={currentPage}\n                totalPages={totalPages}\n                totalItems={totalItems}\n                itemsPerPage={itemsPerPage}\n                onPageChange={setCurrentPage}\n                onItemsPerPageChange={setItemsPerPage}\n              />\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Create User Modal */}\n      <CreateUserModal\n        isOpen={showCreateModal}\n        onClose={() => setShowCreateModal(false)}\n        onCreateUser={handleCreateUser}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;;;AAnBA;;;;;;;;;;;;;;AAqBe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;IAEpE,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,cAAc,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD;IAC5E,MAAM,EACJ,OAAO,EACP,iBAAiB,EACjB,WAAW,EACX,iBAAiB,EACjB,iBAAiB,EACjB,YAAY,EACZ,oBAAoB,EACrB,GAAG,CAAA,GAAA,4HAAA,CAAA,YAAS,AAAD,EAAE;IAEd,MAAM,EACJ,WAAW,EACX,UAAU,EACV,YAAY,EACZ,eAAe,kBAAkB,EACjC,UAAU,EACV,cAAc,EACd,eAAe,EAChB,GAAG,CAAA,GAAA,gIAAA,CAAA,gBAAa,AAAD,EAAE;QAChB,MAAM;QACN,qBAAqB;IACvB;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM;sDAAiB;oBACrB,IAAI;wBACF,WAAW;wBACX,MAAM,WAAW,MAAM,MAAM;wBAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;4BAChB,MAAM,IAAI,MAAM;wBAClB;wBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;wBAChC,MAAM,uBAAuB,KAAK,KAAK,CAAC,GAAG;uFAAC,CAAC,OAC3C,CAAA,GAAA,sHAAA,CAAA,0BAAuB,AAAD,EAAE;;wBAG1B,aAAa;oBACf,EAAE,OAAO,KAAK;wBACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;oBAChD,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;8BAAG,EAAE;IAEL,MAAM,iBAAiB,CAAC;QACtB,IAAI,aAAa,KAAK;YACpB,eAAe;QACjB,OAAO;YACL,YAAY;QACd;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,6CAA6C;QAC7C,QAAQ,GAAG,CAAC,AAAC,+BAAiC,OAAH;IAC3C,sCAAsC;IACxC;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,WAAW;QACjB,aAAa,CAAA,OAAQ;gBAAC;mBAAa;aAAK;QACxC,QAAQ,GAAG,CAAC,yBAAyB;IACvC;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;;;;;;8BAIjB,6LAAC,mIAAA,CAAA,UAAI;8BACH,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;8BAKnB,6LAAC;oBAAI,WAAU;8BACZ,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,6LAAC,2IAAA,CAAA,UAAY,MAAM;;;;;;;;;;;;;;;;IAK7B;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC,mIAAA,CAAA,UAAI;sBACH,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAAwC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCAAmD;;;;;;kCAChE,6LAAC;wBACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;wBACrC,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAgC;;;;;;;;;;;0CAElD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAuC;;;;;;kDAGrD,6LAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;kCAGnB,6LAAC;wBAAE,WAAU;kCAAsE;;;;;;;;;;;;0BAMrF,6LAAC,yIAAA,CAAA,UAAgB;gBACf,SAAS;gBACT,eAAe;gBACf,qBAAqB;gBACrB,qBAAqB;gBACrB,gBAAgB;gBAChB,sBAAsB;;;;;;0BAIxB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAE,WAAU;;gDAAmC;gDACrC,mBAAmB,MAAM;gDAAC;gDAAK;gDAAW;gDAClD,aAAa,mBACZ,6LAAC;oDAAK,WAAU;;wDAA6B;wDACpC;wDAAY;wDAAK;wDAAW;;;;;;;;;;;;;;;;;;;gCAM1C,kBAAkB,MAAM,GAAG,mBAC1B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;gDACZ,UAAU,MAAM;gDAAC;;;;;;;;;;;;;;;;;;;sCAM1B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,UAAM;oCACL,SAAS,IAAM,mBAAmB;oCAClC,WAAU;;sDAEV,6LAAC,0NAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;sDACxB,6LAAC;sDAAK;;;;;;;;;;;;8CAGR,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAK,WAAU;sDAA0E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASjG,kBAAkB,MAAM,KAAK,kBAC5B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAAsB;;;;;;;;;;;kCAExC,6LAAC;wBAAI,WAAU;kCAAqC;;;;;;kCACpD,6LAAC;wBAAE,WAAU;kCAA8C;;;;;;kCAG3D,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;qCAKH,6LAAC;;kCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAEjB,6LAAC;wBAAI,WAAU;kCACZ,mBAAmB,GAAG,CAAC,CAAC,UAAU,sBACjC,6LAAC;gCAEC,WAAU;gCACV,OAAO;oCAAE,gBAAgB,AAAC,GAAc,OAAZ,QAAQ,KAAI;gCAAI;0CAE5C,cAAA,6LAAC,iIAAA,CAAA,UAAQ;oCACP,UAAU;oCACV,QAAQ,CAAC,KAAO,QAAQ,GAAG,CAAC,AAAC,oBAAsB,OAAH;oCAChD,YAAY;oCACZ,WAAW;oCACX,cAAc,aAAa,SAAS,EAAE;;;;;;+BATnC,SAAS,EAAE;;;;;;;;;;oBAgBrB,aAAa,mBACZ,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,yIAAA,CAAA,UAAU;4BACT,aAAa;4BACb,YAAY;4BACZ,YAAY;4BACZ,cAAc;4BACd,cAAc;4BACd,sBAAsB;;;;;;;;;;;;;;;;;0BAQhC,6LAAC,wIAAA,CAAA,UAAe;gBACd,QAAQ;gBACR,SAAS,IAAM,mBAAmB;gBAClC,cAAc;;;;;;;;;;;;AAItB;GApQwB;;QAO2C,+HAAA,CAAA,eAAY;QASzE,4HAAA,CAAA,YAAS;QAUT,gIAAA,CAAA,gBAAa;;;KA1BK", "debugId": null}}]}