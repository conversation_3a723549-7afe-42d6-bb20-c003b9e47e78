import type { TSESLint } from '@typescript-eslint/utils';
export type MessageIds = 'noShadow' | 'noShadowGlobal';
export type Options = [
    {
        allow?: string[];
        builtinGlobals?: boolean;
        hoist?: 'all' | 'functions' | 'functions-and-types' | 'never' | 'types';
        ignoreFunctionTypeParameterNameValueShadow?: boolean;
        ignoreOnInitialization?: boolean;
        ignoreTypeValueShadow?: boolean;
    }
];
declare const _default: TSESLint.RuleModule<MessageIds, Options, import("../../rules").ESLintPluginDocs, TSESLint.RuleListener>;
export default _default;
//# sourceMappingURL=no-shadow.d.ts.map