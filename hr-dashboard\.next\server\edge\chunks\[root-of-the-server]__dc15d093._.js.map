{"version": 3, "sources": [], "sections": [{"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport type { NextRequest } from 'next/server';\n\nexport function middleware(request: NextRequest) {\n  // Get the pathname of the request\n  const path = request.nextUrl.pathname;\n\n  // Define public paths that don't require authentication\n  const isPublicPath = path === '/login';\n\n  // Check if user is authenticated by looking for the auth cookie\n  const isAuthenticated = request.cookies.has('hr-auth-user');\n\n  // Redirect logic\n  if (path === '/') {\n    // Root path should redirect to login if not authenticated, otherwise to dashboard\n    if (!isAuthenticated) {\n      return NextResponse.redirect(new URL('/login', request.url));\n    } else {\n      return NextResponse.redirect(new URL('/dashboard', request.url));\n    }\n  }\n\n  // If the user is on a public path and is authenticated, redirect to dashboard\n  if (isPublicPath && isAuthenticated) {\n    return NextResponse.redirect(new URL('/dashboard', request.url));\n  }\n\n  // If the user is not on a public path and is not authenticated, redirect to login\n  if (!isPublicPath && !isAuthenticated && path !== '/_next/static' && !path.includes('.')) {\n    return NextResponse.redirect(new URL('/login', request.url));\n  }\n\n  // Continue with the request\n  return NextResponse.next();\n}\n\n// Configure the middleware to run on specific paths\nexport const config = {\n  matcher: [\n    '/',\n    '/login',\n    '/dashboard',\n    '/bookmarks',\n    '/analytics',\n    '/employee/:path*',\n  ],\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAGO,SAAS,WAAW,OAAoB;IAC7C,kCAAkC;IAClC,MAAM,OAAO,QAAQ,OAAO,CAAC,QAAQ;IAErC,wDAAwD;IACxD,MAAM,eAAe,SAAS;IAE9B,gEAAgE;IAChE,MAAM,kBAAkB,QAAQ,OAAO,CAAC,GAAG,CAAC;IAE5C,iBAAiB;IACjB,IAAI,SAAS,KAAK;QAChB,kFAAkF;QAClF,IAAI,CAAC,iBAAiB;YACpB,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,QAAQ,GAAG;QAC5D,OAAO;YACL,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,cAAc,QAAQ,GAAG;QAChE;IACF;IAEA,8EAA8E;IAC9E,IAAI,gBAAgB,iBAAiB;QACnC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,cAAc,QAAQ,GAAG;IAChE;IAEA,kFAAkF;IAClF,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,SAAS,mBAAmB,CAAC,KAAK,QAAQ,CAAC,MAAM;QACxF,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,QAAQ,GAAG;IAC5D;IAEA,4BAA4B;IAC5B,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAGO,MAAM,SAAS;IACpB,SAAS;QACP;QACA;QACA;QACA;QACA;QACA;KACD;AACH"}}]}