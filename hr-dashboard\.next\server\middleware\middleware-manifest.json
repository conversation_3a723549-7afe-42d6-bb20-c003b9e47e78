{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_82ad035c._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_fb905aa2.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/(/?index|/?index\\\\.json)?", "originalSource": "/"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/login{(\\\\.json)}?", "originalSource": "/login"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/dashboard{(\\\\.json)}?", "originalSource": "/dashboard"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/bookmarks{(\\\\.json)}?", "originalSource": "/bookmarks"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/analytics{(\\\\.json)}?", "originalSource": "/analytics"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/employee/:path*{(\\\\.json)}?", "originalSource": "/employee/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "1YdCMC+OfSJ4QuPZDVSpwcJ5/IdKgzgJy/NFRC946l4=", "__NEXT_PREVIEW_MODE_ID": "51c729d9394f947d687c436257d4f3f9", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "8ea0ec83fff9b478c5983d47043f74e921e0b19946d0d03b78270e67c8d66227", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "9238cefad157cddb484fca5753e5a3bbc274d00c205a3e429f280918f3b7eba5"}}}, "instrumentation": null, "functions": {}}