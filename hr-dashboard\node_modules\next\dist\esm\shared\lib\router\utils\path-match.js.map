{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/path-match.ts"], "sourcesContent": ["import type { Key } from 'next/dist/compiled/path-to-regexp'\nimport { pathToRegexp } from 'next/dist/compiled/path-to-regexp'\nimport { regexpToFunction } from 'next/dist/compiled/path-to-regexp'\n\ninterface Options {\n  /**\n   * A transformer function that will be applied to the regexp generated\n   * from the provided path and path-to-regexp.\n   */\n  regexModifier?: (regex: string) => string\n  /**\n   * When true the function will remove all unnamed parameters\n   * from the matched parameters.\n   */\n  removeUnnamedParams?: boolean\n  /**\n   * When true the regexp won't allow an optional trailing delimiter\n   * to match.\n   */\n  strict?: boolean\n\n  /**\n   * When true the matcher will be case-sensitive, defaults to false\n   */\n  sensitive?: boolean\n}\n\nexport type PatchMatcher = (\n  pathname: string,\n  params?: Record<string, any>\n) => Record<string, any> | false\n\n/**\n * Generates a path matcher function for a given path and options based on\n * path-to-regexp. By default the match will be case insensitive, non strict\n * and delimited by `/`.\n */\nexport function getPathMatch(path: string, options?: Options): PatchMatcher {\n  const keys: Key[] = []\n  const regexp = pathToRegexp(path, keys, {\n    delimiter: '/',\n    sensitive:\n      typeof options?.sensitive === 'boolean' ? options.sensitive : false,\n    strict: options?.strict,\n  })\n\n  const matcher = regexpToFunction<Record<string, any>>(\n    options?.regexModifier\n      ? new RegExp(options.regexModifier(regexp.source), regexp.flags)\n      : regexp,\n    keys\n  )\n\n  /**\n   * A matcher function that will check if a given pathname matches the path\n   * given in the builder function. When the path does not match it will return\n   * `false` but if it does it will return an object with the matched params\n   * merged with the params provided in the second argument.\n   */\n  return (pathname, params) => {\n    // If no pathname is provided it's not a match.\n    if (typeof pathname !== 'string') return false\n\n    const match = matcher(pathname)\n\n    // If the path did not match `false` will be returned.\n    if (!match) return false\n\n    /**\n     * If unnamed params are not allowed they must be removed from\n     * the matched parameters. path-to-regexp uses \"string\" for named and\n     * \"number\" for unnamed parameters.\n     */\n    if (options?.removeUnnamedParams) {\n      for (const key of keys) {\n        if (typeof key.name === 'number') {\n          delete match.params[key.name]\n        }\n      }\n    }\n\n    return { ...params, ...match.params }\n  }\n}\n"], "names": ["pathToRegexp", "regexpToFunction", "getPathMatch", "path", "options", "keys", "regexp", "delimiter", "sensitive", "strict", "matcher", "regexModifier", "RegExp", "source", "flags", "pathname", "params", "match", "removeUnnamedP<PERSON>ms", "key", "name"], "mappings": "AACA,SAASA,YAAY,QAAQ,oCAAmC;AAChE,SAASC,gBAAgB,QAAQ,oCAAmC;AA8BpE;;;;CAIC,GACD,OAAO,SAASC,aAAaC,IAAY,EAAEC,OAAiB;IAC1D,MAAMC,OAAc,EAAE;IACtB,MAAMC,SAASN,aAAaG,MAAME,MAAM;QACtCE,WAAW;QACXC,WACE,QAAOJ,2BAAAA,QAASI,SAAS,MAAK,YAAYJ,QAAQI,SAAS,GAAG;QAChEC,MAAM,EAAEL,2BAAAA,QAASK,MAAM;IACzB;IAEA,MAAMC,UAAUT,iBACdG,CAAAA,2BAAAA,QAASO,aAAa,IAClB,IAAIC,OAAOR,QAAQO,aAAa,CAACL,OAAOO,MAAM,GAAGP,OAAOQ,KAAK,IAC7DR,QACJD;IAGF;;;;;GAKC,GACD,OAAO,CAACU,UAAUC;QAChB,+CAA+C;QAC/C,IAAI,OAAOD,aAAa,UAAU,OAAO;QAEzC,MAAME,QAAQP,QAAQK;QAEtB,sDAAsD;QACtD,IAAI,CAACE,OAAO,OAAO;QAEnB;;;;KAIC,GACD,IAAIb,2BAAAA,QAASc,mBAAmB,EAAE;YAChC,KAAK,MAAMC,OAAOd,KAAM;gBACtB,IAAI,OAAOc,IAAIC,IAAI,KAAK,UAAU;oBAChC,OAAOH,MAAMD,MAAM,CAACG,IAAIC,IAAI,CAAC;gBAC/B;YACF;QACF;QAEA,OAAO;YAAE,GAAGJ,MAAM;YAAE,GAAGC,MAAMD,MAAM;QAAC;IACtC;AACF", "ignoreList": [0]}