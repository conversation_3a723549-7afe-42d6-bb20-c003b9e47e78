{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/contexts/AuthContext';\n\nexport default function Home() {\n  const router = useRouter();\n  const { isAuthenticated, isLoading } = useAuth();\n\n  useEffect(() => {\n    if (!isLoading) {\n      if (isAuthenticated) {\n        router.replace('/dashboard');\n      } else {\n        router.replace('/login');\n      }\n    }\n  }, [isAuthenticated, isLoading, router]);\n\n  // Show loading spinner while checking auth\n  return (\n    <div className=\"min-h-screen flex items-center justify-center\">\n      <div className=\"relative\">\n        <div className=\"w-16 h-16 border-4 border-blue-200 dark:border-blue-800 rounded-full animate-spin\"></div>\n        <div className=\"absolute top-0 left-0 w-16 h-16 border-4 border-transparent border-t-blue-600 rounded-full animate-spin\"></div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,CAAC,WAAW;gBACd,IAAI,iBAAiB;oBACnB,OAAO,OAAO,CAAC;gBACjB,OAAO;oBACL,OAAO,OAAO,CAAC;gBACjB;YACF;QACF;yBAAG;QAAC;QAAiB;QAAW;KAAO;IAEvC,2CAA2C;IAC3C,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAIvB;GAvBwB;;QACP,qIAAA,CAAA,YAAS;QACe,kIAAA,CAAA,UAAO;;;KAFxB", "debugId": null}}]}