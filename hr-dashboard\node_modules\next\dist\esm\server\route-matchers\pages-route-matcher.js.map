{"version": 3, "sources": ["../../../src/server/route-matchers/pages-route-matcher.ts"], "sourcesContent": ["import type { PagesRouteDefinition } from '../route-definitions/pages-route-definition'\nimport { LocaleRouteMatcher } from './locale-route-matcher'\nimport { RouteMatcher } from './route-matcher'\n\nexport class PagesRouteMatcher extends RouteMatcher<PagesRouteDefinition> {}\n\nexport class PagesLocaleRouteMatcher extends LocaleRouteMatcher<PagesRouteDefinition> {}\n"], "names": ["LocaleRouteMatcher", "RouteMatcher", "PagesRouteMatcher", "PagesLocaleRouteMatcher"], "mappings": "AACA,SAASA,kBAAkB,QAAQ,yBAAwB;AAC3D,SAASC,YAAY,QAAQ,kBAAiB;AAE9C,OAAO,MAAMC,0BAA0BD;AAAoC;AAE3E,OAAO,MAAME,gCAAgCH;AAA0C", "ignoreList": [0]}