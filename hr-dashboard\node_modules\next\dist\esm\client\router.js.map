{"version": 3, "sources": ["../../src/client/router.ts"], "sourcesContent": ["/* global window */\nimport React from 'react'\nimport Router from '../shared/lib/router/router'\nimport type { NextRouter } from '../shared/lib/router/router'\nimport { RouterContext } from '../shared/lib/router-context.shared-runtime'\nimport isError from '../lib/is-error'\n\ntype SingletonRouterBase = {\n  router: Router | null\n  readyCallbacks: Array<() => any>\n  ready(cb: () => any): void\n}\n\nexport { Router }\n\nexport type { NextRouter }\n\nexport type SingletonRouter = SingletonRouterBase & NextRouter\n\nconst singletonRouter: SingletonRouterBase = {\n  router: null, // holds the actual router instance\n  readyCallbacks: [],\n  ready(callback: () => void) {\n    if (this.router) return callback()\n    if (typeof window !== 'undefined') {\n      this.readyCallbacks.push(callback)\n    }\n  },\n}\n\n// Create public properties and methods of the router in the singletonRouter\nconst urlPropertyFields = [\n  'pathname',\n  'route',\n  'query',\n  'asPath',\n  'components',\n  'isFallback',\n  'basePath',\n  'locale',\n  'locales',\n  'defaultLocale',\n  'isReady',\n  'isPreview',\n  'isLocaleDomain',\n  'domainLocales',\n] as const\nconst routerEvents = [\n  'routeChangeStart',\n  'beforeHistoryChange',\n  'routeChangeComplete',\n  'routeChangeError',\n  'hashChangeStart',\n  'hashChangeComplete',\n] as const\nexport type RouterEvent = (typeof routerEvents)[number]\n\nconst coreMethodFields = [\n  'push',\n  'replace',\n  'reload',\n  'back',\n  'prefetch',\n  'beforePopState',\n] as const\n\n// Events is a static property on the router, the router doesn't have to be initialized to use it\nObject.defineProperty(singletonRouter, 'events', {\n  get() {\n    return Router.events\n  },\n})\n\nfunction getRouter(): Router {\n  if (!singletonRouter.router) {\n    const message =\n      'No router instance found.\\n' +\n      'You should only use \"next/router\" on the client side of your app.\\n'\n    throw new Error(message)\n  }\n  return singletonRouter.router\n}\n\nurlPropertyFields.forEach((field) => {\n  // Here we need to use Object.defineProperty because we need to return\n  // the property assigned to the actual router\n  // The value might get changed as we change routes and this is the\n  // proper way to access it\n  Object.defineProperty(singletonRouter, field, {\n    get() {\n      const router = getRouter()\n      return router[field] as string\n    },\n  })\n})\n\ncoreMethodFields.forEach((field) => {\n  // We don't really know the types here, so we add them later instead\n  ;(singletonRouter as any)[field] = (...args: any[]) => {\n    const router = getRouter() as any\n    return router[field](...args)\n  }\n})\n\nrouterEvents.forEach((event) => {\n  singletonRouter.ready(() => {\n    Router.events.on(event, (...args) => {\n      const eventField = `on${event.charAt(0).toUpperCase()}${event.substring(\n        1\n      )}`\n      const _singletonRouter = singletonRouter as any\n      if (_singletonRouter[eventField]) {\n        try {\n          _singletonRouter[eventField](...args)\n        } catch (err) {\n          console.error(`Error when running the Router event: ${eventField}`)\n          console.error(\n            isError(err) ? `${err.message}\\n${err.stack}` : err + ''\n          )\n        }\n      }\n    })\n  })\n})\n\n// Export the singletonRouter and this is the public API.\nexport default singletonRouter as SingletonRouter\n\n// Reexport the withRouter HOC\nexport { default as withRouter } from './with-router'\n\n/**\n * This hook gives access the [router object](https://nextjs.org/docs/pages/api-reference/functions/use-router#router-object)\n * inside the [Pages Router](https://nextjs.org/docs/pages/building-your-application).\n *\n * Read more: [Next.js Docs: `useRouter`](https://nextjs.org/docs/pages/api-reference/functions/use-router)\n */\nexport function useRouter(): NextRouter {\n  const router = React.useContext(RouterContext)\n  if (!router) {\n    throw new Error(\n      'NextRouter was not mounted. https://nextjs.org/docs/messages/next-router-not-mounted'\n    )\n  }\n\n  return router\n}\n\n/**\n * Create a router and assign it as the singleton instance.\n * This is used in client side when we are initializing the app.\n * This should **not** be used inside the server.\n * @internal\n */\nexport function createRouter(\n  ...args: ConstructorParameters<typeof Router>\n): Router {\n  singletonRouter.router = new Router(...args)\n  singletonRouter.readyCallbacks.forEach((cb) => cb())\n  singletonRouter.readyCallbacks = []\n\n  return singletonRouter.router\n}\n\n/**\n * This function is used to create the `withRouter` router instance\n * @internal\n */\nexport function makePublicRouterInstance(router: Router): NextRouter {\n  const scopedRouter = router as any\n  const instance = {} as any\n\n  for (const property of urlPropertyFields) {\n    if (typeof scopedRouter[property] === 'object') {\n      instance[property] = Object.assign(\n        Array.isArray(scopedRouter[property]) ? [] : {},\n        scopedRouter[property]\n      ) // makes sure query is not stateful\n      continue\n    }\n\n    instance[property] = scopedRouter[property]\n  }\n\n  // Events is a static property on the router, the router doesn't have to be initialized to use it\n  instance.events = Router.events\n\n  coreMethodFields.forEach((field) => {\n    instance[field] = (...args: any[]) => {\n      return scopedRouter[field](...args)\n    }\n  })\n\n  return instance\n}\n"], "names": ["React", "Router", "RouterContext", "isError", "singletonRouter", "router", "readyCallbacks", "ready", "callback", "window", "push", "url<PERSON><PERSON><PERSON><PERSON>ields", "routerEvents", "core<PERSON><PERSON><PERSON><PERSON><PERSON>s", "Object", "defineProperty", "get", "events", "getRouter", "message", "Error", "for<PERSON>ach", "field", "args", "event", "on", "eventField", "char<PERSON>t", "toUpperCase", "substring", "_singletonRouter", "err", "console", "error", "stack", "default", "with<PERSON><PERSON><PERSON>", "useRouter", "useContext", "createRouter", "cb", "makePublicRouterInstance", "scopedRouter", "instance", "property", "assign", "Array", "isArray"], "mappings": "AAAA,iBAAiB,GACjB,OAAOA,WAAW,QAAO;AACzB,OAAOC,YAAY,8BAA6B;AAEhD,SAASC,aAAa,QAAQ,8CAA6C;AAC3E,OAAOC,aAAa,kBAAiB;AAQrC,SAASF,MAAM,GAAE;AAMjB,MAAMG,kBAAuC;IAC3CC,QAAQ;IACRC,gBAAgB,EAAE;IAClBC,OAAMC,QAAoB;QACxB,IAAI,IAAI,CAACH,MAAM,EAAE,OAAOG;QACxB,IAAI,OAAOC,WAAW,aAAa;YACjC,IAAI,CAACH,cAAc,CAACI,IAAI,CAACF;QAC3B;IACF;AACF;AAEA,4EAA4E;AAC5E,MAAMG,oBAAoB;IACxB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACD,MAAMC,eAAe;IACnB;IACA;IACA;IACA;IACA;IACA;CACD;AAGD,MAAMC,mBAAmB;IACvB;IACA;IACA;IACA;IACA;IACA;CACD;AAED,iGAAiG;AACjGC,OAAOC,cAAc,CAACX,iBAAiB,UAAU;IAC/CY;QACE,OAAOf,OAAOgB,MAAM;IACtB;AACF;AAEA,SAASC;IACP,IAAI,CAACd,gBAAgBC,MAAM,EAAE;QAC3B,MAAMc,UACJ,gCACA;QACF,MAAM,qBAAkB,CAAlB,IAAIC,MAAMD,UAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAiB;IACzB;IACA,OAAOf,gBAAgBC,MAAM;AAC/B;AAEAM,kBAAkBU,OAAO,CAAC,CAACC;IACzB,sEAAsE;IACtE,6CAA6C;IAC7C,kEAAkE;IAClE,0BAA0B;IAC1BR,OAAOC,cAAc,CAACX,iBAAiBkB,OAAO;QAC5CN;YACE,MAAMX,SAASa;YACf,OAAOb,MAAM,CAACiB,MAAM;QACtB;IACF;AACF;AAEAT,iBAAiBQ,OAAO,CAAC,CAACC;IACxB,oEAAoE;;IAClElB,eAAuB,CAACkB,MAAM,GAAG;yCAAIC;YAAAA;;QACrC,MAAMlB,SAASa;QACf,OAAOb,MAAM,CAACiB,MAAM,IAAIC;IAC1B;AACF;AAEAX,aAAaS,OAAO,CAAC,CAACG;IACpBpB,gBAAgBG,KAAK,CAAC;QACpBN,OAAOgB,MAAM,CAACQ,EAAE,CAACD,OAAO;6CAAID;gBAAAA;;YAC1B,MAAMG,aAAa,AAAC,OAAIF,MAAMG,MAAM,CAAC,GAAGC,WAAW,KAAKJ,MAAMK,SAAS,CACrE;YAEF,MAAMC,mBAAmB1B;YACzB,IAAI0B,gBAAgB,CAACJ,WAAW,EAAE;gBAChC,IAAI;oBACFI,gBAAgB,CAACJ,WAAW,IAAIH;gBAClC,EAAE,OAAOQ,KAAK;oBACZC,QAAQC,KAAK,CAAC,AAAC,0CAAuCP;oBACtDM,QAAQC,KAAK,CACX9B,QAAQ4B,OAAO,AAAGA,IAAIZ,OAAO,GAAC,OAAIY,IAAIG,KAAK,GAAKH,MAAM;gBAE1D;YACF;QACF;IACF;AACF;AAEA,yDAAyD;AACzD,eAAe3B,gBAAkC;AAEjD,8BAA8B;AAC9B,SAAS+B,WAAWC,UAAU,QAAQ,gBAAe;AAErD;;;;;CAKC,GACD,OAAO,SAASC;IACd,MAAMhC,SAASL,MAAMsC,UAAU,CAACpC;IAChC,IAAI,CAACG,QAAQ;QACX,MAAM,qBAEL,CAFK,IAAIe,MACR,yFADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,OAAOf;AACT;AAEA;;;;;CAKC,GACD,OAAO,SAASkC;IACd,IAAA,IAAA,OAAA,UAAA,QAAA,AAAGhB,OAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAGA,KAAH,QAAA,SAAA,CAAA,KAA6C;;IAE7CnB,gBAAgBC,MAAM,GAAG,IAAIJ,UAAUsB;IACvCnB,gBAAgBE,cAAc,CAACe,OAAO,CAAC,CAACmB,KAAOA;IAC/CpC,gBAAgBE,cAAc,GAAG,EAAE;IAEnC,OAAOF,gBAAgBC,MAAM;AAC/B;AAEA;;;CAGC,GACD,OAAO,SAASoC,yBAAyBpC,MAAc;IACrD,MAAMqC,eAAerC;IACrB,MAAMsC,WAAW,CAAC;IAElB,KAAK,MAAMC,YAAYjC,kBAAmB;QACxC,IAAI,OAAO+B,YAAY,CAACE,SAAS,KAAK,UAAU;YAC9CD,QAAQ,CAACC,SAAS,GAAG9B,OAAO+B,MAAM,CAChCC,MAAMC,OAAO,CAACL,YAAY,CAACE,SAAS,IAAI,EAAE,GAAG,CAAC,GAC9CF,YAAY,CAACE,SAAS,EACtB,mCAAmC;;YACrC;QACF;QAEAD,QAAQ,CAACC,SAAS,GAAGF,YAAY,CAACE,SAAS;IAC7C;IAEA,iGAAiG;IACjGD,SAAS1B,MAAM,GAAGhB,OAAOgB,MAAM;IAE/BJ,iBAAiBQ,OAAO,CAAC,CAACC;QACxBqB,QAAQ,CAACrB,MAAM,GAAG;6CAAIC;gBAAAA;;YACpB,OAAOmB,YAAY,CAACpB,MAAM,IAAIC;QAChC;IACF;IAEA,OAAOoB;AACT", "ignoreList": [0]}