{"version": 3, "sources": ["../../../../../src/server/normalizers/built/pages/pages-bundle-path-normalizer.ts"], "sourcesContent": ["import { normalizePagePath } from '../../../../shared/lib/page-path/normalize-page-path'\nimport type { Normalizer } from '../../normalizer'\nimport { Normalizers } from '../../normalizers'\nimport { PrefixingNormalizer } from '../../prefixing-normalizer'\nimport { wrapNormalizerFn } from '../../wrap-normalizer-fn'\n\nexport class PagesBundlePathNormalizer extends Normalizers {\n  constructor() {\n    super([\n      // The bundle path should have the trailing `/index` stripped from\n      // it.\n      wrapNormalizerFn(normalizePagePath),\n      // The page should prefixed with `pages/`.\n      new PrefixingNormalizer('pages'),\n    ])\n  }\n\n  public normalize(page: string): string {\n    return super.normalize(page)\n  }\n}\n\nexport class DevPagesBundlePathNormalizer extends Normalizers {\n  constructor(pagesNormalizer: Normalizer) {\n    super([\n      // This should normalize the filename to a page.\n      pagesNormalizer,\n      // Normalize the app page to a pathname.\n      new PagesBundlePathNormalizer(),\n    ])\n  }\n\n  public normalize(filename: string): string {\n    return super.normalize(filename)\n  }\n}\n"], "names": ["DevPagesBundlePathNormalizer", "PagesBundlePathNormalizer", "Normalizers", "constructor", "wrapNormalizerFn", "normalizePagePath", "PrefixingNormalizer", "normalize", "page", "pagesNormalizer", "filename"], "mappings": ";;;;;;;;;;;;;;;IAsBaA,4BAA4B;eAA5BA;;IAhBAC,yBAAyB;eAAzBA;;;mCANqB;6BAEN;qCACQ;kCACH;AAE1B,MAAMA,kCAAkCC,wBAAW;IACxDC,aAAc;QACZ,KAAK,CAAC;YACJ,kEAAkE;YAClE,MAAM;YACNC,IAAAA,kCAAgB,EAACC,oCAAiB;YAClC,0CAA0C;YAC1C,IAAIC,wCAAmB,CAAC;SACzB;IACH;IAEOC,UAAUC,IAAY,EAAU;QACrC,OAAO,KAAK,CAACD,UAAUC;IACzB;AACF;AAEO,MAAMR,qCAAqCE,wBAAW;IAC3DC,YAAYM,eAA2B,CAAE;QACvC,KAAK,CAAC;YACJ,gDAAgD;YAChDA;YACA,wCAAwC;YACxC,IAAIR;SACL;IACH;IAEOM,UAAUG,QAAgB,EAAU;QACzC,OAAO,KAAK,CAACH,UAAUG;IACzB;AACF", "ignoreList": [0]}