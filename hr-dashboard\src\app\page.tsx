'use client';

import React, { useState, useEffect } from 'react';
import { Employee, User } from '@/types';
import { transformUserToEmployee } from '@/lib/utils';
import { useSearch } from '@/hooks/useSearch';
import { useBookmarks } from '@/hooks/useBookmarks';
import UserCard from '@/components/UserCard';
import SearchAndFilters from '@/components/SearchAndFilters';
import Card from '@/components/ui/Card';
import SkeletonCard from '@/components/ui/SkeletonCard';

export default function Dashboard() {
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { bookmarks, addBookmark, removeBookmark, isBookmarked } = useBookmarks();
  const {
    filters,
    filteredEmployees,
    updateQuery,
    updateDepartments,
    updateRatingRange,
    clearFilters,
    availableDepartments
  } = useSearch(employees);

  useEffect(() => {
    const fetchEmployees = async () => {
      try {
        setLoading(true);
        const response = await fetch('https://dummyjson.com/users?limit=20');

        if (!response.ok) {
          throw new Error('Failed to fetch employees');
        }

        const data = await response.json();
        const transformedEmployees = data.users.map((user: User) =>
          transformUserToEmployee(user)
        );

        setEmployees(transformedEmployees);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchEmployees();
  }, []);

  const handleBookmark = (id: number) => {
    if (isBookmarked(id)) {
      removeBookmark(id);
    } else {
      addBookmark(id);
    }
  };

  const handlePromote = (id: number) => {
    // In a real app, this would make an API call
    console.log(`Promoting employee with ID: ${id}`);
    // You could show a success toast here
  };

  if (loading) {
    return (
      <div className="space-y-6">
        {/* Header skeleton */}
        <div className="text-center mb-8 animate-pulse">
          <div className="h-10 bg-gradient-to-r from-slate-200 to-slate-300 dark:from-slate-700 dark:to-slate-600 rounded-lg w-80 mx-auto mb-4"></div>
          <div className="h-6 bg-gradient-to-r from-slate-200 to-slate-300 dark:from-slate-700 dark:to-slate-600 rounded-lg w-96 mx-auto"></div>
        </div>

        {/* Search skeleton */}
        <Card>
          <div className="animate-pulse">
            <div className="h-12 bg-gradient-to-r from-slate-200 to-slate-300 dark:from-slate-700 dark:to-slate-600 rounded-xl"></div>
          </div>
        </Card>

        {/* Cards skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {Array.from({ length: 8 }).map((_, index) => (
            <SkeletonCard key={index} />
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <div className="text-center py-12">
          <div className="text-red-500 text-lg font-medium mb-2">Error Loading Employees</div>
          <p className="text-secondary-600 dark:text-secondary-400 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent mb-4">
          Employee Dashboard
        </h1>
        <p className="text-lg text-slate-600 dark:text-slate-400 max-w-2xl mx-auto">
          Manage and view employee performance across your organization with our modern HR analytics platform
        </p>
      </div>

      {/* Search and Filters */}
      <SearchAndFilters
        filters={filters}
        onQueryChange={updateQuery}
        onDepartmentsChange={updateDepartments}
        onRatingRangeChange={updateRatingRange}
        onClearFilters={clearFilters}
        availableDepartments={availableDepartments}
      />

      {/* Results Summary */}
      <Card padding="sm">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full"></div>
              <p className="text-sm font-medium text-slate-700 dark:text-slate-300">
                Showing {filteredEmployees.length} of {employees.length} employees
              </p>
            </div>

            {filteredEmployees.length > 0 && (
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full"></div>
                <div className="text-sm font-medium text-slate-600 dark:text-slate-400">
                  {bookmarks.length} bookmarked
                </div>
              </div>
            )}
          </div>

          <div className="text-xs text-slate-500 dark:text-slate-400 bg-slate-100 dark:bg-slate-800 px-3 py-1 rounded-full">
            Live Data
          </div>
        </div>
      </Card>

      {/* Employee Grid */}
      {filteredEmployees.length === 0 ? (
        <Card>
          <div className="text-center py-12">
            <div className="text-secondary-500 text-lg font-medium mb-2">No employees found</div>
            <p className="text-secondary-400 mb-4">
              Try adjusting your search criteria or filters
            </p>
            <button
              onClick={clearFilters}
              className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
            >
              Clear Filters
            </button>
          </div>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredEmployees.map((employee) => (
            <UserCard
              key={employee.id}
              employee={employee}
              onView={(id) => console.log(`Viewing employee ${id}`)}
              onBookmark={handleBookmark}
              onPromote={handlePromote}
              isBookmarked={isBookmarked(employee.id)}
            />
          ))}
        </div>
      )}
    </div>
  );
}
