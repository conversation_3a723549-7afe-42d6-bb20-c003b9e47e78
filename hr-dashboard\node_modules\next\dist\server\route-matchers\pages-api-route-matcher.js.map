{"version": 3, "sources": ["../../../src/server/route-matchers/pages-api-route-matcher.ts"], "sourcesContent": ["import type { PagesAPIRouteDefinition } from '../route-definitions/pages-api-route-definition'\nimport { LocaleRouteMatcher } from './locale-route-matcher'\nimport { RouteMatcher } from './route-matcher'\n\nexport class PagesAPIRouteMatcher extends RouteMatcher<PagesAPIRouteDefinition> {}\n\nexport class PagesAPILocaleRouteMatcher extends LocaleRouteMatcher<PagesAPIRouteDefinition> {}\n"], "names": ["PagesAPILocaleRouteMatcher", "PagesAPIRouteMatcher", "RouteMatcher", "LocaleRouteMatcher"], "mappings": ";;;;;;;;;;;;;;;IAMaA,0BAA0B;eAA1BA;;IAFAC,oBAAoB;eAApBA;;;oCAHsB;8BACN;AAEtB,MAAMA,6BAA6BC,0BAAY;AAA2B;AAE1E,MAAMF,mCAAmCG,sCAAkB;AAA2B", "ignoreList": [0]}