{"version": 3, "sources": ["../../src/client/normalize-locale-path.ts"], "sourcesContent": ["import type { normalizeLocalePath as Fn } from '../shared/lib/i18n/normalize-locale-path'\n\nexport const normalizeLocalePath: typeof Fn = (pathname, locales) => {\n  if (process.env.__NEXT_I18N_SUPPORT) {\n    return (\n      require('../shared/lib/i18n/normalize-locale-path') as typeof import('../shared/lib/i18n/normalize-locale-path')\n    ).normalizeLocalePath(pathname, locales)\n  }\n  return { pathname, detectedLocale: undefined }\n}\n"], "names": ["normalizeLocalePath", "pathname", "locales", "process", "env", "__NEXT_I18N_SUPPORT", "require", "detectedLocale", "undefined"], "mappings": "AAEA,OAAO,MAAMA,sBAAiC,CAACC,UAAUC;IACvD,IAAIC,QAAQC,GAAG,CAACC,mBAAmB,EAAE;QACnC,OAAO,AACLC,QAAQ,4CACRN,mBAAmB,CAACC,UAAUC;IAClC;IACA,OAAO;QAAED;QAAUM,gBAAgBC;IAAU;AAC/C,EAAC", "ignoreList": [0]}