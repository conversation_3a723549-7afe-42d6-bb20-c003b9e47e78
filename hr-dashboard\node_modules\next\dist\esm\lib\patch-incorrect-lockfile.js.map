{"version": 3, "sources": ["../../src/lib/patch-incorrect-lockfile.ts"], "sourcesContent": ["import { promises } from 'fs'\nimport * as Log from '../build/output/log'\nimport findUp from 'next/dist/compiled/find-up'\n// @ts-ignore no-json types\nimport nextPkgJson from 'next/package.json'\nimport type { UnwrapPromise } from './coalesced-function'\nimport { isCI } from '../server/ci-info'\nimport { getRegistry } from './helpers/get-registry'\n\nlet registry: string | undefined\n\nasync function fetchPkgInfo(pkg: string) {\n  if (!registry) registry = getRegistry()\n  const res = await fetch(`${registry}${pkg}`)\n\n  if (!res.ok) {\n    throw new Error(\n      `Failed to fetch registry info for ${pkg}, got status ${res.status}`\n    )\n  }\n  const data = await res.json()\n  const versionData = data.versions[nextPkgJson.version]\n\n  return {\n    os: versionData.os,\n    cpu: versionData.cpu,\n    engines: versionData.engines,\n    tarball: versionData.dist.tarball,\n    integrity: versionData.dist.integrity,\n  }\n}\n\n/**\n * Attempts to patch npm package-lock.json when it\n * fails to include optionalDependencies for other platforms\n * this can occur when the package-lock is rebuilt from a current\n * node_modules install instead of pulling fresh package data\n */\nexport async function patchIncorrectLockfile(dir: string) {\n  if (process.env.NEXT_IGNORE_INCORRECT_LOCKFILE) {\n    return\n  }\n  const lockfilePath = await findUp('package-lock.json', { cwd: dir })\n\n  if (!lockfilePath) {\n    // if no lockfile present there is no action to take\n    return\n  }\n  const content = await promises.readFile(lockfilePath, 'utf8')\n  // maintain current line ending\n  const endingNewline = content.endsWith('\\r\\n')\n    ? '\\r\\n'\n    : content.endsWith('\\n')\n      ? '\\n'\n      : ''\n\n  const lockfileParsed = JSON.parse(content)\n  const lockfileVersion = parseInt(lockfileParsed?.lockfileVersion, 10)\n  const expectedSwcPkgs = Object.keys(\n    nextPkgJson['optionalDependencies'] || {}\n  ).filter((pkg) => pkg.startsWith('@next/swc-'))\n\n  const patchDependency = (\n    pkg: string,\n    pkgData: UnwrapPromise<ReturnType<typeof fetchPkgInfo>>\n  ) => {\n    lockfileParsed.dependencies[pkg] = {\n      version: nextPkgJson.version,\n      resolved: pkgData.tarball,\n      integrity: pkgData.integrity,\n      optional: true,\n    }\n  }\n\n  const patchPackage = (\n    pkg: string,\n    pkgData: UnwrapPromise<ReturnType<typeof fetchPkgInfo>>\n  ) => {\n    lockfileParsed.packages[pkg] = {\n      version: nextPkgJson.version,\n      resolved: pkgData.tarball,\n      integrity: pkgData.integrity,\n      cpu: pkgData.cpu,\n      optional: true,\n      os: pkgData.os,\n      engines: pkgData.engines,\n    }\n  }\n\n  try {\n    const supportedVersions = [1, 2, 3]\n\n    if (!supportedVersions.includes(lockfileVersion)) {\n      // bail on unsupported version\n      return\n    }\n    // v1 only uses dependencies\n    // v2 uses dependencies and packages\n    // v3 only uses packages\n    const shouldPatchDependencies =\n      lockfileVersion === 1 || lockfileVersion === 2\n    const shouldPatchPackages = lockfileVersion === 2 || lockfileVersion === 3\n\n    if (\n      (shouldPatchDependencies && !lockfileParsed.dependencies) ||\n      (shouldPatchPackages && !lockfileParsed.packages)\n    ) {\n      // invalid lockfile so bail\n      return\n    }\n    const missingSwcPkgs = []\n    let pkgPrefix: string | undefined\n\n    if (shouldPatchPackages) {\n      pkgPrefix = ''\n      for (const pkg of Object.keys(lockfileParsed.packages)) {\n        if (pkg.endsWith('node_modules/next')) {\n          pkgPrefix = pkg.substring(0, pkg.length - 4)\n        }\n      }\n\n      if (!pkgPrefix) {\n        // unable to locate the next package so bail\n        return\n      }\n    }\n\n    for (const pkg of expectedSwcPkgs) {\n      if (\n        (shouldPatchDependencies && !lockfileParsed.dependencies[pkg]) ||\n        (shouldPatchPackages && !lockfileParsed.packages[`${pkgPrefix}${pkg}`])\n      ) {\n        missingSwcPkgs.push(pkg)\n      }\n    }\n    if (missingSwcPkgs.length === 0) {\n      return\n    }\n    Log.warn(\n      `Found lockfile missing swc dependencies,`,\n      isCI ? 'run next locally to automatically patch' : 'patching...'\n    )\n\n    if (isCI) {\n      // no point in updating in CI as the user can't save the patch\n      return\n    }\n    const pkgsData = await Promise.all(\n      missingSwcPkgs.map((pkg) => fetchPkgInfo(pkg))\n    )\n\n    for (let i = 0; i < pkgsData.length; i++) {\n      const pkg = missingSwcPkgs[i]\n      const pkgData = pkgsData[i]\n\n      if (shouldPatchDependencies) {\n        patchDependency(pkg, pkgData)\n      }\n      if (shouldPatchPackages) {\n        patchPackage(`${pkgPrefix}${pkg}`, pkgData)\n      }\n    }\n\n    await promises.writeFile(\n      lockfilePath,\n      JSON.stringify(lockfileParsed, null, 2) + endingNewline\n    )\n    Log.warn(\n      'Lockfile was successfully patched, please run \"npm install\" to ensure @next/swc dependencies are downloaded'\n    )\n  } catch (err) {\n    Log.error(\n      `Failed to patch lockfile, please try uninstalling and reinstalling next in this workspace`\n    )\n    console.error(err)\n  }\n}\n"], "names": ["promises", "Log", "findUp", "nextPkgJson", "isCI", "getRegistry", "registry", "fetchPkgInfo", "pkg", "res", "fetch", "ok", "Error", "status", "data", "json", "versionData", "versions", "version", "os", "cpu", "engines", "tarball", "dist", "integrity", "patchIncorrectLockfile", "dir", "process", "env", "NEXT_IGNORE_INCORRECT_LOCKFILE", "lockfilePath", "cwd", "content", "readFile", "endingNewline", "endsWith", "lockfileParsed", "JSON", "parse", "lockfileVersion", "parseInt", "expectedSwcPkgs", "Object", "keys", "filter", "startsWith", "patchDependency", "pkgData", "dependencies", "resolved", "optional", "patchPackage", "packages", "supportedVersions", "includes", "shouldPatchDependencies", "shouldPatchPackages", "missingSwcPkgs", "pkgPrefix", "substring", "length", "push", "warn", "pkgsData", "Promise", "all", "map", "i", "writeFile", "stringify", "err", "error", "console"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,KAAI;AAC7B,YAAYC,SAAS,sBAAqB;AAC1C,OAAOC,YAAY,6BAA4B;AAC/C,2BAA2B;AAC3B,OAAOC,iBAAiB,oBAAmB;AAE3C,SAASC,IAAI,QAAQ,oBAAmB;AACxC,SAASC,WAAW,QAAQ,yBAAwB;AAEpD,IAAIC;AAEJ,eAAeC,aAAaC,GAAW;IACrC,IAAI,CAACF,UAAUA,WAAWD;IAC1B,MAAMI,MAAM,MAAMC,MAAM,GAAGJ,WAAWE,KAAK;IAE3C,IAAI,CAACC,IAAIE,EAAE,EAAE;QACX,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,kCAAkC,EAAEJ,IAAI,aAAa,EAAEC,IAAII,MAAM,EAAE,GADhE,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IACA,MAAMC,OAAO,MAAML,IAAIM,IAAI;IAC3B,MAAMC,cAAcF,KAAKG,QAAQ,CAACd,YAAYe,OAAO,CAAC;IAEtD,OAAO;QACLC,IAAIH,YAAYG,EAAE;QAClBC,KAAKJ,YAAYI,GAAG;QACpBC,SAASL,YAAYK,OAAO;QAC5BC,SAASN,YAAYO,IAAI,CAACD,OAAO;QACjCE,WAAWR,YAAYO,IAAI,CAACC,SAAS;IACvC;AACF;AAEA;;;;;CAKC,GACD,OAAO,eAAeC,uBAAuBC,GAAW;IACtD,IAAIC,QAAQC,GAAG,CAACC,8BAA8B,EAAE;QAC9C;IACF;IACA,MAAMC,eAAe,MAAM5B,OAAO,qBAAqB;QAAE6B,KAAKL;IAAI;IAElE,IAAI,CAACI,cAAc;QACjB,oDAAoD;QACpD;IACF;IACA,MAAME,UAAU,MAAMhC,SAASiC,QAAQ,CAACH,cAAc;IACtD,+BAA+B;IAC/B,MAAMI,gBAAgBF,QAAQG,QAAQ,CAAC,UACnC,SACAH,QAAQG,QAAQ,CAAC,QACf,OACA;IAEN,MAAMC,iBAAiBC,KAAKC,KAAK,CAACN;IAClC,MAAMO,kBAAkBC,SAASJ,kCAAAA,eAAgBG,eAAe,EAAE;IAClE,MAAME,kBAAkBC,OAAOC,IAAI,CACjCxC,WAAW,CAAC,uBAAuB,IAAI,CAAC,GACxCyC,MAAM,CAAC,CAACpC,MAAQA,IAAIqC,UAAU,CAAC;IAEjC,MAAMC,kBAAkB,CACtBtC,KACAuC;QAEAX,eAAeY,YAAY,CAACxC,IAAI,GAAG;YACjCU,SAASf,YAAYe,OAAO;YAC5B+B,UAAUF,QAAQzB,OAAO;YACzBE,WAAWuB,QAAQvB,SAAS;YAC5B0B,UAAU;QACZ;IACF;IAEA,MAAMC,eAAe,CACnB3C,KACAuC;QAEAX,eAAegB,QAAQ,CAAC5C,IAAI,GAAG;YAC7BU,SAASf,YAAYe,OAAO;YAC5B+B,UAAUF,QAAQzB,OAAO;YACzBE,WAAWuB,QAAQvB,SAAS;YAC5BJ,KAAK2B,QAAQ3B,GAAG;YAChB8B,UAAU;YACV/B,IAAI4B,QAAQ5B,EAAE;YACdE,SAAS0B,QAAQ1B,OAAO;QAC1B;IACF;IAEA,IAAI;QACF,MAAMgC,oBAAoB;YAAC;YAAG;YAAG;SAAE;QAEnC,IAAI,CAACA,kBAAkBC,QAAQ,CAACf,kBAAkB;YAChD,8BAA8B;YAC9B;QACF;QACA,4BAA4B;QAC5B,oCAAoC;QACpC,wBAAwB;QACxB,MAAMgB,0BACJhB,oBAAoB,KAAKA,oBAAoB;QAC/C,MAAMiB,sBAAsBjB,oBAAoB,KAAKA,oBAAoB;QAEzE,IACE,AAACgB,2BAA2B,CAACnB,eAAeY,YAAY,IACvDQ,uBAAuB,CAACpB,eAAegB,QAAQ,EAChD;YACA,2BAA2B;YAC3B;QACF;QACA,MAAMK,iBAAiB,EAAE;QACzB,IAAIC;QAEJ,IAAIF,qBAAqB;YACvBE,YAAY;YACZ,KAAK,MAAMlD,OAAOkC,OAAOC,IAAI,CAACP,eAAegB,QAAQ,EAAG;gBACtD,IAAI5C,IAAI2B,QAAQ,CAAC,sBAAsB;oBACrCuB,YAAYlD,IAAImD,SAAS,CAAC,GAAGnD,IAAIoD,MAAM,GAAG;gBAC5C;YACF;YAEA,IAAI,CAACF,WAAW;gBACd,4CAA4C;gBAC5C;YACF;QACF;QAEA,KAAK,MAAMlD,OAAOiC,gBAAiB;YACjC,IACE,AAACc,2BAA2B,CAACnB,eAAeY,YAAY,CAACxC,IAAI,IAC5DgD,uBAAuB,CAACpB,eAAegB,QAAQ,CAAC,GAAGM,YAAYlD,KAAK,CAAC,EACtE;gBACAiD,eAAeI,IAAI,CAACrD;YACtB;QACF;QACA,IAAIiD,eAAeG,MAAM,KAAK,GAAG;YAC/B;QACF;QACA3D,IAAI6D,IAAI,CACN,CAAC,wCAAwC,CAAC,EAC1C1D,OAAO,4CAA4C;QAGrD,IAAIA,MAAM;YACR,8DAA8D;YAC9D;QACF;QACA,MAAM2D,WAAW,MAAMC,QAAQC,GAAG,CAChCR,eAAeS,GAAG,CAAC,CAAC1D,MAAQD,aAAaC;QAG3C,IAAK,IAAI2D,IAAI,GAAGA,IAAIJ,SAASH,MAAM,EAAEO,IAAK;YACxC,MAAM3D,MAAMiD,cAAc,CAACU,EAAE;YAC7B,MAAMpB,UAAUgB,QAAQ,CAACI,EAAE;YAE3B,IAAIZ,yBAAyB;gBAC3BT,gBAAgBtC,KAAKuC;YACvB;YACA,IAAIS,qBAAqB;gBACvBL,aAAa,GAAGO,YAAYlD,KAAK,EAAEuC;YACrC;QACF;QAEA,MAAM/C,SAASoE,SAAS,CACtBtC,cACAO,KAAKgC,SAAS,CAACjC,gBAAgB,MAAM,KAAKF;QAE5CjC,IAAI6D,IAAI,CACN;IAEJ,EAAE,OAAOQ,KAAK;QACZrE,IAAIsE,KAAK,CACP,CAAC,yFAAyF,CAAC;QAE7FC,QAAQD,KAAK,CAACD;IAChB;AACF", "ignoreList": [0]}