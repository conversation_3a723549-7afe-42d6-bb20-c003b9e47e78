{"version": 3, "sources": ["../../../../src/build/webpack/plugins/rspack-flight-client-entry-plugin.ts"], "sourcesContent": ["import type { Compiler } from '@rspack/core'\nimport {\n  getInvalidator,\n  getEntries,\n  EntryTypes,\n  getEntryKey,\n} from '../../../server/dev/on-demand-entry-handler'\nimport { COMPILER_NAMES } from '../../../shared/lib/constants'\n\nimport { getProxiedPluginState } from '../../build-context'\nimport { PAGE_TYPES } from '../../../lib/page-types'\nimport { getRspackCore } from '../../../shared/lib/get-rspack'\n\ntype Actions = {\n  [actionId: string]: {\n    workers: {\n      [name: string]: { moduleId: string | number; async: boolean }\n    }\n    // Record which layer the action is in (rsc or sc_action), in the specific entry.\n    layer: {\n      [name: string]: string\n    }\n  }\n}\n\nexport type ActionManifest = {\n  // Assign a unique encryption key during production build.\n  encryptionKey: string\n  node: Actions\n  edge: Actions\n}\n\nexport interface ModuleInfo {\n  moduleId: string | number\n  async: boolean\n}\n\nconst pluginState = getProxiedPluginState({\n  // A map to track \"action\" -> \"list of bundles\".\n  serverActions: {} as ActionManifest['node'],\n  edgeServerActions: {} as ActionManifest['edge'],\n\n  serverActionModules: {} as {\n    [workerName: string]: { server?: ModuleInfo; client?: ModuleInfo }\n  },\n\n  edgeServerActionModules: {} as {\n    [workerName: string]: { server?: ModuleInfo; client?: ModuleInfo }\n  },\n\n  ssrModules: {} as { [ssrModuleId: string]: ModuleInfo },\n  edgeSsrModules: {} as { [ssrModuleId: string]: ModuleInfo },\n\n  rscModules: {} as { [rscModuleId: string]: ModuleInfo },\n  edgeRscModules: {} as { [rscModuleId: string]: ModuleInfo },\n\n  injectedClientEntries: {} as Record<string, string>,\n})\n\ninterface Options {\n  dev: boolean\n  appDir: string\n  isEdgeServer: boolean\n  encryptionKey: string\n}\n\nexport class RspackFlightClientEntryPlugin {\n  plugin: any\n  compiler?: Compiler\n\n  constructor(options: Options) {\n    const { FlightClientEntryPlugin } = getRspackCore()\n\n    this.plugin = new FlightClientEntryPlugin({\n      ...options,\n      builtinAppLoader: !!process.env.BUILTIN_SWC_LOADER,\n      shouldInvalidateCb: ({\n        bundlePath,\n        entryName,\n        absolutePagePath,\n        clientBrowserLoader,\n      }: any) => {\n        let shouldInvalidate = false\n        const compiler = this.compiler!\n\n        const entries = getEntries(compiler.outputPath)\n        const pageKey = getEntryKey(\n          COMPILER_NAMES.client,\n          PAGE_TYPES.APP,\n          bundlePath\n        )\n\n        if (!entries[pageKey]) {\n          entries[pageKey] = {\n            type: EntryTypes.CHILD_ENTRY,\n            parentEntries: new Set([entryName]),\n            absoluteEntryFilePath: absolutePagePath,\n            bundlePath,\n            request: clientBrowserLoader,\n            dispose: false,\n            lastActiveTime: Date.now(),\n          }\n          shouldInvalidate = true\n        } else {\n          const entryData = entries[pageKey]\n          // New version of the client loader\n          if (entryData.request !== clientBrowserLoader) {\n            entryData.request = clientBrowserLoader\n            shouldInvalidate = true\n          }\n          if (entryData.type === EntryTypes.CHILD_ENTRY) {\n            entryData.parentEntries.add(entryName)\n          }\n          entryData.dispose = false\n          entryData.lastActiveTime = Date.now()\n        }\n\n        return shouldInvalidate\n      },\n      invalidateCb: () => {\n        const compiler = this.compiler!\n\n        // Invalidate in development to trigger recompilation\n        const invalidator = getInvalidator(compiler.outputPath)\n        // Check if any of the entry injections need an invalidation\n        if (invalidator) {\n          invalidator.invalidate([COMPILER_NAMES.client])\n        }\n      },\n      stateCb: (state: any) => {\n        Object.assign(pluginState.serverActions, state.serverActions)\n        Object.assign(pluginState.edgeServerActions, state.edgeServerActions)\n        Object.assign(\n          pluginState.serverActionModules,\n          state.serverActionModules\n        )\n        Object.assign(\n          pluginState.edgeServerActionModules,\n          state.edgeServerActionModules\n        )\n        Object.assign(pluginState.ssrModules, state.ssrModules)\n        Object.assign(pluginState.edgeSsrModules, state.edgeSsrModules)\n        Object.assign(pluginState.rscModules, state.rscModules)\n        Object.assign(pluginState.edgeRscModules, state.edgeRscModules)\n        Object.assign(\n          pluginState.injectedClientEntries,\n          state.injectedClientEntries\n        )\n      },\n    })\n  }\n\n  apply(compiler: Compiler) {\n    this.compiler = compiler\n    this.plugin.apply(compiler)\n  }\n}\n"], "names": ["getInvalidator", "getEntries", "EntryTypes", "getEntry<PERSON>ey", "COMPILER_NAMES", "getProxiedPluginState", "PAGE_TYPES", "getRspackCore", "pluginState", "serverActions", "edgeServerActions", "serverActionModules", "edgeServerActionModules", "ssrModules", "edgeSsrModules", "rscModules", "edgeRscModules", "injectedClientEntries", "RspackFlightClientEntryPlugin", "constructor", "options", "FlightClientEntryPlugin", "plugin", "builtinAppLoader", "process", "env", "BUILTIN_SWC_LOADER", "shouldInvalidateCb", "bundlePath", "entryName", "absolutePagePath", "clientBrowserLoader", "shouldInvalidate", "compiler", "entries", "outputPath", "page<PERSON><PERSON>", "client", "APP", "type", "CHILD_ENTRY", "parentEntries", "Set", "absoluteEntryFilePath", "request", "dispose", "lastActiveTime", "Date", "now", "entryData", "add", "invalidateCb", "invalidator", "invalidate", "stateCb", "state", "Object", "assign", "apply"], "mappings": "AACA,SACEA,cAAc,EACdC,UAAU,EACVC,UAAU,EACVC,WAAW,QACN,8CAA6C;AACpD,SAASC,cAAc,QAAQ,gCAA+B;AAE9D,SAASC,qBAAqB,QAAQ,sBAAqB;AAC3D,SAASC,UAAU,QAAQ,0BAAyB;AACpD,SAASC,aAAa,QAAQ,iCAAgC;AA0B9D,MAAMC,cAAcH,sBAAsB;IACxC,gDAAgD;IAChDI,eAAe,CAAC;IAChBC,mBAAmB,CAAC;IAEpBC,qBAAqB,CAAC;IAItBC,yBAAyB,CAAC;IAI1BC,YAAY,CAAC;IACbC,gBAAgB,CAAC;IAEjBC,YAAY,CAAC;IACbC,gBAAgB,CAAC;IAEjBC,uBAAuB,CAAC;AAC1B;AASA,OAAO,MAAMC;IAIXC,YAAYC,OAAgB,CAAE;QAC5B,MAAM,EAAEC,uBAAuB,EAAE,GAAGd;QAEpC,IAAI,CAACe,MAAM,GAAG,IAAID,wBAAwB;YACxC,GAAGD,OAAO;YACVG,kBAAkB,CAAC,CAACC,QAAQC,GAAG,CAACC,kBAAkB;YAClDC,oBAAoB,CAAC,EACnBC,UAAU,EACVC,SAAS,EACTC,gBAAgB,EAChBC,mBAAmB,EACf;gBACJ,IAAIC,mBAAmB;gBACvB,MAAMC,WAAW,IAAI,CAACA,QAAQ;gBAE9B,MAAMC,UAAUjC,WAAWgC,SAASE,UAAU;gBAC9C,MAAMC,UAAUjC,YACdC,eAAeiC,MAAM,EACrB/B,WAAWgC,GAAG,EACdV;gBAGF,IAAI,CAACM,OAAO,CAACE,QAAQ,EAAE;oBACrBF,OAAO,CAACE,QAAQ,GAAG;wBACjBG,MAAMrC,WAAWsC,WAAW;wBAC5BC,eAAe,IAAIC,IAAI;4BAACb;yBAAU;wBAClCc,uBAAuBb;wBACvBF;wBACAgB,SAASb;wBACTc,SAAS;wBACTC,gBAAgBC,KAAKC,GAAG;oBAC1B;oBACAhB,mBAAmB;gBACrB,OAAO;oBACL,MAAMiB,YAAYf,OAAO,CAACE,QAAQ;oBAClC,mCAAmC;oBACnC,IAAIa,UAAUL,OAAO,KAAKb,qBAAqB;wBAC7CkB,UAAUL,OAAO,GAAGb;wBACpBC,mBAAmB;oBACrB;oBACA,IAAIiB,UAAUV,IAAI,KAAKrC,WAAWsC,WAAW,EAAE;wBAC7CS,UAAUR,aAAa,CAACS,GAAG,CAACrB;oBAC9B;oBACAoB,UAAUJ,OAAO,GAAG;oBACpBI,UAAUH,cAAc,GAAGC,KAAKC,GAAG;gBACrC;gBAEA,OAAOhB;YACT;YACAmB,cAAc;gBACZ,MAAMlB,WAAW,IAAI,CAACA,QAAQ;gBAE9B,qDAAqD;gBACrD,MAAMmB,cAAcpD,eAAeiC,SAASE,UAAU;gBACtD,4DAA4D;gBAC5D,IAAIiB,aAAa;oBACfA,YAAYC,UAAU,CAAC;wBAACjD,eAAeiC,MAAM;qBAAC;gBAChD;YACF;YACAiB,SAAS,CAACC;gBACRC,OAAOC,MAAM,CAACjD,YAAYC,aAAa,EAAE8C,MAAM9C,aAAa;gBAC5D+C,OAAOC,MAAM,CAACjD,YAAYE,iBAAiB,EAAE6C,MAAM7C,iBAAiB;gBACpE8C,OAAOC,MAAM,CACXjD,YAAYG,mBAAmB,EAC/B4C,MAAM5C,mBAAmB;gBAE3B6C,OAAOC,MAAM,CACXjD,YAAYI,uBAAuB,EACnC2C,MAAM3C,uBAAuB;gBAE/B4C,OAAOC,MAAM,CAACjD,YAAYK,UAAU,EAAE0C,MAAM1C,UAAU;gBACtD2C,OAAOC,MAAM,CAACjD,YAAYM,cAAc,EAAEyC,MAAMzC,cAAc;gBAC9D0C,OAAOC,MAAM,CAACjD,YAAYO,UAAU,EAAEwC,MAAMxC,UAAU;gBACtDyC,OAAOC,MAAM,CAACjD,YAAYQ,cAAc,EAAEuC,MAAMvC,cAAc;gBAC9DwC,OAAOC,MAAM,CACXjD,YAAYS,qBAAqB,EACjCsC,MAAMtC,qBAAqB;YAE/B;QACF;IACF;IAEAyC,MAAMzB,QAAkB,EAAE;QACxB,IAAI,CAACA,QAAQ,GAAGA;QAChB,IAAI,CAACX,MAAM,CAACoC,KAAK,CAACzB;IACpB;AACF", "ignoreList": [0]}