# HR Performance Dashboard

A modern, responsive HR dashboard built with Next.js 13+ App Router, TypeScript, and Tailwind CSS. This application provides a comprehensive interface for managing employee performance, bookmarks, and analytics with a beautiful glass morphism design.

## ✨ Features

### 🏠 Dashboard Homepage
- **Employee Grid**: Responsive card layout displaying employee information
- **Real-time Search**: Search employees by name, email, or department
- **Advanced Filters**: Multi-select department filters and performance rating ranges
- **Performance Ratings**: Visual star ratings with color-coded badges
- **Quick Actions**: View, bookmark, and promote employees directly from cards

### 👤 Employee Details
- **Dynamic Routing**: Individual employee pages with detailed information
- **Tabbed Interface**: Overview, Projects, and Feedback sections
- **Performance History**: Quarterly performance tracking with goals and achievements
- **Interactive Feedback**: Add and view employee feedback with ratings
- **Project Management**: Track employee projects and their status

### 🔖 Bookmark Manager
- **Persistent Bookmarks**: Save favorite employees with localStorage persistence
- **Statistics Dashboard**: View bookmark counts and performance metrics
- **Quick Access**: Easy management of bookmarked employees
- **Department Analytics**: See bookmark distribution across departments

### 📊 Analytics Dashboard
- **Interactive Charts**: Department performance visualization using Chart.js
- **Performance Distribution**: Doughnut chart showing rating distribution
- **Bookmark Trends**: Line chart tracking bookmark growth over time
- **Summary Statistics**: Key metrics and performance indicators

### 🎨 Design & UX
- **Glass Morphism**: Modern glass effect with backdrop blur
- **Gradient Backgrounds**: Beautiful gradient overlays and button designs
- **Dark Mode**: Complete dark theme support with smooth transitions
- **Responsive Design**: Mobile-first approach with adaptive layouts
- **Smooth Animations**: Hover effects, transitions, and loading states

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Run the development server**
   ```bash
   npm run dev
   ```

3. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🎯 Key Features Implemented

- ✅ Next.js 13+ App Router with TypeScript
- ✅ Tailwind CSS with custom design system
- ✅ Glass morphism design with gradients
- ✅ Dark mode with system preference detection
- ✅ Responsive mobile-first design
- ✅ Employee dashboard with search and filters
- ✅ Dynamic employee detail pages
- ✅ Bookmark management with persistence
- ✅ Analytics dashboard with Chart.js
- ✅ Loading states and error handling
- ✅ Accessibility features and keyboard navigation

## 🌙 Dark Mode

Complete dark mode support with system preference detection and manual toggle.

## 📱 Responsive Design

Fully responsive with mobile sidebar and adaptive layouts for all screen sizes.

Built with ❤️ using Next.js, TypeScript, and Tailwind CSS
