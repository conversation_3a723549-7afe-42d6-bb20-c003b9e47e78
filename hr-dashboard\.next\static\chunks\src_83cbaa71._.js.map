{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/contexts/ThemeContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { Theme, ThemeContextType } from '@/types';\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\ninterface ThemeProviderProps {\n  children: React.ReactNode;\n}\n\nexport const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {\n  const [theme, setTheme] = useState<Theme>('light');\n\n  useEffect(() => {\n    // Check for saved theme preference or default to 'light'\n    const savedTheme = localStorage.getItem('theme') as Theme;\n    if (savedTheme) {\n      setTheme(savedTheme);\n    } else {\n      // Check system preference\n      const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n      setTheme(systemPrefersDark ? 'dark' : 'light');\n    }\n  }, []);\n\n  useEffect(() => {\n    // Apply theme to document\n    const root = document.documentElement;\n    if (theme === 'dark') {\n      root.classList.add('dark');\n    } else {\n      root.classList.remove('dark');\n    }\n    \n    // Save theme preference\n    localStorage.setItem('theme', theme);\n  }, [theme]);\n\n  const toggleTheme = () => {\n    setTheme(prev => prev === 'light' ? 'dark' : 'light');\n  };\n\n  const value: ThemeContextType = {\n    theme,\n    toggleTheme\n  };\n\n  return (\n    <ThemeContext.Provider value={value}>\n      {children}\n    </ThemeContext.Provider>\n  );\n};\n\nexport const useThemeContext = () => {\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('useThemeContext must be used within a ThemeProvider');\n  }\n  return context;\n};\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAKA,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAgC;AAM1D,MAAM,gBAA8C;QAAC,EAAE,QAAQ,EAAE;;IACtE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS;IAE1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,yDAAyD;YACzD,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,IAAI,YAAY;gBACd,SAAS;YACX,OAAO;gBACL,0BAA0B;gBAC1B,MAAM,oBAAoB,OAAO,UAAU,CAAC,gCAAgC,OAAO;gBACnF,SAAS,oBAAoB,SAAS;YACxC;QACF;kCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,0BAA0B;YAC1B,MAAM,OAAO,SAAS,eAAe;YACrC,IAAI,UAAU,QAAQ;gBACpB,KAAK,SAAS,CAAC,GAAG,CAAC;YACrB,OAAO;gBACL,KAAK,SAAS,CAAC,MAAM,CAAC;YACxB;YAEA,wBAAwB;YACxB,aAAa,OAAO,CAAC,SAAS;QAChC;kCAAG;QAAC;KAAM;IAEV,MAAM,cAAc;QAClB,SAAS,CAAA,OAAQ,SAAS,UAAU,SAAS;IAC/C;IAEA,MAAM,QAA0B;QAC9B;QACA;IACF;IAEA,qBACE,6LAAC,aAAa,QAAQ;QAAC,OAAO;kBAC3B;;;;;;AAGP;GA1Ca;KAAA;AA4CN,MAAM,kBAAkB;;IAC7B,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANa", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/contexts/BookmarkContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { BookmarkContextType } from '@/types';\n\nconst BookmarkContext = createContext<BookmarkContextType | undefined>(undefined);\n\ninterface BookmarkProviderProps {\n  children: React.ReactNode;\n}\n\nexport const BookmarkProvider: React.FC<BookmarkProviderProps> = ({ children }) => {\n  const [bookmarks, setBookmarks] = useState<number[]>([]);\n\n  useEffect(() => {\n    // Load bookmarks from localStorage on mount\n    const savedBookmarks = localStorage.getItem('hr-bookmarks');\n    if (savedBookmarks) {\n      try {\n        const parsed = JSON.parse(savedBookmarks);\n        if (Array.isArray(parsed)) {\n          setBookmarks(parsed);\n        }\n      } catch (error) {\n        console.error('Failed to parse saved bookmarks:', error);\n      }\n    }\n  }, []);\n\n  useEffect(() => {\n    // Save bookmarks to localStorage whenever they change\n    localStorage.setItem('hr-bookmarks', JSON.stringify(bookmarks));\n  }, [bookmarks]);\n\n  const addBookmark = (id: number) => {\n    setBookmarks(prev => {\n      if (!prev.includes(id)) {\n        return [...prev, id];\n      }\n      return prev;\n    });\n  };\n\n  const removeBookmark = (id: number) => {\n    setBookmarks(prev => prev.filter(bookmarkId => bookmarkId !== id));\n  };\n\n  const isBookmarked = (id: number) => {\n    return bookmarks.includes(id);\n  };\n\n  const value: BookmarkContextType = {\n    bookmarks,\n    addBookmark,\n    removeBookmark,\n    isBookmarked\n  };\n\n  return (\n    <BookmarkContext.Provider value={value}>\n      {children}\n    </BookmarkContext.Provider>\n  );\n};\n\nexport const useBookmarkContext = () => {\n  const context = useContext(BookmarkContext);\n  if (context === undefined) {\n    throw new Error('useBookmarkContext must be used within a BookmarkProvider');\n  }\n  return context;\n};\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAKA,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAmC;AAMhE,MAAM,mBAAoD;QAAC,EAAE,QAAQ,EAAE;;IAC5E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,4CAA4C;YAC5C,MAAM,iBAAiB,aAAa,OAAO,CAAC;YAC5C,IAAI,gBAAgB;gBAClB,IAAI;oBACF,MAAM,SAAS,KAAK,KAAK,CAAC;oBAC1B,IAAI,MAAM,OAAO,CAAC,SAAS;wBACzB,aAAa;oBACf;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,oCAAoC;gBACpD;YACF;QACF;qCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,sDAAsD;YACtD,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;QACtD;qCAAG;QAAC;KAAU;IAEd,MAAM,cAAc,CAAC;QACnB,aAAa,CAAA;YACX,IAAI,CAAC,KAAK,QAAQ,CAAC,KAAK;gBACtB,OAAO;uBAAI;oBAAM;iBAAG;YACtB;YACA,OAAO;QACT;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,aAAa,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,aAAc,eAAe;IAChE;IAEA,MAAM,eAAe,CAAC;QACpB,OAAO,UAAU,QAAQ,CAAC;IAC5B;IAEA,MAAM,QAA6B;QACjC;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,gBAAgB,QAAQ;QAAC,OAAO;kBAC9B;;;;;;AAGP;GApDa;KAAA;AAsDN,MAAM,qBAAqB;;IAChC,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANa", "debugId": null}}, {"offset": {"line": 181, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\n\ninterface User {\n  id: string;\n  email: string;\n  name: string;\n  role: 'admin' | 'hr' | 'manager';\n  avatar?: string;\n}\n\ninterface AuthContextType {\n  user: User | null;\n  login: (email: string, password: string) => Promise<boolean>;\n  logout: () => void;\n  isLoading: boolean;\n  isAuthenticated: boolean;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\n// Mock users for demonstration\nconst mockUsers: Record<string, { password: string; user: User }> = {\n  '<EMAIL>': {\n    password: 'admin123',\n    user: {\n      id: '1',\n      email: '<EMAIL>',\n      name: 'Admin User',\n      role: 'admin',\n      avatar: 'https://dummyjson.com/icon/admin/128'\n    }\n  },\n  '<EMAIL>': {\n    password: 'hr123',\n    user: {\n      id: '2',\n      email: '<EMAIL>',\n      name: 'HR Manager',\n      role: 'hr',\n      avatar: 'https://dummyjson.com/icon/hr/128'\n    }\n  },\n  '<EMAIL>': {\n    password: 'manager123',\n    user: {\n      id: '3',\n      email: '<EMAIL>',\n      name: 'Team Manager',\n      role: 'manager',\n      avatar: 'https://dummyjson.com/icon/manager/128'\n    }\n  }\n};\n\nexport const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [isClient, setIsClient] = useState(false);\n\n  useEffect(() => {\n    // Mark as client-side\n    setIsClient(true);\n\n    // Check for stored auth on mount\n    const checkAuth = () => {\n      try {\n        const storedUser = localStorage.getItem('hr-auth-user');\n        if (storedUser) {\n          const parsedUser = JSON.parse(storedUser);\n          setUser(parsedUser);\n        }\n      } catch (error) {\n        console.error('Error parsing stored user:', error);\n        localStorage.removeItem('hr-auth-user');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    checkAuth();\n  }, []);\n\n  const login = async (email: string, password: string): Promise<boolean> => {\n    setIsLoading(true);\n    \n    // Simulate API call delay\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    \n    const mockUser = mockUsers[email.toLowerCase()];\n    \n    if (mockUser && mockUser.password === password) {\n      setUser(mockUser.user);\n      localStorage.setItem('hr-auth-user', JSON.stringify(mockUser.user));\n      // Set cookie for middleware\n      document.cookie = `hr-auth-user=${JSON.stringify(mockUser.user)}; path=/; max-age=86400`;\n      setIsLoading(false);\n      return true;\n    }\n    \n    setIsLoading(false);\n    return false;\n  };\n\n  const logout = () => {\n    setUser(null);\n    localStorage.removeItem('hr-auth-user');\n    // Remove cookie for middleware\n    document.cookie = 'hr-auth-user=; path=/; max-age=0';\n  };\n\n  const value: AuthContextType = {\n    user,\n    login,\n    logout,\n    isLoading: isLoading || !isClient,\n    isAuthenticated: isClient && !!user\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\nexport const useAuth = (): AuthContextType => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAoBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAE/D,+BAA+B;AAC/B,MAAM,YAA8D;IAClE,mBAAmB;QACjB,UAAU;QACV,MAAM;YACJ,IAAI;YACJ,OAAO;YACP,MAAM;YACN,MAAM;YACN,QAAQ;QACV;IACF;IACA,gBAAgB;QACd,UAAU;QACV,MAAM;YACJ,IAAI;YACJ,OAAO;YACP,MAAM;YACN,MAAM;YACN,QAAQ;QACV;IACF;IACA,qBAAqB;QACnB,UAAU;QACV,MAAM;YACJ,IAAI;YACJ,OAAO;YACP,MAAM;YACN,MAAM;YACN,QAAQ;QACV;IACF;AACF;AAEO,MAAM,eAAkD;QAAC,EAAE,QAAQ,EAAE;;IAC1E,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,sBAAsB;YACtB,YAAY;YAEZ,iCAAiC;YACjC,MAAM;oDAAY;oBAChB,IAAI;wBACF,MAAM,aAAa,aAAa,OAAO,CAAC;wBACxC,IAAI,YAAY;4BACd,MAAM,aAAa,KAAK,KAAK,CAAC;4BAC9B,QAAQ;wBACV;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,8BAA8B;wBAC5C,aAAa,UAAU,CAAC;oBAC1B,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA;QACF;iCAAG,EAAE;IAEL,MAAM,QAAQ,OAAO,OAAe;QAClC,aAAa;QAEb,0BAA0B;QAC1B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,MAAM,WAAW,SAAS,CAAC,MAAM,WAAW,GAAG;QAE/C,IAAI,YAAY,SAAS,QAAQ,KAAK,UAAU;YAC9C,QAAQ,SAAS,IAAI;YACrB,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC,SAAS,IAAI;YACjE,4BAA4B;YAC5B,SAAS,MAAM,GAAG,AAAC,gBAA6C,OAA9B,KAAK,SAAS,CAAC,SAAS,IAAI,GAAE;YAChE,aAAa;YACb,OAAO;QACT;QAEA,aAAa;QACb,OAAO;IACT;IAEA,MAAM,SAAS;QACb,QAAQ;QACR,aAAa,UAAU,CAAC;QACxB,+BAA+B;QAC/B,SAAS,MAAM,GAAG;IACpB;IAEA,MAAM,QAAyB;QAC7B;QACA;QACA;QACA,WAAW,aAAa,CAAC;QACzB,iBAAiB,YAAY,CAAC,CAAC;IACjC;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;GArEa;KAAA;AAuEN,MAAM,UAAU;;IACrB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANa", "debugId": null}}, {"offset": {"line": 315, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\nimport { Employee, User, Project, Feedback, PerformanceRecord } from '@/types';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// Department options for random assignment\nconst DEPARTMENTS = [\n  'Engineering',\n  'Human Resources',\n  'Sales',\n  'Marketing',\n  'Finance',\n  'Operations',\n  'Customer Support',\n  'Product Management',\n  'Design',\n  'Legal'\n];\n\n// Generate random department\nexport function getRandomDepartment(): string {\n  return DEPARTMENTS[Math.floor(Math.random() * DEPARTMENTS.length)];\n}\n\n// Generate random performance rating (1-5)\nexport function getRandomRating(): number {\n  return Math.floor(Math.random() * 5) + 1;\n}\n\n// Generate random bio\nexport function generateBio(firstName: string, lastName: string, department: string): string {\n  const bios = [\n    `${firstName} ${lastName} is a dedicated professional in the ${department} department with a passion for excellence and innovation.`,\n    `With years of experience in ${department}, ${firstName} brings valuable expertise and leadership to our team.`,\n    `${firstName} is known for their collaborative approach and commitment to delivering high-quality results in ${department}.`,\n    `A results-driven professional, ${firstName} ${lastName} consistently exceeds expectations in their role within ${department}.`,\n    `${firstName} combines technical expertise with strong communication skills, making them a valuable asset to the ${department} team.`\n  ];\n  return bios[Math.floor(Math.random() * bios.length)];\n}\n\n// Generate mock projects\nexport function generateProjects(): Project[] {\n  const projectNames = [\n    'Customer Portal Redesign',\n    'Mobile App Development',\n    'Data Analytics Platform',\n    'Security Audit',\n    'Performance Optimization',\n    'User Experience Research',\n    'API Integration',\n    'Cloud Migration',\n    'Automation Framework',\n    'Quality Assurance'\n  ];\n\n  const roles = ['Lead Developer', 'Project Manager', 'Designer', 'Analyst', 'Consultant', 'Coordinator'];\n  const statuses: ('active' | 'completed' | 'on-hold')[] = ['active', 'completed', 'on-hold'];\n\n  const numProjects = Math.floor(Math.random() * 4) + 1; // 1-4 projects\n  const projects: Project[] = [];\n\n  for (let i = 0; i < numProjects; i++) {\n    projects.push({\n      id: `proj-${Math.random().toString(36).substr(2, 9)}`,\n      name: projectNames[Math.floor(Math.random() * projectNames.length)],\n      role: roles[Math.floor(Math.random() * roles.length)],\n      status: statuses[Math.floor(Math.random() * statuses.length)],\n      startDate: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],\n      endDate: Math.random() > 0.5 ? new Date(Date.now() + Math.random() * 180 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] : undefined\n    });\n  }\n\n  return projects;\n}\n\n// Generate mock feedback\nexport function generateFeedback(): Feedback[] {\n  const feedbackComments = [\n    'Excellent work on the recent project. Great attention to detail.',\n    'Shows strong leadership skills and helps team members grow.',\n    'Consistently delivers high-quality work on time.',\n    'Great communication skills and collaborative approach.',\n    'Innovative thinking and problem-solving abilities.',\n    'Reliable team player who goes above and beyond.',\n    'Strong technical skills and willingness to learn.',\n    'Positive attitude and great work ethic.'\n  ];\n\n  const names = ['John Smith', 'Sarah Johnson', 'Mike Davis', 'Emily Brown', 'David Wilson', 'Lisa Garcia'];\n\n  const numFeedback = Math.floor(Math.random() * 3) + 1; // 1-3 feedback items\n  const feedback: Feedback[] = [];\n\n  for (let i = 0; i < numFeedback; i++) {\n    feedback.push({\n      id: `feedback-${Math.random().toString(36).substr(2, 9)}`,\n      from: names[Math.floor(Math.random() * names.length)],\n      comment: feedbackComments[Math.floor(Math.random() * feedbackComments.length)],\n      rating: Math.floor(Math.random() * 5) + 1,\n      date: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]\n    });\n  }\n\n  return feedback;\n}\n\n// Generate performance history\nexport function generatePerformanceHistory(): PerformanceRecord[] {\n  const quarters = ['Q1 2024', 'Q2 2024', 'Q3 2024', 'Q4 2023'];\n  const goals = [\n    'Improve code quality',\n    'Enhance team collaboration',\n    'Complete certification',\n    'Mentor junior developers',\n    'Optimize system performance',\n    'Implement new features'\n  ];\n  const achievements = [\n    'Reduced bug count by 30%',\n    'Led successful project delivery',\n    'Improved team productivity',\n    'Implemented new process',\n    'Received client commendation',\n    'Completed training program'\n  ];\n\n  return quarters.map(quarter => ({\n    quarter,\n    rating: Math.floor(Math.random() * 5) + 1,\n    goals: goals.slice(0, Math.floor(Math.random() * 3) + 1),\n    achievements: achievements.slice(0, Math.floor(Math.random() * 3) + 1)\n  }));\n}\n\n// Transform User to Employee with additional HR data\nexport function transformUserToEmployee(user: User): Employee {\n  const department = getRandomDepartment();\n  const performanceRating = getRandomRating();\n\n  return {\n    ...user,\n    department,\n    performanceRating,\n    bio: generateBio(user.firstName, user.lastName, department),\n    projects: generateProjects(),\n    feedback: generateFeedback(),\n    performanceHistory: generatePerformanceHistory()\n  };\n}\n\n// Get performance badge variant based on rating\nexport function getPerformanceBadgeVariant(rating: number): 'primary' | 'secondary' | 'success' | 'warning' | 'danger' {\n  if (rating >= 5) return 'success';\n  if (rating >= 4) return 'primary';\n  if (rating >= 3) return 'warning';\n  if (rating >= 2) return 'secondary';\n  return 'danger';\n}\n\n// Get performance label based on rating\nexport function getPerformanceLabel(rating: number): string {\n  if (rating >= 5) return 'Outstanding';\n  if (rating >= 4) return 'Excellent';\n  if (rating >= 3) return 'Good';\n  if (rating >= 2) return 'Fair';\n  return 'Needs Improvement';\n}\n\n// Format date for display\nexport function formatDate(dateString: string): string {\n  return new Date(dateString).toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric'\n  });\n}\n\n// Debounce function for search\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n// Calculate department statistics\nexport function calculateDepartmentStats(employees: Employee[]) {\n  const departmentMap = new Map<string, { totalRating: number; count: number }>();\n\n  employees.forEach(employee => {\n    const current = departmentMap.get(employee.department) || { totalRating: 0, count: 0 };\n    departmentMap.set(employee.department, {\n      totalRating: current.totalRating + employee.performanceRating,\n      count: current.count + 1\n    });\n  });\n\n  return Array.from(departmentMap.entries()).map(([department, stats]) => ({\n    department,\n    averageRating: stats.totalRating / stats.count,\n    employeeCount: stats.count\n  }));\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;AAGO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEA,2CAA2C;AAC3C,MAAM,cAAc;IAClB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAGM,SAAS;IACd,OAAO,WAAW,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,YAAY,MAAM,EAAE;AACpE;AAGO,SAAS;IACd,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK;AACzC;AAGO,SAAS,YAAY,SAAiB,EAAE,QAAgB,EAAE,UAAkB;IACjF,MAAM,OAAO;QACV,GAAe,OAAb,WAAU,KAAkD,OAA/C,UAAS,wCAAiD,OAAX,YAAW;QACzE,+BAA6C,OAAf,YAAW,MAAc,OAAV,WAAU;QACvD,GAA8G,OAA5G,WAAU,oGAA6G,OAAX,YAAW;QACzH,kCAA8C,OAAb,WAAU,KAAsE,OAAnE,UAAS,4DAAqE,OAAX,YAAW;QAC5H,GAAkH,OAAhH,WAAU,wGAAiH,OAAX,YAAW;KAC/H;IACD,OAAO,IAAI,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK,MAAM,EAAE;AACtD;AAGO,SAAS;IACd,MAAM,eAAe;QACnB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,QAAQ;QAAC;QAAkB;QAAmB;QAAY;QAAW;QAAc;KAAc;IACvG,MAAM,WAAmD;QAAC;QAAU;QAAa;KAAU;IAE3F,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK,GAAG,eAAe;IACtE,MAAM,WAAsB,EAAE;IAE9B,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,IAAK;QACpC,SAAS,IAAI,CAAC;YACZ,IAAI,AAAC,QAA+C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;YACjD,MAAM,YAAY,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,aAAa,MAAM,EAAE;YACnE,MAAM,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM,EAAE;YACrD,QAAQ,QAAQ,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS,MAAM,EAAE;YAC7D,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,MAAM,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YACvG,SAAS,KAAK,MAAM,KAAK,MAAM,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,MAAM,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;QAChI;IACF;IAEA,OAAO;AACT;AAGO,SAAS;IACd,MAAM,mBAAmB;QACvB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,QAAQ;QAAC;QAAc;QAAiB;QAAc;QAAe;QAAgB;KAAc;IAEzG,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK,GAAG,qBAAqB;IAC5E,MAAM,WAAuB,EAAE;IAE/B,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,IAAK;QACpC,SAAS,IAAI,CAAC;YACZ,IAAI,AAAC,YAAmD,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;YACrD,MAAM,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM,EAAE;YACrD,SAAS,gBAAgB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,iBAAiB,MAAM,EAAE;YAC9E,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK;YACxC,MAAM,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACnG;IACF;IAEA,OAAO;AACT;AAGO,SAAS;IACd,MAAM,WAAW;QAAC;QAAW;QAAW;QAAW;KAAU;IAC7D,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,eAAe;QACnB;QACA;QACA;QACA;QACA;QACA;KACD;IAED,OAAO,SAAS,GAAG,CAAC,CAAA,UAAW,CAAC;YAC9B;YACA,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK;YACxC,OAAO,MAAM,KAAK,CAAC,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK;YACtD,cAAc,aAAa,KAAK,CAAC,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK;QACtE,CAAC;AACH;AAGO,SAAS,wBAAwB,IAAU;IAChD,MAAM,aAAa;IACnB,MAAM,oBAAoB;IAE1B,OAAO;QACL,GAAG,IAAI;QACP;QACA;QACA,KAAK,YAAY,KAAK,SAAS,EAAE,KAAK,QAAQ,EAAE;QAChD,UAAU;QACV,UAAU;QACV,oBAAoB;IACtB;AACF;AAGO,SAAS,2BAA2B,MAAc;IACvD,IAAI,UAAU,GAAG,OAAO;IACxB,IAAI,UAAU,GAAG,OAAO;IACxB,IAAI,UAAU,GAAG,OAAO;IACxB,IAAI,UAAU,GAAG,OAAO;IACxB,OAAO;AACT;AAGO,SAAS,oBAAoB,MAAc;IAChD,IAAI,UAAU,GAAG,OAAO;IACxB,IAAI,UAAU,GAAG,OAAO;IACxB,IAAI,UAAU,GAAG,OAAO;IACxB,IAAI,UAAU,GAAG,OAAO;IACxB,OAAO;AACT;AAGO,SAAS,WAAW,UAAkB;IAC3C,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;QACtD,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO;yCAAI;YAAA;;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS,yBAAyB,SAAqB;IAC5D,MAAM,gBAAgB,IAAI;IAE1B,UAAU,OAAO,CAAC,CAAA;QAChB,MAAM,UAAU,cAAc,GAAG,CAAC,SAAS,UAAU,KAAK;YAAE,aAAa;YAAG,OAAO;QAAE;QACrF,cAAc,GAAG,CAAC,SAAS,UAAU,EAAE;YACrC,aAAa,QAAQ,WAAW,GAAG,SAAS,iBAAiB;YAC7D,OAAO,QAAQ,KAAK,GAAG;QACzB;IACF;IAEA,OAAO,MAAM,IAAI,CAAC,cAAc,OAAO,IAAI,GAAG,CAAC;YAAC,CAAC,YAAY,MAAM;eAAM;YACvE;YACA,eAAe,MAAM,WAAW,GAAG,MAAM,KAAK;YAC9C,eAAe,MAAM,KAAK;QAC5B;;AACF", "debugId": null}}, {"offset": {"line": 545, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/hooks/useTheme.ts"], "sourcesContent": ["import { useThemeContext } from '@/contexts/ThemeContext';\n\nexport const useTheme = () => {\n  return useThemeContext();\n};\n"], "names": [], "mappings": ";;;AAAA;;;AAEO,MAAM,WAAW;;IACtB,OAAO,CAAA,GAAA,mIAAA,CAAA,kBAAe,AAAD;AACvB;GAFa;;QACJ,mIAAA,CAAA,kBAAe", "debugId": null}}, {"offset": {"line": 569, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { cn } from '@/lib/utils';\nimport {\n  HomeIcon,\n  UserGroupIcon,\n  BookmarkIcon,\n  ChartBarIcon,\n  SunIcon,\n  MoonIcon,\n  ArrowRightOnRectangleIcon,\n  UserCircleIcon\n} from '@heroicons/react/24/outline';\nimport { useTheme } from '@/hooks/useTheme';\nimport { useAuth } from '@/contexts/AuthContext';\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },\n  { name: 'Employees', href: '/dashboard', icon: UserGroupIcon },\n  { name: 'Bookmarks', href: '/bookmarks', icon: BookmarkIcon },\n  { name: 'Analytics', href: '/analytics', icon: ChartBarIcon },\n];\n\nconst Sidebar: React.FC = () => {\n  const pathname = usePathname();\n  const { theme, toggleTheme } = useTheme();\n  const { user, logout } = useAuth();\n\n  return (\n    <div className=\"flex flex-col w-72 glass-card border-r border-white/15 h-screen shadow-2xl animate-slide-up\">\n      {/* Logo */}\n      <div className=\"flex items-center justify-center h-20 px-6 border-b border-white/10\">\n        <div className=\"flex items-center space-x-4 animate-scale-in\">\n          <div className=\"relative\">\n            <div className=\"w-12 h-12 bg-gradient-primary rounded-2xl flex items-center justify-center shadow-2xl animate-glow\">\n              <span className=\"text-white font-bold text-lg\">HR</span>\n            </div>\n            <div className=\"absolute -top-1 -right-1 w-4 h-4 bg-gradient-accent rounded-full animate-pulse\"></div>\n          </div>\n          <div>\n            <h1 className=\"text-2xl font-bold text-premium\">HR Elite</h1>\n            <p className=\"text-sm text-gray-400 font-medium\">Premium Dashboard</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"flex-1 px-6 py-8 space-y-3\">\n        {navigation.map((item, index) => {\n          const isActive = pathname === item.href;\n          return (\n            <Link\n              key={item.name}\n              href={item.href}\n              className={cn(\n                'group flex items-center space-x-4 px-6 py-4 rounded-2xl transition-all duration-300 hover-lift animate-slide-up relative overflow-hidden',\n                isActive\n                  ? 'bg-gradient-primary text-white shadow-2xl'\n                  : 'text-gray-300 hover:text-white hover:bg-white/10'\n              )}\n              style={{ animationDelay: `${index * 100}ms` }}\n            >\n              {isActive && (\n                <div className=\"absolute inset-0 bg-gradient-primary opacity-20 animate-pulse\"></div>\n              )}\n              <div className={cn(\n                'p-2 rounded-xl transition-all duration-300',\n                isActive\n                  ? 'bg-white/20 shadow-lg'\n                  : 'group-hover:bg-white/10'\n              )}>\n                <item.icon className=\"h-5 w-5\" />\n              </div>\n              <span className=\"font-semibold text-base tracking-wide\">{item.name}</span>\n              {isActive && (\n                <div className=\"ml-auto w-2 h-2 bg-white rounded-full animate-pulse\"></div>\n              )}\n            </Link>\n          );\n        })}\n      </nav>\n\n      {/* Footer */}\n      <div className=\"p-6 border-t border-white/10\">\n        <div className=\"glass p-4 rounded-2xl mb-4\">\n          <div className=\"flex items-center space-x-3 mb-3\">\n            <div className=\"w-8 h-8 bg-gradient-accent rounded-full flex items-center justify-center\">\n              <span className=\"text-xs font-bold text-white\">v2</span>\n            </div>\n            <div>\n              <p className=\"text-sm font-semibold text-white\">HR Elite Dashboard</p>\n              <p className=\"text-xs text-gray-400\">Premium Edition</p>\n            </div>\n          </div>\n          <div className=\"text-xs text-gray-500 font-medium\">\n            © 2024 Elite Solutions\n          </div>\n        </div>\n\n        {/* User Profile */}\n        {user && (\n          <div className=\"glass p-4 rounded-2xl mb-4\">\n            <div className=\"flex items-center space-x-3 mb-3\">\n              <div className=\"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center\">\n                <UserCircleIcon className=\"h-6 w-6 text-white\" />\n              </div>\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"text-sm font-semibold text-white truncate\">{user.name}</p>\n                <p className=\"text-xs text-gray-400 capitalize\">{user.role}</p>\n              </div>\n            </div>\n            <button\n              onClick={logout}\n              className=\"flex items-center w-full px-3 py-2 text-xs font-medium text-red-300 hover:text-red-200 hover:bg-red-500/10 rounded-xl transition-all duration-200 group\"\n            >\n              <ArrowRightOnRectangleIcon className=\"h-4 w-4 mr-2 group-hover:scale-110 transition-transform duration-200\" />\n              Sign Out\n            </button>\n          </div>\n        )}\n\n        {/* Theme Toggle */}\n        <button\n          onClick={toggleTheme}\n          className=\"flex items-center w-full px-4 py-3 text-sm font-semibold text-gray-300 hover:text-white hover:bg-white/10 rounded-2xl transition-all duration-300 hover-lift group\"\n        >\n          <div className=\"p-2 rounded-xl bg-white/10 mr-3 group-hover:bg-white/20 transition-all duration-300\">\n            {theme === 'dark' ? (\n              <SunIcon className=\"h-4 w-4 group-hover:scale-110 transition-transform duration-200\" />\n            ) : (\n              <MoonIcon className=\"h-4 w-4 group-hover:scale-110 transition-transform duration-200\" />\n            )}\n          </div>\n          {theme === 'dark' ? 'Light Mode' : 'Dark Mode'}\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default Sidebar;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;;;AAjBA;;;;;;;AAmBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,kNAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,4NAAA,CAAA,gBAAa;IAAC;IAC7D;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,0NAAA,CAAA,eAAY;IAAC;IAC5D;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,0NAAA,CAAA,eAAY;IAAC;CAC7D;AAED,MAAM,UAAoB;;IACxB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,WAAQ,AAAD;IACtC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAE/B,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,6LAAC;oCAAI,WAAU;;;;;;;;;;;;sCAEjB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAkC;;;;;;8CAChD,6LAAC;oCAAE,WAAU;8CAAoC;;;;;;;;;;;;;;;;;;;;;;;0BAMvD,6LAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC,MAAM;oBACrB,MAAM,WAAW,aAAa,KAAK,IAAI;oBACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;wBAEH,MAAM,KAAK,IAAI;wBACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4IACA,WACI,8CACA;wBAEN,OAAO;4BAAE,gBAAgB,AAAC,GAAc,OAAZ,QAAQ,KAAI;wBAAI;;4BAE3C,0BACC,6LAAC;gCAAI,WAAU;;;;;;0CAEjB,6LAAC;gCAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,8CACA,WACI,0BACA;0CAEJ,cAAA,6LAAC,KAAK,IAAI;oCAAC,WAAU;;;;;;;;;;;0CAEvB,6LAAC;gCAAK,WAAU;0CAAyC,KAAK,IAAI;;;;;;4BACjE,0BACC,6LAAC;gCAAI,WAAU;;;;;;;uBAvBZ,KAAK,IAAI;;;;;gBA2BpB;;;;;;0BAIF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAmC;;;;;;0DAChD,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAGzC,6LAAC;gCAAI,WAAU;0CAAoC;;;;;;;;;;;;oBAMpD,sBACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,8NAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;;;;;;kDAE5B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAA6C,KAAK,IAAI;;;;;;0DACnE,6LAAC;gDAAE,WAAU;0DAAoC,KAAK,IAAI;;;;;;;;;;;;;;;;;;0CAG9D,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC,oPAAA,CAAA,4BAAyB;wCAAC,WAAU;;;;;;oCAAyE;;;;;;;;;;;;;kCAOpH,6LAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;0CACZ,UAAU,uBACT,6LAAC,gNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;6FAEnB,6LAAC,kNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;4BAGvB,UAAU,SAAS,eAAe;;;;;;;;;;;;;;;;;;;AAK7C;GAnHM;;QACa,qIAAA,CAAA,cAAW;QACG,2HAAA,CAAA,WAAQ;QACd,kIAAA,CAAA,UAAO;;;KAH5B;uCAqHS", "debugId": null}}, {"offset": {"line": 962, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/components/layout/MobileSidebar.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { cn } from '@/lib/utils';\nimport {\n  HomeIcon,\n  UserGroupIcon,\n  BookmarkIcon,\n  ChartBarIcon,\n  SunIcon,\n  MoonIcon,\n  XMarkIcon\n} from '@heroicons/react/24/outline';\nimport { useTheme } from '@/hooks/useTheme';\nimport { Transition } from '@headlessui/react';\n\nconst navigation = [\n  { name: 'Dashboard', href: '/', icon: HomeIcon },\n  { name: 'Employees', href: '/', icon: UserGroupIcon },\n  { name: 'Bookmarks', href: '/bookmarks', icon: BookmarkIcon },\n  { name: 'Analytics', href: '/analytics', icon: ChartBarIcon },\n];\n\ninterface MobileSidebarProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nconst MobileSidebar: React.FC<MobileSidebarProps> = ({ isOpen, onClose }) => {\n  const pathname = usePathname();\n  const { theme, toggleTheme } = useTheme();\n\n  return (\n    <Transition show={isOpen}>\n      {/* Backdrop */}\n      <Transition.Child\n        enter=\"transition-opacity ease-linear duration-300\"\n        enterFrom=\"opacity-0\"\n        enterTo=\"opacity-100\"\n        leave=\"transition-opacity ease-linear duration-300\"\n        leaveFrom=\"opacity-100\"\n        leaveTo=\"opacity-0\"\n      >\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 z-40\" onClick={onClose} />\n      </Transition.Child>\n\n      {/* Sidebar */}\n      <Transition.Child\n        enter=\"transition ease-in-out duration-300 transform\"\n        enterFrom=\"-translate-x-full\"\n        enterTo=\"translate-x-0\"\n        leave=\"transition ease-in-out duration-300 transform\"\n        leaveFrom=\"translate-x-0\"\n        leaveTo=\"-translate-x-full\"\n      >\n        <div className=\"fixed inset-y-0 left-0 flex flex-col w-64 glass backdrop-blur-xl bg-white/90 dark:bg-slate-900/90 border-r border-white/20 dark:border-slate-700/50 shadow-2xl z-50\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between h-16 px-4 border-b border-white/20 dark:border-slate-700/50\">\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">HR</span>\n              </div>\n              <h1 className=\"text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">\n                Dashboard\n              </h1>\n            </div>\n            <button\n              onClick={onClose}\n              className=\"p-2 rounded-xl text-slate-600 hover:text-slate-900 hover:bg-white/50 dark:text-slate-300 dark:hover:text-white dark:hover:bg-slate-800/50 transition-all duration-200\"\n            >\n              <XMarkIcon className=\"h-6 w-6\" />\n            </button>\n          </div>\n\n          {/* Navigation */}\n          <nav className=\"flex-1 px-4 py-6 space-y-2\">\n            {navigation.map((item) => {\n              const isActive = pathname === item.href;\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  onClick={onClose}\n                  className={cn(\n                    'flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 group',\n                    isActive\n                      ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg transform scale-105'\n                      : 'text-slate-600 hover:bg-white/50 hover:text-slate-900 dark:text-slate-300 dark:hover:bg-slate-800/50 dark:hover:text-white hover:shadow-md hover:transform hover:scale-105'\n                  )}\n                >\n                  <item.icon className={cn(\n                    \"mr-3 h-5 w-5 transition-transform duration-200\",\n                    isActive ? \"text-white\" : \"group-hover:scale-110\"\n                  )} />\n                  {item.name}\n                </Link>\n              );\n            })}\n          </nav>\n\n          {/* Theme Toggle */}\n          <div className=\"p-4 border-t border-white/20 dark:border-slate-700/50\">\n            <button\n              onClick={toggleTheme}\n              className=\"flex items-center w-full px-4 py-3 text-sm font-medium text-slate-600 hover:bg-white/50 hover:text-slate-900 dark:text-slate-300 dark:hover:bg-slate-800/50 dark:hover:text-white rounded-xl transition-all duration-200 hover:shadow-md hover:transform hover:scale-105 group\"\n            >\n              {theme === 'dark' ? (\n                <SunIcon className=\"mr-3 h-5 w-5 group-hover:scale-110 transition-transform duration-200\" />\n              ) : (\n                <MoonIcon className=\"mr-3 h-5 w-5 group-hover:scale-110 transition-transform duration-200\" />\n              )}\n              {theme === 'dark' ? 'Light Mode' : 'Dark Mode'}\n            </button>\n          </div>\n        </div>\n      </Transition.Child>\n    </Transition>\n  );\n};\n\nexport default MobileSidebar;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;;;AAhBA;;;;;;;AAkBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAK,MAAM,kNAAA,CAAA,WAAQ;IAAC;IAC/C;QAAE,MAAM;QAAa,MAAM;QAAK,MAAM,4NAAA,CAAA,gBAAa;IAAC;IACpD;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,0NAAA,CAAA,eAAY;IAAC;IAC5D;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,0NAAA,CAAA,eAAY;IAAC;CAC7D;AAOD,MAAM,gBAA8C;QAAC,EAAE,MAAM,EAAE,OAAO,EAAE;;IACtE,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,WAAQ,AAAD;IAEtC,qBACE,6LAAC,0LAAA,CAAA,aAAU;QAAC,MAAM;;0BAEhB,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;gBACf,OAAM;gBACN,WAAU;gBACV,SAAQ;gBACR,OAAM;gBACN,WAAU;gBACV,SAAQ;0BAER,cAAA,6LAAC;oBAAI,WAAU;oBAA4C,SAAS;;;;;;;;;;;0BAItE,6LAAC,0LAAA,CAAA,aAAU,CAAC,KAAK;gBACf,OAAM;gBACN,WAAU;gBACV,SAAQ;gBACR,OAAM;gBACN,WAAU;gBACV,SAAQ;0BAER,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,6LAAC;4CAAG,WAAU;sDAA+F;;;;;;;;;;;;8CAI/G,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKzB,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,WAAW,aAAa,KAAK,IAAI;gCACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,SAAS;oCACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gGACA,WACI,0FACA;;sDAGN,6LAAC,KAAK,IAAI;4CAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACrB,kDACA,WAAW,eAAe;;;;;;wCAE3B,KAAK,IAAI;;mCAdL,KAAK,IAAI;;;;;4BAiBpB;;;;;;sCAIF,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS;gCACT,WAAU;;oCAET,UAAU,uBACT,6LAAC,gNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;iGAEnB,6LAAC,kNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAErB,UAAU,SAAS,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjD;GA1FM;;QACa,qIAAA,CAAA,cAAW;QACG,2HAAA,CAAA,WAAQ;;;KAFnC;uCA4FS", "debugId": null}}, {"offset": {"line": 1200, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Bars3Icon, BellIcon } from '@heroicons/react/24/outline';\nimport { cn } from '@/lib/utils';\nimport MobileSidebar from './MobileSidebar';\n\ninterface HeaderProps {\n  title?: string;\n}\n\nconst Header: React.FC<HeaderProps> = ({ title = 'Dashboard' }) => {\n  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);\n\n  return (\n    <>\n      <header className=\"glass backdrop-blur-xl bg-white/80 dark:bg-slate-900/80 border-b border-white/20 dark:border-slate-700/50 px-6 py-4 shadow-lg\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center\">\n            {/* Mobile menu button */}\n            <button\n              onClick={() => setIsMobileSidebarOpen(true)}\n              className=\"lg:hidden p-2 rounded-xl text-slate-600 hover:text-slate-900 hover:bg-white/50 dark:text-slate-300 dark:hover:text-white dark:hover:bg-slate-800/50 transition-all duration-200 hover:shadow-md\"\n            >\n              <Bars3Icon className=\"h-6 w-6\" />\n            </button>\n\n            <h1 className=\"ml-4 lg:ml-0 text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">\n              {title}\n            </h1>\n          </div>\n\n        <div className=\"flex items-center space-x-4\">\n          {/* Notifications */}\n          <button className=\"p-2 rounded-xl text-slate-600 hover:text-slate-900 hover:bg-white/50 dark:text-slate-300 dark:hover:text-white dark:hover:bg-slate-800/50 transition-all duration-200 hover:shadow-md relative\">\n            <BellIcon className=\"h-6 w-6\" />\n            <span className=\"absolute -top-1 -right-1 h-3 w-3 bg-gradient-to-r from-red-500 to-pink-500 rounded-full animate-pulse\"></span>\n          </button>\n\n          {/* User Avatar */}\n          <div className=\"flex items-center\">\n            <div className=\"h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center shadow-lg ring-2 ring-white/20\">\n              <span className=\"text-sm font-bold text-white\">AD</span>\n            </div>\n            <div className=\"ml-3 hidden md:block\">\n              <p className=\"text-sm font-semibold text-slate-900 dark:text-white\">\n                Admin User\n              </p>\n              <p className=\"text-xs text-slate-500 dark:text-slate-400\">\n                <EMAIL>\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n\n    {/* Mobile Sidebar */}\n    <MobileSidebar\n      isOpen={isMobileSidebarOpen}\n      onClose={() => setIsMobileSidebarOpen(false)}\n    />\n  </>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAEA;;;AALA;;;;AAWA,MAAM,SAAgC;QAAC,EAAE,QAAQ,WAAW,EAAE;;IAC5D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,qBACE;;0BACE,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCACC,SAAS,IAAM,uBAAuB;oCACtC,WAAU;8CAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAGvB,6LAAC;oCAAG,WAAU;8CACX;;;;;;;;;;;;sCAIP,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAO,WAAU;;sDAChB,6LAAC,kNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;4CAAK,WAAU;;;;;;;;;;;;8CAIlB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAuD;;;;;;8DAGpE,6LAAC;oDAAE,WAAU;8DAA6C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUpE,6LAAC,gJAAA,CAAA,UAAa;gBACZ,QAAQ;gBACR,SAAS,IAAM,uBAAuB;;;;;;;;AAI5C;GArDM;KAAA;uCAuDS", "debugId": null}}, {"offset": {"line": 1374, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/components/layout/AuthenticatedLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { usePathname } from 'next/navigation';\nimport { useAuth } from '@/contexts/AuthContext';\nimport Sidebar from '@/components/layout/Sidebar';\nimport Header from '@/components/layout/Header';\nimport ProtectedRoute from '@/components/ProtectedRoute';\n\ninterface AuthenticatedLayoutProps {\n  children: React.ReactNode;\n}\n\nconst AuthenticatedLayout: React.FC<AuthenticatedLayoutProps> = ({ children }) => {\n  const { isAuthenticated, isLoading } = useAuth();\n  const pathname = usePathname();\n\n  // If we're on the login page, just render the children without the layout\n  if (pathname === '/login') {\n    return <>{children}</>;\n  }\n\n  // Don't render the layout if not authenticated (except for login page)\n  if (!isLoading && !isAuthenticated) {\n    return <>{children}</>;\n  }\n\n  return (\n    <div className=\"flex h-screen relative\">\n      {/* Background gradient overlay */}\n      <div className=\"fixed inset-0 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900\"></div>\n\n      {/* Sidebar */}\n      <div className=\"hidden lg:flex lg:flex-shrink-0 relative z-10\">\n        <Sidebar />\n      </div>\n\n      {/* Main content */}\n      <div className=\"flex flex-col flex-1 overflow-hidden relative z-10\">\n        <Header />\n        <main className=\"flex-1 overflow-y-auto p-6\">\n          <div className=\"max-w-7xl mx-auto\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n};\n\nexport default AuthenticatedLayout;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;;;AANA;;;;;AAaA,MAAM,sBAA0D;QAAC,EAAE,QAAQ,EAAE;;IAC3E,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC7C,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,0EAA0E;IAC1E,IAAI,aAAa,UAAU;QACzB,qBAAO;sBAAG;;IACZ;IAEA,uEAAuE;IACvE,IAAI,CAAC,aAAa,CAAC,iBAAiB;QAClC,qBAAO;sBAAG;;IACZ;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,0IAAA,CAAA,UAAO;;;;;;;;;;0BAIV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yIAAA,CAAA,UAAM;;;;;kCACP,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb;GAnCM;;QACmC,kIAAA,CAAA,UAAO;QAC7B,qIAAA,CAAA,cAAW;;;KAFxB;uCAqCS", "debugId": null}}]}