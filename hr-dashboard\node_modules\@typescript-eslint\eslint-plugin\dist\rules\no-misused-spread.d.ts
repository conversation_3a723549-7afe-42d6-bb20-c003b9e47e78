import type { TSESLint } from '@typescript-eslint/utils';
import type { TypeOrValueSpecifier } from '../util';
type Options = [
    {
        allow?: TypeOrValueSpecifier[];
    }
];
type MessageIds = 'addAwait' | 'noArraySpreadInObject' | 'noClassDeclarationSpreadInObject' | 'noClassInstanceSpreadInObject' | 'noFunctionSpreadInObject' | 'noIterableSpreadInObject' | 'noMapSpreadInObject' | 'noPromiseSpreadInObject' | 'noStringSpread' | 'replaceMapSpreadInObject';
declare const _default: TSESLint.RuleModule<MessageIds, Options, import("../../rules").ESLintPluginDocs, TSESLint.RuleListener>;
export default _default;
//# sourceMappingURL=no-misused-spread.d.ts.map