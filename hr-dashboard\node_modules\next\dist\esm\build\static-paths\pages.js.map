{"version": 3, "sources": ["../../../src/build/static-paths/pages.ts"], "sourcesContent": ["import type { GetStaticPaths } from '../../types'\nimport type { PrerenderedRoute, StaticPathsResult } from './types'\n\nimport { normalizeLocalePath } from '../../shared/lib/i18n/normalize-locale-path'\nimport { parseStaticPathsResult } from '../../lib/fallback'\nimport escapePathDelimiters from '../../shared/lib/router/utils/escape-path-delimiters'\nimport { removeTrailingSlash } from '../../shared/lib/router/utils/remove-trailing-slash'\nimport { getRouteMatcher } from '../../shared/lib/router/utils/route-matcher'\nimport { getRouteRegex } from '../../shared/lib/router/utils/route-regex'\nimport { encodeParam, normalizePathname } from './utils'\n\nexport async function buildPagesStaticPaths({\n  page,\n  getStaticPaths,\n  configFileName,\n  locales,\n  defaultLocale,\n}: {\n  page: string\n  getStaticPaths: GetStaticPaths\n  configFileName: string\n  locales?: readonly string[]\n  defaultLocale?: string\n}): Promise<StaticPathsResult> {\n  const prerenderedRoutesByPathname = new Map<string, PrerenderedRoute>()\n  const _routeRegex = getRouteRegex(page)\n  const _routeMatcher = getRouteMatcher(_routeRegex)\n\n  // Get the default list of allowed params.\n  const routeParameterKeys = Object.keys(_routeMatcher(page))\n  const staticPathsResult = await getStaticPaths({\n    // We create a copy here to avoid having the types of `getStaticPaths`\n    // change. This ensures that users can't mutate this array and have it\n    // poison the reference.\n    locales: [...(locales ?? [])],\n    defaultLocale,\n  })\n\n  const expectedReturnVal =\n    `Expected: { paths: [], fallback: boolean }\\n` +\n    `See here for more info: https://nextjs.org/docs/messages/invalid-getstaticpaths-value`\n\n  if (\n    !staticPathsResult ||\n    typeof staticPathsResult !== 'object' ||\n    Array.isArray(staticPathsResult)\n  ) {\n    throw new Error(\n      `Invalid value returned from getStaticPaths in ${page}. Received ${typeof staticPathsResult} ${expectedReturnVal}`\n    )\n  }\n\n  const invalidStaticPathKeys = Object.keys(staticPathsResult).filter(\n    (key) => !(key === 'paths' || key === 'fallback')\n  )\n\n  if (invalidStaticPathKeys.length > 0) {\n    throw new Error(\n      `Extra keys returned from getStaticPaths in ${page} (${invalidStaticPathKeys.join(\n        ', '\n      )}) ${expectedReturnVal}`\n    )\n  }\n\n  if (\n    !(\n      typeof staticPathsResult.fallback === 'boolean' ||\n      staticPathsResult.fallback === 'blocking'\n    )\n  ) {\n    throw new Error(\n      `The \\`fallback\\` key must be returned from getStaticPaths in ${page}.\\n` +\n        expectedReturnVal\n    )\n  }\n\n  const toPrerender = staticPathsResult.paths\n\n  if (!Array.isArray(toPrerender)) {\n    throw new Error(\n      `Invalid \\`paths\\` value returned from getStaticPaths in ${page}.\\n` +\n        `\\`paths\\` must be an array of strings or objects of shape { params: [key: string]: string }`\n    )\n  }\n\n  toPrerender.forEach((entry) => {\n    // For a string-provided path, we must make sure it matches the dynamic\n    // route.\n    if (typeof entry === 'string') {\n      entry = removeTrailingSlash(entry)\n\n      const localePathResult = normalizeLocalePath(entry, locales)\n      let cleanedEntry = entry\n\n      if (localePathResult.detectedLocale) {\n        cleanedEntry = entry.slice(localePathResult.detectedLocale.length + 1)\n      } else if (defaultLocale) {\n        entry = `/${defaultLocale}${entry}`\n      }\n\n      const params = _routeMatcher(cleanedEntry)\n      if (!params) {\n        throw new Error(\n          `The provided path \\`${cleanedEntry}\\` does not match the page: \\`${page}\\`.`\n        )\n      }\n\n      // If leveraging the string paths variant the entry should already be\n      // encoded so we decode the segments ensuring we only escape path\n      // delimiters\n      const pathname = entry\n        .split('/')\n        .map((segment) =>\n          escapePathDelimiters(decodeURIComponent(segment), true)\n        )\n        .join('/')\n\n      if (!prerenderedRoutesByPathname.has(pathname)) {\n        prerenderedRoutesByPathname.set(pathname, {\n          params,\n          pathname,\n          encodedPathname: entry,\n          fallbackRouteParams: undefined,\n          fallbackMode: parseStaticPathsResult(staticPathsResult.fallback),\n          fallbackRootParams: undefined,\n          throwOnEmptyStaticShell: undefined,\n        })\n      }\n    }\n    // For the object-provided path, we must make sure it specifies all\n    // required keys.\n    else {\n      const invalidKeys = Object.keys(entry).filter(\n        (key) => key !== 'params' && key !== 'locale'\n      )\n\n      if (invalidKeys.length) {\n        throw new Error(\n          `Additional keys were returned from \\`getStaticPaths\\` in page \"${page}\". ` +\n            `URL Parameters intended for this dynamic route must be nested under the \\`params\\` key, i.e.:` +\n            `\\n\\n\\treturn { params: { ${routeParameterKeys\n              .map((k) => `${k}: ...`)\n              .join(', ')} } }` +\n            `\\n\\nKeys that need to be moved: ${invalidKeys.join(', ')}.\\n`\n        )\n      }\n\n      const { params = {} } = entry\n      let builtPage = page\n      let encodedBuiltPage = page\n\n      routeParameterKeys.forEach((validParamKey) => {\n        const { repeat, optional } = _routeRegex.groups[validParamKey]\n        let paramValue = params[validParamKey]\n        if (\n          optional &&\n          params.hasOwnProperty(validParamKey) &&\n          (paramValue === null ||\n            paramValue === undefined ||\n            (paramValue as any) === false)\n        ) {\n          paramValue = []\n        }\n\n        if (\n          (repeat && !Array.isArray(paramValue)) ||\n          (!repeat && typeof paramValue !== 'string') ||\n          typeof paramValue === 'undefined'\n        ) {\n          throw new Error(\n            `A required parameter (${validParamKey}) was not provided as ${\n              repeat ? 'an array' : 'a string'\n            } received ${typeof paramValue} in getStaticPaths for ${page}`\n          )\n        }\n\n        let replaced = `[${repeat ? '...' : ''}${validParamKey}]`\n        if (optional) {\n          replaced = `[${replaced}]`\n        }\n\n        builtPage = builtPage.replace(\n          replaced,\n          encodeParam(paramValue, (value) => escapePathDelimiters(value, true))\n        )\n\n        encodedBuiltPage = encodedBuiltPage.replace(\n          replaced,\n          encodeParam(paramValue, encodeURIComponent)\n        )\n      })\n\n      if (!builtPage && !encodedBuiltPage) {\n        return\n      }\n\n      if (entry.locale && !locales?.includes(entry.locale)) {\n        throw new Error(\n          `Invalid locale returned from getStaticPaths for ${page}, the locale ${entry.locale} is not specified in ${configFileName}`\n        )\n      }\n      const curLocale = entry.locale || defaultLocale || ''\n\n      const pathname = normalizePathname(\n        `${curLocale ? `/${curLocale}` : ''}${curLocale && builtPage === '/' ? '' : builtPage}`\n      )\n\n      if (!prerenderedRoutesByPathname.has(pathname)) {\n        prerenderedRoutesByPathname.set(pathname, {\n          params,\n          pathname,\n          encodedPathname: normalizePathname(\n            `${curLocale ? `/${curLocale}` : ''}${\n              curLocale && encodedBuiltPage === '/' ? '' : encodedBuiltPage\n            }`\n          ),\n          fallbackRouteParams: undefined,\n          fallbackMode: parseStaticPathsResult(staticPathsResult.fallback),\n          fallbackRootParams: undefined,\n          throwOnEmptyStaticShell: undefined,\n        })\n      }\n    }\n  })\n\n  return {\n    fallbackMode: parseStaticPathsResult(staticPathsResult.fallback),\n    prerenderedRoutes: [...prerenderedRoutesByPathname.values()],\n  }\n}\n"], "names": ["normalizeLocalePath", "parseStaticPathsResult", "escapePathDelimiters", "removeTrailingSlash", "getRouteMatcher", "getRouteRegex", "encodeParam", "normalizePathname", "buildPagesStaticPaths", "page", "getStaticPaths", "configFileName", "locales", "defaultLocale", "prerenderedRoutesByPathname", "Map", "_routeRegex", "_routeMatcher", "routeParameterKeys", "Object", "keys", "staticPathsResult", "expectedReturnVal", "Array", "isArray", "Error", "invalidStatic<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "key", "length", "join", "fallback", "<PERSON><PERSON><PERSON><PERSON>", "paths", "for<PERSON>ach", "entry", "localePathResult", "cleanedEntry", "detectedLocale", "slice", "params", "pathname", "split", "map", "segment", "decodeURIComponent", "has", "set", "encodedPathname", "fallbackRouteParams", "undefined", "fallbackMode", "fallbackRootParams", "throwOnEmptyStaticShell", "<PERSON><PERSON><PERSON><PERSON>", "k", "builtPage", "encodedBuiltPage", "validParamKey", "repeat", "optional", "groups", "paramValue", "hasOwnProperty", "replaced", "replace", "value", "encodeURIComponent", "locale", "includes", "cur<PERSON><PERSON><PERSON>", "prerenderedRoutes", "values"], "mappings": "AAGA,SAASA,mBAAmB,QAAQ,8CAA6C;AACjF,SAASC,sBAAsB,QAAQ,qBAAoB;AAC3D,OAAOC,0BAA0B,uDAAsD;AACvF,SAASC,mBAAmB,QAAQ,sDAAqD;AACzF,SAASC,eAAe,QAAQ,8CAA6C;AAC7E,SAASC,aAAa,QAAQ,4CAA2C;AACzE,SAASC,WAAW,EAAEC,iBAAiB,QAAQ,UAAS;AAExD,OAAO,eAAeC,sBAAsB,EAC1CC,IAAI,EACJC,cAAc,EACdC,cAAc,EACdC,OAAO,EACPC,aAAa,EAOd;IACC,MAAMC,8BAA8B,IAAIC;IACxC,MAAMC,cAAcX,cAAcI;IAClC,MAAMQ,gBAAgBb,gBAAgBY;IAEtC,0CAA0C;IAC1C,MAAME,qBAAqBC,OAAOC,IAAI,CAACH,cAAcR;IACrD,MAAMY,oBAAoB,MAAMX,eAAe;QAC7C,sEAAsE;QACtE,sEAAsE;QACtE,wBAAwB;QACxBE,SAAS;eAAKA,WAAW,EAAE;SAAE;QAC7BC;IACF;IAEA,MAAMS,oBACJ,CAAC,4CAA4C,CAAC,GAC9C,CAAC,qFAAqF,CAAC;IAEzF,IACE,CAACD,qBACD,OAAOA,sBAAsB,YAC7BE,MAAMC,OAAO,CAACH,oBACd;QACA,MAAM,qBAEL,CAFK,IAAII,MACR,CAAC,8CAA8C,EAAEhB,KAAK,WAAW,EAAE,OAAOY,kBAAkB,CAAC,EAAEC,mBAAmB,GAD9G,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMI,wBAAwBP,OAAOC,IAAI,CAACC,mBAAmBM,MAAM,CACjE,CAACC,MAAQ,CAAEA,CAAAA,QAAQ,WAAWA,QAAQ,UAAS;IAGjD,IAAIF,sBAAsBG,MAAM,GAAG,GAAG;QACpC,MAAM,qBAIL,CAJK,IAAIJ,MACR,CAAC,2CAA2C,EAAEhB,KAAK,EAAE,EAAEiB,sBAAsBI,IAAI,CAC/E,MACA,EAAE,EAAER,mBAAmB,GAHrB,qBAAA;mBAAA;wBAAA;0BAAA;QAIN;IACF;IAEA,IACE,CACE,CAAA,OAAOD,kBAAkBU,QAAQ,KAAK,aACtCV,kBAAkBU,QAAQ,KAAK,UAAS,GAE1C;QACA,MAAM,qBAGL,CAHK,IAAIN,MACR,CAAC,6DAA6D,EAAEhB,KAAK,GAAG,CAAC,GACvEa,oBAFE,qBAAA;mBAAA;wBAAA;0BAAA;QAGN;IACF;IAEA,MAAMU,cAAcX,kBAAkBY,KAAK;IAE3C,IAAI,CAACV,MAAMC,OAAO,CAACQ,cAAc;QAC/B,MAAM,qBAGL,CAHK,IAAIP,MACR,CAAC,wDAAwD,EAAEhB,KAAK,GAAG,CAAC,GAClE,CAAC,2FAA2F,CAAC,GAF3F,qBAAA;mBAAA;wBAAA;0BAAA;QAGN;IACF;IAEAuB,YAAYE,OAAO,CAAC,CAACC;QACnB,uEAAuE;QACvE,SAAS;QACT,IAAI,OAAOA,UAAU,UAAU;YAC7BA,QAAQhC,oBAAoBgC;YAE5B,MAAMC,mBAAmBpC,oBAAoBmC,OAAOvB;YACpD,IAAIyB,eAAeF;YAEnB,IAAIC,iBAAiBE,cAAc,EAAE;gBACnCD,eAAeF,MAAMI,KAAK,CAACH,iBAAiBE,cAAc,CAACT,MAAM,GAAG;YACtE,OAAO,IAAIhB,eAAe;gBACxBsB,QAAQ,CAAC,CAAC,EAAEtB,gBAAgBsB,OAAO;YACrC;YAEA,MAAMK,SAASvB,cAAcoB;YAC7B,IAAI,CAACG,QAAQ;gBACX,MAAM,qBAEL,CAFK,IAAIf,MACR,CAAC,oBAAoB,EAAEY,aAAa,8BAA8B,EAAE5B,KAAK,GAAG,CAAC,GADzE,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,qEAAqE;YACrE,iEAAiE;YACjE,aAAa;YACb,MAAMgC,WAAWN,MACdO,KAAK,CAAC,KACNC,GAAG,CAAC,CAACC,UACJ1C,qBAAqB2C,mBAAmBD,UAAU,OAEnDd,IAAI,CAAC;YAER,IAAI,CAAChB,4BAA4BgC,GAAG,CAACL,WAAW;gBAC9C3B,4BAA4BiC,GAAG,CAACN,UAAU;oBACxCD;oBACAC;oBACAO,iBAAiBb;oBACjBc,qBAAqBC;oBACrBC,cAAclD,uBAAuBoB,kBAAkBU,QAAQ;oBAC/DqB,oBAAoBF;oBACpBG,yBAAyBH;gBAC3B;YACF;QACF,OAGK;YACH,MAAMI,cAAcnC,OAAOC,IAAI,CAACe,OAAOR,MAAM,CAC3C,CAACC,MAAQA,QAAQ,YAAYA,QAAQ;YAGvC,IAAI0B,YAAYzB,MAAM,EAAE;gBACtB,MAAM,qBAOL,CAPK,IAAIJ,MACR,CAAC,+DAA+D,EAAEhB,KAAK,GAAG,CAAC,GACzE,CAAC,6FAA6F,CAAC,GAC/F,CAAC,yBAAyB,EAAES,mBACzByB,GAAG,CAAC,CAACY,IAAM,GAAGA,EAAE,KAAK,CAAC,EACtBzB,IAAI,CAAC,MAAM,IAAI,CAAC,GACnB,CAAC,gCAAgC,EAAEwB,YAAYxB,IAAI,CAAC,MAAM,GAAG,CAAC,GAN5D,qBAAA;2BAAA;gCAAA;kCAAA;gBAON;YACF;YAEA,MAAM,EAAEU,SAAS,CAAC,CAAC,EAAE,GAAGL;YACxB,IAAIqB,YAAY/C;YAChB,IAAIgD,mBAAmBhD;YAEvBS,mBAAmBgB,OAAO,CAAC,CAACwB;gBAC1B,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAE,GAAG5C,YAAY6C,MAAM,CAACH,cAAc;gBAC9D,IAAII,aAAatB,MAAM,CAACkB,cAAc;gBACtC,IACEE,YACApB,OAAOuB,cAAc,CAACL,kBACrBI,CAAAA,eAAe,QACdA,eAAeZ,aACf,AAACY,eAAuB,KAAI,GAC9B;oBACAA,aAAa,EAAE;gBACjB;gBAEA,IACE,AAACH,UAAU,CAACpC,MAAMC,OAAO,CAACsC,eACzB,CAACH,UAAU,OAAOG,eAAe,YAClC,OAAOA,eAAe,aACtB;oBACA,MAAM,qBAIL,CAJK,IAAIrC,MACR,CAAC,sBAAsB,EAAEiC,cAAc,sBAAsB,EAC3DC,SAAS,aAAa,WACvB,UAAU,EAAE,OAAOG,WAAW,uBAAuB,EAAErD,MAAM,GAH1D,qBAAA;+BAAA;oCAAA;sCAAA;oBAIN;gBACF;gBAEA,IAAIuD,WAAW,CAAC,CAAC,EAAEL,SAAS,QAAQ,KAAKD,cAAc,CAAC,CAAC;gBACzD,IAAIE,UAAU;oBACZI,WAAW,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC;gBAC5B;gBAEAR,YAAYA,UAAUS,OAAO,CAC3BD,UACA1D,YAAYwD,YAAY,CAACI,QAAUhE,qBAAqBgE,OAAO;gBAGjET,mBAAmBA,iBAAiBQ,OAAO,CACzCD,UACA1D,YAAYwD,YAAYK;YAE5B;YAEA,IAAI,CAACX,aAAa,CAACC,kBAAkB;gBACnC;YACF;YAEA,IAAItB,MAAMiC,MAAM,IAAI,EAACxD,2BAAAA,QAASyD,QAAQ,CAAClC,MAAMiC,MAAM,IAAG;gBACpD,MAAM,qBAEL,CAFK,IAAI3C,MACR,CAAC,gDAAgD,EAAEhB,KAAK,aAAa,EAAE0B,MAAMiC,MAAM,CAAC,qBAAqB,EAAEzD,gBAAgB,GADvH,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,MAAM2D,YAAYnC,MAAMiC,MAAM,IAAIvD,iBAAiB;YAEnD,MAAM4B,WAAWlC,kBACf,GAAG+D,YAAY,CAAC,CAAC,EAAEA,WAAW,GAAG,KAAKA,aAAad,cAAc,MAAM,KAAKA,WAAW;YAGzF,IAAI,CAAC1C,4BAA4BgC,GAAG,CAACL,WAAW;gBAC9C3B,4BAA4BiC,GAAG,CAACN,UAAU;oBACxCD;oBACAC;oBACAO,iBAAiBzC,kBACf,GAAG+D,YAAY,CAAC,CAAC,EAAEA,WAAW,GAAG,KAC/BA,aAAab,qBAAqB,MAAM,KAAKA,kBAC7C;oBAEJR,qBAAqBC;oBACrBC,cAAclD,uBAAuBoB,kBAAkBU,QAAQ;oBAC/DqB,oBAAoBF;oBACpBG,yBAAyBH;gBAC3B;YACF;QACF;IACF;IAEA,OAAO;QACLC,cAAclD,uBAAuBoB,kBAAkBU,QAAQ;QAC/DwC,mBAAmB;eAAIzD,4BAA4B0D,MAAM;SAAG;IAC9D;AACF", "ignoreList": [0]}