{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\nimport { Employee, User, Project, Feedback, PerformanceRecord } from '@/types';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// Department options for random assignment\nconst DEPARTMENTS = [\n  'Engineering',\n  'Human Resources',\n  'Sales',\n  'Marketing',\n  'Finance',\n  'Operations',\n  'Customer Support',\n  'Product Management',\n  'Design',\n  'Legal'\n];\n\n// Generate random department\nexport function getRandomDepartment(): string {\n  return DEPARTMENTS[Math.floor(Math.random() * DEPARTMENTS.length)];\n}\n\n// Generate random performance rating (1-5)\nexport function getRandomRating(): number {\n  return Math.floor(Math.random() * 5) + 1;\n}\n\n// Generate random bio\nexport function generateBio(firstName: string, lastName: string, department: string): string {\n  const bios = [\n    `${firstName} ${lastName} is a dedicated professional in the ${department} department with a passion for excellence and innovation.`,\n    `With years of experience in ${department}, ${firstName} brings valuable expertise and leadership to our team.`,\n    `${firstName} is known for their collaborative approach and commitment to delivering high-quality results in ${department}.`,\n    `A results-driven professional, ${firstName} ${lastName} consistently exceeds expectations in their role within ${department}.`,\n    `${firstName} combines technical expertise with strong communication skills, making them a valuable asset to the ${department} team.`\n  ];\n  return bios[Math.floor(Math.random() * bios.length)];\n}\n\n// Generate mock projects\nexport function generateProjects(): Project[] {\n  const projectNames = [\n    'Customer Portal Redesign',\n    'Mobile App Development',\n    'Data Analytics Platform',\n    'Security Audit',\n    'Performance Optimization',\n    'User Experience Research',\n    'API Integration',\n    'Cloud Migration',\n    'Automation Framework',\n    'Quality Assurance'\n  ];\n\n  const roles = ['Lead Developer', 'Project Manager', 'Designer', 'Analyst', 'Consultant', 'Coordinator'];\n  const statuses: ('active' | 'completed' | 'on-hold')[] = ['active', 'completed', 'on-hold'];\n\n  const numProjects = Math.floor(Math.random() * 4) + 1; // 1-4 projects\n  const projects: Project[] = [];\n\n  for (let i = 0; i < numProjects; i++) {\n    projects.push({\n      id: `proj-${Math.random().toString(36).substr(2, 9)}`,\n      name: projectNames[Math.floor(Math.random() * projectNames.length)],\n      role: roles[Math.floor(Math.random() * roles.length)],\n      status: statuses[Math.floor(Math.random() * statuses.length)],\n      startDate: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],\n      endDate: Math.random() > 0.5 ? new Date(Date.now() + Math.random() * 180 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] : undefined\n    });\n  }\n\n  return projects;\n}\n\n// Generate mock feedback\nexport function generateFeedback(): Feedback[] {\n  const feedbackComments = [\n    'Excellent work on the recent project. Great attention to detail.',\n    'Shows strong leadership skills and helps team members grow.',\n    'Consistently delivers high-quality work on time.',\n    'Great communication skills and collaborative approach.',\n    'Innovative thinking and problem-solving abilities.',\n    'Reliable team player who goes above and beyond.',\n    'Strong technical skills and willingness to learn.',\n    'Positive attitude and great work ethic.'\n  ];\n\n  const names = ['John Smith', 'Sarah Johnson', 'Mike Davis', 'Emily Brown', 'David Wilson', 'Lisa Garcia'];\n\n  const numFeedback = Math.floor(Math.random() * 3) + 1; // 1-3 feedback items\n  const feedback: Feedback[] = [];\n\n  for (let i = 0; i < numFeedback; i++) {\n    feedback.push({\n      id: `feedback-${Math.random().toString(36).substr(2, 9)}`,\n      from: names[Math.floor(Math.random() * names.length)],\n      comment: feedbackComments[Math.floor(Math.random() * feedbackComments.length)],\n      rating: Math.floor(Math.random() * 5) + 1,\n      date: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]\n    });\n  }\n\n  return feedback;\n}\n\n// Generate performance history\nexport function generatePerformanceHistory(): PerformanceRecord[] {\n  const quarters = ['Q1 2024', 'Q2 2024', 'Q3 2024', 'Q4 2023'];\n  const goals = [\n    'Improve code quality',\n    'Enhance team collaboration',\n    'Complete certification',\n    'Mentor junior developers',\n    'Optimize system performance',\n    'Implement new features'\n  ];\n  const achievements = [\n    'Reduced bug count by 30%',\n    'Led successful project delivery',\n    'Improved team productivity',\n    'Implemented new process',\n    'Received client commendation',\n    'Completed training program'\n  ];\n\n  return quarters.map(quarter => ({\n    quarter,\n    rating: Math.floor(Math.random() * 5) + 1,\n    goals: goals.slice(0, Math.floor(Math.random() * 3) + 1),\n    achievements: achievements.slice(0, Math.floor(Math.random() * 3) + 1)\n  }));\n}\n\n// Transform User to Employee with additional HR data\nexport function transformUserToEmployee(user: User): Employee {\n  const department = getRandomDepartment();\n  const performanceRating = getRandomRating();\n\n  return {\n    ...user,\n    department,\n    performanceRating,\n    bio: generateBio(user.firstName, user.lastName, department),\n    projects: generateProjects(),\n    feedback: generateFeedback(),\n    performanceHistory: generatePerformanceHistory()\n  };\n}\n\n// Get performance badge variant based on rating\nexport function getPerformanceBadgeVariant(rating: number): 'primary' | 'secondary' | 'success' | 'warning' | 'danger' {\n  if (rating >= 5) return 'success';\n  if (rating >= 4) return 'primary';\n  if (rating >= 3) return 'warning';\n  if (rating >= 2) return 'secondary';\n  return 'danger';\n}\n\n// Get performance label based on rating\nexport function getPerformanceLabel(rating: number): string {\n  if (rating >= 5) return 'Outstanding';\n  if (rating >= 4) return 'Excellent';\n  if (rating >= 3) return 'Good';\n  if (rating >= 2) return 'Fair';\n  return 'Needs Improvement';\n}\n\n// Format date for display\nexport function formatDate(dateString: string): string {\n  return new Date(dateString).toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric'\n  });\n}\n\n// Debounce function for search\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n// Calculate department statistics\nexport function calculateDepartmentStats(employees: Employee[]) {\n  const departmentMap = new Map<string, { totalRating: number; count: number }>();\n\n  employees.forEach(employee => {\n    const current = departmentMap.get(employee.department) || { totalRating: 0, count: 0 };\n    departmentMap.set(employee.department, {\n      totalRating: current.totalRating + employee.performanceRating,\n      count: current.count + 1\n    });\n  });\n\n  return Array.from(departmentMap.entries()).map(([department, stats]) => ({\n    department,\n    averageRating: stats.totalRating / stats.count,\n    employeeCount: stats.count\n  }));\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;AAGO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEA,2CAA2C;AAC3C,MAAM,cAAc;IAClB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAGM,SAAS;IACd,OAAO,WAAW,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,YAAY,MAAM,EAAE;AACpE;AAGO,SAAS;IACd,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK;AACzC;AAGO,SAAS,YAAY,SAAiB,EAAE,QAAgB,EAAE,UAAkB;IACjF,MAAM,OAAO;QACX,GAAG,UAAU,CAAC,EAAE,SAAS,oCAAoC,EAAE,WAAW,yDAAyD,CAAC;QACpI,CAAC,4BAA4B,EAAE,WAAW,EAAE,EAAE,UAAU,sDAAsD,CAAC;QAC/G,GAAG,UAAU,gGAAgG,EAAE,WAAW,CAAC,CAAC;QAC5H,CAAC,+BAA+B,EAAE,UAAU,CAAC,EAAE,SAAS,wDAAwD,EAAE,WAAW,CAAC,CAAC;QAC/H,GAAG,UAAU,oGAAoG,EAAE,WAAW,MAAM,CAAC;KACtI;IACD,OAAO,IAAI,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK,MAAM,EAAE;AACtD;AAGO,SAAS;IACd,MAAM,eAAe;QACnB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,QAAQ;QAAC;QAAkB;QAAmB;QAAY;QAAW;QAAc;KAAc;IACvG,MAAM,WAAmD;QAAC;QAAU;QAAa;KAAU;IAE3F,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK,GAAG,eAAe;IACtE,MAAM,WAAsB,EAAE;IAE9B,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,IAAK;QACpC,SAAS,IAAI,CAAC;YACZ,IAAI,CAAC,KAAK,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;YACrD,MAAM,YAAY,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,aAAa,MAAM,EAAE;YACnE,MAAM,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM,EAAE;YACrD,QAAQ,QAAQ,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS,MAAM,EAAE;YAC7D,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,MAAM,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YACvG,SAAS,KAAK,MAAM,KAAK,MAAM,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,MAAM,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;QAChI;IACF;IAEA,OAAO;AACT;AAGO,SAAS;IACd,MAAM,mBAAmB;QACvB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,QAAQ;QAAC;QAAc;QAAiB;QAAc;QAAe;QAAgB;KAAc;IAEzG,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK,GAAG,qBAAqB;IAC5E,MAAM,WAAuB,EAAE;IAE/B,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,IAAK;QACpC,SAAS,IAAI,CAAC;YACZ,IAAI,CAAC,SAAS,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;YACzD,MAAM,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM,EAAE;YACrD,SAAS,gBAAgB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,iBAAiB,MAAM,EAAE;YAC9E,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK;YACxC,MAAM,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACnG;IACF;IAEA,OAAO;AACT;AAGO,SAAS;IACd,MAAM,WAAW;QAAC;QAAW;QAAW;QAAW;KAAU;IAC7D,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,eAAe;QACnB;QACA;QACA;QACA;QACA;QACA;KACD;IAED,OAAO,SAAS,GAAG,CAAC,CAAA,UAAW,CAAC;YAC9B;YACA,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK;YACxC,OAAO,MAAM,KAAK,CAAC,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK;YACtD,cAAc,aAAa,KAAK,CAAC,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK;QACtE,CAAC;AACH;AAGO,SAAS,wBAAwB,IAAU;IAChD,MAAM,aAAa;IACnB,MAAM,oBAAoB;IAE1B,OAAO;QACL,GAAG,IAAI;QACP;QACA;QACA,KAAK,YAAY,KAAK,SAAS,EAAE,KAAK,QAAQ,EAAE;QAChD,UAAU;QACV,UAAU;QACV,oBAAoB;IACtB;AACF;AAGO,SAAS,2BAA2B,MAAc;IACvD,IAAI,UAAU,GAAG,OAAO;IACxB,IAAI,UAAU,GAAG,OAAO;IACxB,IAAI,UAAU,GAAG,OAAO;IACxB,IAAI,UAAU,GAAG,OAAO;IACxB,OAAO;AACT;AAGO,SAAS,oBAAoB,MAAc;IAChD,IAAI,UAAU,GAAG,OAAO;IACxB,IAAI,UAAU,GAAG,OAAO;IACxB,IAAI,UAAU,GAAG,OAAO;IACxB,IAAI,UAAU,GAAG,OAAO;IACxB,OAAO;AACT;AAGO,SAAS,WAAW,UAAkB;IAC3C,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;QACtD,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS,yBAAyB,SAAqB;IAC5D,MAAM,gBAAgB,IAAI;IAE1B,UAAU,OAAO,CAAC,CAAA;QAChB,MAAM,UAAU,cAAc,GAAG,CAAC,SAAS,UAAU,KAAK;YAAE,aAAa;YAAG,OAAO;QAAE;QACrF,cAAc,GAAG,CAAC,SAAS,UAAU,EAAE;YACrC,aAAa,QAAQ,WAAW,GAAG,SAAS,iBAAiB;YAC7D,OAAO,QAAQ,KAAK,GAAG;QACzB;IACF;IAEA,OAAO,MAAM,IAAI,CAAC,cAAc,OAAO,IAAI,GAAG,CAAC,CAAC,CAAC,YAAY,MAAM,GAAK,CAAC;YACvE;YACA,eAAe,MAAM,WAAW,GAAG,MAAM,KAAK;YAC9C,eAAe,MAAM,KAAK;QAC5B,CAAC;AACH", "debugId": null}}, {"offset": {"line": 221, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/hooks/useSearch.ts"], "sourcesContent": ["import { useState, useMemo } from 'react';\nimport { Employee, SearchFilters } from '@/types';\n\nexport const useSearch = (employees: Employee[]) => {\n  const [filters, setFilters] = useState<SearchFilters>({\n    query: '',\n    departments: [],\n    minRating: 0,\n    maxRating: 5\n  });\n\n  const filteredEmployees = useMemo(() => {\n    return employees.filter(employee => {\n      // Text search\n      const matchesQuery = filters.query === '' || \n        employee.firstName.toLowerCase().includes(filters.query.toLowerCase()) ||\n        employee.lastName.toLowerCase().includes(filters.query.toLowerCase()) ||\n        employee.email.toLowerCase().includes(filters.query.toLowerCase()) ||\n        employee.department.toLowerCase().includes(filters.query.toLowerCase());\n\n      // Department filter\n      const matchesDepartment = filters.departments.length === 0 || \n        filters.departments.includes(employee.department);\n\n      // Rating filter\n      const matchesRating = employee.performanceRating >= filters.minRating && \n        employee.performanceRating <= filters.maxRating;\n\n      return matchesQuery && matchesDepartment && matchesRating;\n    });\n  }, [employees, filters]);\n\n  const updateQuery = (query: string) => {\n    setFilters(prev => ({ ...prev, query }));\n  };\n\n  const updateDepartments = (departments: string[]) => {\n    setFilters(prev => ({ ...prev, departments }));\n  };\n\n  const updateRatingRange = (minRating: number, maxRating: number) => {\n    setFilters(prev => ({ ...prev, minRating, maxRating }));\n  };\n\n  const clearFilters = () => {\n    setFilters({\n      query: '',\n      departments: [],\n      minRating: 0,\n      maxRating: 5\n    });\n  };\n\n  const availableDepartments = useMemo(() => {\n    const departments = new Set(employees.map(emp => emp.department));\n    return Array.from(departments).sort();\n  }, [employees]);\n\n  return {\n    filters,\n    filteredEmployees,\n    updateQuery,\n    updateDepartments,\n    updateRatingRange,\n    clearFilters,\n    availableDepartments\n  };\n};\n"], "names": [], "mappings": ";;;AAAA;;AAGO,MAAM,YAAY,CAAC;IACxB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QACpD,OAAO;QACP,aAAa,EAAE;QACf,WAAW;QACX,WAAW;IACb;IAEA,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAChC,OAAO,UAAU,MAAM,CAAC,CAAA;YACtB,cAAc;YACd,MAAM,eAAe,QAAQ,KAAK,KAAK,MACrC,SAAS,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,KAAK,CAAC,WAAW,OACnE,SAAS,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,KAAK,CAAC,WAAW,OAClE,SAAS,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,KAAK,CAAC,WAAW,OAC/D,SAAS,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,KAAK,CAAC,WAAW;YAEtE,oBAAoB;YACpB,MAAM,oBAAoB,QAAQ,WAAW,CAAC,MAAM,KAAK,KACvD,QAAQ,WAAW,CAAC,QAAQ,CAAC,SAAS,UAAU;YAElD,gBAAgB;YAChB,MAAM,gBAAgB,SAAS,iBAAiB,IAAI,QAAQ,SAAS,IACnE,SAAS,iBAAiB,IAAI,QAAQ,SAAS;YAEjD,OAAO,gBAAgB,qBAAqB;QAC9C;IACF,GAAG;QAAC;QAAW;KAAQ;IAEvB,MAAM,cAAc,CAAC;QACnB,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE;YAAM,CAAC;IACxC;IAEA,MAAM,oBAAoB,CAAC;QACzB,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE;YAAY,CAAC;IAC9C;IAEA,MAAM,oBAAoB,CAAC,WAAmB;QAC5C,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE;gBAAW;YAAU,CAAC;IACvD;IAEA,MAAM,eAAe;QACnB,WAAW;YACT,OAAO;YACP,aAAa,EAAE;YACf,WAAW;YACX,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACnC,MAAM,cAAc,IAAI,IAAI,UAAU,GAAG,CAAC,CAAA,MAAO,IAAI,UAAU;QAC/D,OAAO,MAAM,IAAI,CAAC,aAAa,IAAI;IACrC,GAAG;QAAC;KAAU;IAEd,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 294, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/hooks/useBookmarks.ts"], "sourcesContent": ["import { useBookmarkContext } from '@/contexts/BookmarkContext';\n\nexport const useBookmarks = () => {\n  return useBookmarkContext();\n};\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,eAAe;IAC1B,OAAO,CAAA,GAAA,mIAAA,CAAA,qBAAkB,AAAD;AAC1B", "debugId": null}}, {"offset": {"line": 330, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/components/ui/Badge.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\nimport { BadgeProps } from '@/types';\n\nconst Badge: React.FC<BadgeProps> = ({\n  children,\n  variant,\n  size = 'md',\n  ...props\n}) => {\n  const baseClasses = 'inline-flex items-center font-medium rounded-full';\n  \n  const variants = {\n    primary: 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg',\n    secondary: 'bg-gradient-to-r from-slate-100 to-slate-200 text-slate-800 dark:from-slate-700 dark:to-slate-600 dark:text-slate-200 shadow-md',\n    success: 'bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-lg',\n    warning: 'bg-gradient-to-r from-yellow-500 to-orange-600 text-white shadow-lg',\n    danger: 'bg-gradient-to-r from-red-500 to-pink-600 text-white shadow-lg'\n  };\n\n  const sizes = {\n    sm: 'px-2 py-0.5 text-xs',\n    md: 'px-2.5 py-1 text-sm',\n    lg: 'px-3 py-1.5 text-base'\n  };\n\n  return (\n    <span\n      className={cn(\n        baseClasses,\n        variants[variant],\n        sizes[size]\n      )}\n      {...props}\n    >\n      {children}\n    </span>\n  );\n};\n\nexport default Badge;\n"], "names": [], "mappings": ";;;;AACA;;;AAGA,MAAM,QAA8B,CAAC,EACnC,QAAQ,EACR,OAAO,EACP,OAAO,IAAI,EACX,GAAG,OACJ;IACC,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,SAAS;QACT,QAAQ;IACV;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK;QAEZ,GAAG,KAAK;kBAER;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 366, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\nimport { ButtonProps } from '@/types';\n\nconst Button: React.FC<ButtonProps> = ({\n  children,\n  variant = 'primary',\n  size = 'md',\n  loading = false,\n  disabled = false,\n  onClick,\n  type = 'button',\n  className,\n  ...props\n}) => {\n  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none';\n  \n  const variants = {\n    primary: 'bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700 focus:ring-blue-500 shadow-lg hover:shadow-xl transform hover:scale-105',\n    secondary: 'bg-gradient-to-r from-slate-100 to-slate-200 text-slate-900 hover:from-slate-200 hover:to-slate-300 focus:ring-slate-500 dark:from-slate-800 dark:to-slate-700 dark:text-slate-100 dark:hover:from-slate-700 dark:hover:to-slate-600 shadow-md hover:shadow-lg',\n    outline: 'border border-slate-300 text-slate-700 hover:bg-white/50 focus:ring-slate-500 dark:border-slate-600 dark:text-slate-300 dark:hover:bg-slate-800/50 backdrop-blur-sm',\n    ghost: 'text-slate-700 hover:bg-white/50 focus:ring-slate-500 dark:text-slate-300 dark:hover:bg-slate-800/50 backdrop-blur-sm'\n  };\n\n  const sizes = {\n    sm: 'px-3 py-1.5 text-sm',\n    md: 'px-4 py-2 text-sm',\n    lg: 'px-6 py-3 text-base'\n  };\n\n  return (\n    <button\n      type={type}\n      onClick={onClick}\n      disabled={disabled || loading}\n      className={cn(\n        baseClasses,\n        variants[variant],\n        sizes[size],\n        className\n      )}\n      {...props}\n    >\n      {loading && (\n        <svg\n          className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n          fill=\"none\"\n          viewBox=\"0 0 24 24\"\n        >\n          <circle\n            className=\"opacity-25\"\n            cx=\"12\"\n            cy=\"12\"\n            r=\"10\"\n            stroke=\"currentColor\"\n            strokeWidth=\"4\"\n          />\n          <path\n            className=\"opacity-75\"\n            fill=\"currentColor\"\n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n          />\n        </svg>\n      )}\n      {children}\n    </button>\n  );\n};\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AACA;;;AAGA,MAAM,SAAgC,CAAC,EACrC,QAAQ,EACR,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,OAAO,EACP,OAAO,QAAQ,EACf,SAAS,EACT,GAAG,OACJ;IACC,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,MAAM;QACN,SAAS;QACT,UAAU,YAAY;QACtB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAED,GAAG,KAAK;;YAER,yBACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 439, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/components/ui/StarRating.tsx"], "sourcesContent": ["import React from 'react';\nimport { StarIcon } from '@heroicons/react/24/solid';\nimport { StarIcon as StarOutlineIcon } from '@heroicons/react/24/outline';\nimport { cn } from '@/lib/utils';\n\ninterface StarRatingProps {\n  rating: number;\n  maxRating?: number;\n  size?: 'sm' | 'md' | 'lg';\n  showValue?: boolean;\n  interactive?: boolean;\n  onRatingChange?: (rating: number) => void;\n  className?: string;\n}\n\nconst StarRating: React.FC<StarRatingProps> = ({\n  rating,\n  maxRating = 5,\n  size = 'md',\n  showValue = false,\n  interactive = false,\n  onRatingChange,\n  className\n}) => {\n  const sizeClasses = {\n    sm: 'h-4 w-4',\n    md: 'h-5 w-5',\n    lg: 'h-6 w-6'\n  };\n\n  const handleStarClick = (starRating: number) => {\n    if (interactive && onRatingChange) {\n      onRatingChange(starRating);\n    }\n  };\n\n  return (\n    <div className={cn('flex items-center gap-1', className)}>\n      <div className=\"flex\">\n        {Array.from({ length: maxRating }, (_, index) => {\n          const starRating = index + 1;\n          const isFilled = starRating <= rating;\n          \n          return (\n            <button\n              key={index}\n              type=\"button\"\n              onClick={() => handleStarClick(starRating)}\n              disabled={!interactive}\n              className={cn(\n                'transition-colors',\n                interactive ? 'hover:scale-110 cursor-pointer' : 'cursor-default',\n                isFilled ? 'text-yellow-400' : 'text-secondary-300 dark:text-secondary-600'\n              )}\n            >\n              {isFilled ? (\n                <StarIcon className={sizeClasses[size]} />\n              ) : (\n                <StarOutlineIcon className={sizeClasses[size]} />\n              )}\n            </button>\n          );\n        })}\n      </div>\n      \n      {showValue && (\n        <span className=\"ml-2 text-sm text-secondary-600 dark:text-secondary-400\">\n          {rating.toFixed(1)}/{maxRating}\n        </span>\n      )}\n    </div>\n  );\n};\n\nexport default StarRating;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAYA,MAAM,aAAwC,CAAC,EAC7C,MAAM,EACN,YAAY,CAAC,EACb,OAAO,IAAI,EACX,YAAY,KAAK,EACjB,cAAc,KAAK,EACnB,cAAc,EACd,SAAS,EACV;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,eAAe,gBAAgB;YACjC,eAAe;QACjB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;;0BAC5C,8OAAC;gBAAI,WAAU;0BACZ,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAU,GAAG,CAAC,GAAG;oBACrC,MAAM,aAAa,QAAQ;oBAC3B,MAAM,WAAW,cAAc;oBAE/B,qBACE,8OAAC;wBAEC,MAAK;wBACL,SAAS,IAAM,gBAAgB;wBAC/B,UAAU,CAAC;wBACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qBACA,cAAc,mCAAmC,kBACjD,WAAW,oBAAoB;kCAGhC,yBACC,8OAAC,6MAAA,CAAA,WAAQ;4BAAC,WAAW,WAAW,CAAC,KAAK;;;;;qFAEtC,8OAAC,+MAAA,CAAA,WAAe;4BAAC,WAAW,WAAW,CAAC,KAAK;;;;;;uBAb1C;;;;;gBAiBX;;;;;;YAGD,2BACC,8OAAC;gBAAK,WAAU;;oBACb,OAAO,OAAO,CAAC;oBAAG;oBAAE;;;;;;;;;;;;;AAK/B;uCAEe", "debugId": null}}, {"offset": {"line": 524, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/components/ui/Modal.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { cn } from '@/lib/utils';\nimport { ModalProps } from '@/types';\nimport { XMarkIcon } from '@heroicons/react/24/outline';\n\nconst Modal: React.FC<ModalProps> = ({\n  isOpen,\n  onClose,\n  title,\n  children,\n  size = 'md'\n}) => {\n  useEffect(() => {\n    const handleEscape = (e: KeyboardEvent) => {\n      if (e.key === 'Escape') {\n        onClose();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'hidden';\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen, onClose]);\n\n  if (!isOpen) return null;\n\n  const sizeClasses = {\n    sm: 'max-w-md',\n    md: 'max-w-lg',\n    lg: 'max-w-2xl',\n    xl: 'max-w-4xl'\n  };\n\n  return (\n    <div className=\"fixed inset-0 z-[9999] overflow-y-auto\">\n      <div className=\"flex min-h-screen items-center justify-center p-4\">\n        {/* Backdrop */}\n        <div\n          className=\"fixed inset-0 bg-black/60 backdrop-blur-sm transition-opacity\"\n          onClick={onClose}\n        />\n\n        {/* Modal */}\n        <div\n          className={cn(\n            'relative w-full glass backdrop-blur-xl bg-white/95 dark:bg-slate-900/95 border border-white/20 dark:border-slate-700/50 rounded-2xl shadow-2xl transform transition-all',\n            sizeClasses[size]\n          )}\n        >\n          {/* Header */}\n          <div className=\"flex items-center justify-between p-6 border-b border-gray-200/50 dark:border-gray-700/50\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n              {title}\n            </h3>\n            <button\n              onClick={onClose}\n              className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors p-1 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800\"\n            >\n              <XMarkIcon className=\"h-6 w-6\" />\n            </button>\n          </div>\n\n          {/* Content */}\n          <div className=\"p-6 text-gray-900 dark:text-white\">\n            {children}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Modal;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,QAA8B,CAAC,EACnC,MAAM,EACN,OAAO,EACP,KAAK,EACL,QAAQ,EACR,OAAO,IAAI,EACZ;IACC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,CAAC;YACpB,IAAI,EAAE,GAAG,KAAK,UAAU;gBACtB;YACF;QACF;QAEA,IAAI,QAAQ;YACV,SAAS,gBAAgB,CAAC,WAAW;YACrC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QAEA,OAAO;YACL,SAAS,mBAAmB,CAAC,WAAW;YACxC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG;QAAC;QAAQ;KAAQ;IAEpB,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBACC,WAAU;oBACV,SAAS;;;;;;8BAIX,8OAAC;oBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2KACA,WAAW,CAAC,KAAK;;sCAInB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX;;;;;;8CAEH,8OAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKzB,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb;uCAEe", "debugId": null}}, {"offset": {"line": 640, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/components/UserCard.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Image from 'next/image';\nimport { useRouter } from 'next/navigation';\nimport { UserCardProps } from '@/types';\nimport { getPerformanceBadgeVariant, getPerformanceLabel, cn } from '@/lib/utils';\nimport Card from '@/components/ui/Card';\nimport Badge from '@/components/ui/Badge';\nimport Button from '@/components/ui/Button';\nimport StarRating from '@/components/ui/StarRating';\nimport Modal from '@/components/ui/Modal';\nimport {\n  EyeIcon,\n  BookmarkIcon as BookmarkSolidIcon,\n  ArrowUpIcon\n} from '@heroicons/react/24/solid';\nimport {\n  BookmarkIcon as BookmarkOutlineIcon\n} from '@heroicons/react/24/outline';\n\nconst UserCard: React.FC<UserCardProps> = ({\n  employee,\n  onView,\n  onBookmark,\n  onPromote,\n  isBookmarked\n}) => {\n  const router = useRouter();\n  const [showPromoteModal, setShowPromoteModal] = useState(false);\n\n  const handleView = () => {\n    router.push(`/employee/${employee.id}`);\n  };\n\n  const handleBookmark = () => {\n    onBookmark(employee.id);\n  };\n\n  const handlePromote = () => {\n    setShowPromoteModal(true);\n  };\n\n  const confirmPromote = () => {\n    onPromote(employee.id);\n    setShowPromoteModal(false);\n  };\n\n  return (\n    <>\n      <div className=\"glass-card p-6 rounded-3xl hover-lift group relative overflow-hidden animate-fade-in\">\n        {/* Premium Background Effects */}\n        <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500\"></div>\n        <div className=\"absolute top-0 right-0 w-32 h-32 bg-gradient-primary opacity-10 rounded-full blur-3xl group-hover:opacity-20 transition-opacity duration-500\"></div>\n\n        <div className=\"flex flex-col h-full relative z-10\">\n          {/* Bookmark Button */}\n          <button\n            onClick={handleBookmark}\n            className={cn(\n              \"absolute top-4 right-4 z-20 p-3 rounded-2xl transition-all duration-300 hover:scale-110 shadow-lg\",\n              isBookmarked\n                ? \"bg-gradient-warning text-white animate-glow\"\n                : \"glass text-gray-300 hover:bg-gradient-warning hover:text-white\"\n            )}\n          >\n            {isBookmarked ? (\n              <BookmarkSolidIcon className=\"h-5 w-5\" />\n            ) : (\n              <BookmarkOutlineIcon className=\"h-5 w-5\" />\n            )}\n          </button>\n\n          {/* Premium Profile Section */}\n          <div className=\"flex flex-col items-center mb-6\">\n            <div className=\"relative mb-4\">\n              <div className=\"w-24 h-24 rounded-3xl overflow-hidden border-4 border-white/20 shadow-2xl group-hover:scale-105 transition-transform duration-300\">\n                <Image\n                  src={employee.image}\n                  alt={`${employee.firstName} ${employee.lastName}`}\n                  fill\n                  className=\"object-cover\"\n                  sizes=\"96px\"\n                />\n              </div>\n              <div className=\"absolute -bottom-2 -right-2 w-8 h-8 bg-gradient-success rounded-full border-4 border-white/20 flex items-center justify-center animate-pulse\">\n                <div className=\"w-3 h-3 bg-white rounded-full\"></div>\n              </div>\n            </div>\n\n            <div className=\"text-center\">\n              <h3 className=\"text-xl font-bold text-premium mb-2\">\n                {employee.firstName} {employee.lastName}\n              </h3>\n              <p className=\"text-gray-400 font-medium mb-1 text-sm\">\n                {employee.email}\n              </p>\n              <div className=\"flex items-center justify-center space-x-3 mb-3\">\n                <span className=\"text-xs text-gray-400 bg-white/10 px-3 py-1 rounded-full font-medium\">\n                  Age: {employee.age}\n                </span>\n                <Badge variant=\"secondary\" size=\"sm\" className=\"bg-gradient-primary text-white border-0 font-bold\">\n                  {employee.department}\n                </Badge>\n              </div>\n            </div>\n          </div>\n\n          {/* Premium Performance Metrics */}\n          <div className=\"space-y-4 mb-6\">\n            <div className=\"glass p-4 rounded-2xl\">\n              <div className=\"flex items-center justify-between mb-3\">\n                <span className=\"text-sm font-semibold text-gray-300 uppercase tracking-wide\">\n                  Performance Rating\n                </span>\n                <Badge\n                  variant={getPerformanceBadgeVariant(employee.performanceRating)}\n                  size=\"sm\"\n                  className=\"shadow-lg font-bold\"\n                >\n                  {getPerformanceLabel(employee.performanceRating)}\n                </Badge>\n              </div>\n              <div className=\"bg-gradient-to-r from-white/5 to-white/10 p-4 rounded-xl border border-white/10\">\n                <StarRating rating={employee.performanceRating} showValue />\n              </div>\n            </div>\n          </div>\n\n          {/* Premium Action Buttons */}\n          <div className=\"flex space-x-3 mt-auto\">\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={handleView}\n              className=\"flex-1 glass border-white/20 text-gray-300 hover:text-white hover:bg-white/10 rounded-2xl py-3 font-semibold transition-all duration-300\"\n            >\n              <EyeIcon className=\"h-4 w-4 mr-2\" />\n              View Profile\n            </Button>\n\n            <Button\n              variant=\"secondary\"\n              size=\"sm\"\n              onClick={handlePromote}\n              className=\"flex-1 bg-gradient-success hover:shadow-2xl rounded-2xl py-3 font-semibold transition-all duration-300 hover:scale-105\"\n            >\n              <ArrowUpIcon className=\"h-4 w-4 mr-2\" />\n              Promote\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Promote Modal */}\n      <Modal\n        isOpen={showPromoteModal}\n        onClose={() => setShowPromoteModal(false)}\n        title=\"Promote Employee\"\n        size=\"md\"\n      >\n        <div className=\"space-y-4\">\n          <p className=\"text-gray-700 dark:text-gray-300\">\n            Are you sure you want to promote{' '}\n            <span className=\"font-semibold text-gray-900 dark:text-white\">\n              {employee.firstName} {employee.lastName}\n            </span>\n            ?\n          </p>\n\n          <div className=\"bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 p-4 rounded-xl border border-blue-200/50 dark:border-blue-700/50\">\n            <h4 className=\"font-medium text-gray-900 dark:text-white mb-3 flex items-center\">\n              <div className=\"w-2 h-2 bg-blue-500 rounded-full mr-2\"></div>\n              Current Details:\n            </h4>\n            <ul className=\"text-sm text-gray-700 dark:text-gray-300 space-y-2\">\n              <li className=\"flex items-center\">\n                <span className=\"font-medium text-blue-600 dark:text-blue-400 w-20\">Department:</span>\n                <span className=\"bg-blue-100 dark:bg-blue-900/50 px-2 py-1 rounded-lg text-blue-800 dark:text-blue-200\">{employee.department}</span>\n              </li>\n              <li className=\"flex items-center\">\n                <span className=\"font-medium text-purple-600 dark:text-purple-400 w-20\">Rating:</span>\n                <span className=\"bg-purple-100 dark:bg-purple-900/50 px-2 py-1 rounded-lg text-purple-800 dark:text-purple-200\">{employee.performanceRating}/5</span>\n              </li>\n              <li className=\"flex items-center\">\n                <span className=\"font-medium text-green-600 dark:text-green-400 w-20\">Email:</span>\n                <span className=\"bg-green-100 dark:bg-green-900/50 px-2 py-1 rounded-lg text-green-800 dark:text-green-200 text-xs\">{employee.email}</span>\n              </li>\n            </ul>\n          </div>\n          \n          <div className=\"flex space-x-3 pt-6 border-t border-gray-200/50 dark:border-gray-700/50\">\n            <Button\n              variant=\"outline\"\n              onClick={() => setShowPromoteModal(false)}\n              className=\"flex-1 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800\"\n            >\n              Cancel\n            </Button>\n            <Button\n              variant=\"primary\"\n              onClick={confirmPromote}\n              className=\"flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold\"\n            >\n              Confirm Promotion\n            </Button>\n          </div>\n        </div>\n      </Modal>\n    </>\n  );\n};\n\nexport default UserCard;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAKA;AAjBA;;;;;;;;;;;;AAqBA,MAAM,WAAoC,CAAC,EACzC,QAAQ,EACR,MAAM,EACN,UAAU,EACV,SAAS,EACT,YAAY,EACb;IACC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,aAAa;QACjB,OAAO,IAAI,CAAC,CAAC,UAAU,EAAE,SAAS,EAAE,EAAE;IACxC;IAEA,MAAM,iBAAiB;QACrB,WAAW,SAAS,EAAE;IACxB;IAEA,MAAM,gBAAgB;QACpB,oBAAoB;IACtB;IAEA,MAAM,iBAAiB;QACrB,UAAU,SAAS,EAAE;QACrB,oBAAoB;IACtB;IAEA,qBACE;;0BACE,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCAEf,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCACC,SAAS;gCACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qGACA,eACI,gDACA;0CAGL,6BACC,8OAAC,qNAAA,CAAA,eAAiB;oCAAC,WAAU;;;;;6FAE7B,8OAAC,uNAAA,CAAA,eAAmB;oCAAC,WAAU;;;;;;;;;;;0CAKnC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAK,SAAS,KAAK;oDACnB,KAAK,GAAG,SAAS,SAAS,CAAC,CAAC,EAAE,SAAS,QAAQ,EAAE;oDACjD,IAAI;oDACJ,WAAU;oDACV,OAAM;;;;;;;;;;;0DAGV,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;kDAInB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;oDACX,SAAS,SAAS;oDAAC;oDAAE,SAAS,QAAQ;;;;;;;0DAEzC,8OAAC;gDAAE,WAAU;0DACV,SAAS,KAAK;;;;;;0DAEjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;;4DAAuE;4DAC/E,SAAS,GAAG;;;;;;;kEAEpB,8OAAC,iIAAA,CAAA,UAAK;wDAAC,SAAQ;wDAAY,MAAK;wDAAK,WAAU;kEAC5C,SAAS,UAAU;;;;;;;;;;;;;;;;;;;;;;;;0CAO5B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAA8D;;;;;;8DAG9E,8OAAC,iIAAA,CAAA,UAAK;oDACJ,SAAS,CAAA,GAAA,mHAAA,CAAA,6BAA0B,AAAD,EAAE,SAAS,iBAAiB;oDAC9D,MAAK;oDACL,WAAU;8DAET,CAAA,GAAA,mHAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS,iBAAiB;;;;;;;;;;;;sDAGnD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,sIAAA,CAAA,UAAU;gDAAC,QAAQ,SAAS,iBAAiB;gDAAE,SAAS;;;;;;;;;;;;;;;;;;;;;;0CAM/D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,UAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,8OAAC,2MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAItC,8OAAC,kIAAA,CAAA,UAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,8OAAC,mNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhD,8OAAC,iIAAA,CAAA,UAAK;gBACJ,QAAQ;gBACR,SAAS,IAAM,oBAAoB;gBACnC,OAAM;gBACN,MAAK;0BAEL,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;gCAAmC;gCACb;8CACjC,8OAAC;oCAAK,WAAU;;wCACb,SAAS,SAAS;wCAAC;wCAAE,SAAS,QAAQ;;;;;;;gCAClC;;;;;;;sCAIT,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAI,WAAU;;;;;;wCAA8C;;;;;;;8CAG/D,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAoD;;;;;;8DACpE,8OAAC;oDAAK,WAAU;8DAAyF,SAAS,UAAU;;;;;;;;;;;;sDAE9H,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAwD;;;;;;8DACxE,8OAAC;oDAAK,WAAU;;wDAAiG,SAAS,iBAAiB;wDAAC;;;;;;;;;;;;;sDAE9I,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAsD;;;;;;8DACtE,8OAAC;oDAAK,WAAU;8DAAqG,SAAS,KAAK;;;;;;;;;;;;;;;;;;;;;;;;sCAKzI,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,UAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,oBAAoB;oCACnC,WAAU;8CACX;;;;;;8CAGD,8OAAC,kIAAA,CAAA,UAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;uCAEe", "debugId": null}}, {"offset": {"line": 1148, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface CardProps {\n  children: React.ReactNode;\n  className?: string;\n  padding?: 'none' | 'sm' | 'md' | 'lg';\n  hover?: boolean;\n}\n\nconst Card: React.FC<CardProps> = ({\n  children,\n  className,\n  padding = 'md',\n  hover = false,\n  ...props\n}) => {\n  const baseClasses = 'glass backdrop-blur-xl bg-white/80 dark:bg-slate-900/80 border border-white/20 dark:border-slate-700/50 rounded-2xl shadow-xl';\n\n  const paddingClasses = {\n    none: '',\n    sm: 'p-4',\n    md: 'p-6',\n    lg: 'p-8'\n  };\n\n  const hoverClasses = hover ? 'hover:shadow-2xl hover:transform hover:scale-105 transition-all duration-300' : '';\n\n  return (\n    <div\n      className={cn(\n        baseClasses,\n        paddingClasses[padding],\n        hoverClasses,\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n};\n\nexport default Card;\n"], "names": [], "mappings": ";;;;AACA;;;AASA,MAAM,OAA4B,CAAC,EACjC,QAAQ,EACR,SAAS,EACT,UAAU,IAAI,EACd,QAAQ,KAAK,EACb,GAAG,OACJ;IACC,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,MAAM;QACN,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,eAAe,QAAQ,iFAAiF;IAE9G,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,cAAc,CAAC,QAAQ,EACvB,cACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 1179, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/components/SearchAndFilters.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { MagnifyingGlassIcon, FunnelIcon, XMarkIcon } from '@heroicons/react/24/outline';\nimport { SearchFilters } from '@/types';\nimport Button from '@/components/ui/Button';\nimport Card from '@/components/ui/Card';\nimport { Listbox, Transition } from '@headlessui/react';\nimport { ChevronUpDownIcon, CheckIcon } from '@heroicons/react/20/solid';\n\ninterface SearchAndFiltersProps {\n  filters: SearchFilters;\n  onQueryChange: (query: string) => void;\n  onDepartmentsChange: (departments: string[]) => void;\n  onRatingRangeChange: (minRating: number, maxRating: number) => void;\n  onClearFilters: () => void;\n  availableDepartments: string[];\n}\n\nconst SearchAndFilters: React.FC<SearchAndFiltersProps> = ({\n  filters,\n  onQueryChange,\n  onDepartmentsChange,\n  onRatingRangeChange,\n  onClearFilters,\n  availableDepartments\n}) => {\n  const [showFilters, setShowFilters] = useState(false);\n\n  const hasActiveFilters = filters.departments.length > 0 || filters.minRating > 0 || filters.maxRating < 5;\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Premium Search Bar */}\n      <div className=\"glass-card p-6 rounded-3xl animate-slide-up\">\n        <div className=\"flex items-center space-x-6\">\n          <div className=\"flex-1 relative\">\n            <div className=\"absolute left-6 top-1/2 transform -translate-y-1/2 w-6 h-6 bg-gradient-primary rounded-full flex items-center justify-center\">\n              <MagnifyingGlassIcon className=\"h-4 w-4 text-white\" />\n            </div>\n            <input\n              type=\"text\"\n              placeholder=\"Search elite professionals by name, email, or department...\"\n              value={filters.query}\n              onChange={(e) => onQueryChange(e.target.value)}\n              className=\"w-full pl-16 pr-6 py-4 border-0 rounded-2xl glass text-white placeholder-gray-400 focus:ring-2 focus:ring-purple-500/50 focus:bg-white/10 transition-all duration-300 text-lg font-medium shadow-inner backdrop-blur-xl\"\n            />\n          </div>\n\n          <Button\n            variant=\"outline\"\n            onClick={() => setShowFilters(!showFilters)}\n            className={`relative px-6 py-3 rounded-2xl transition-all duration-300 hover-lift font-semibold ${\n              hasActiveFilters\n                ? 'bg-gradient-primary text-white shadow-2xl animate-glow'\n                : 'glass border-white/20 text-gray-300 hover:text-white hover:bg-white/10'\n            }`}\n          >\n            <div className={`p-2 rounded-xl mr-3 ${hasActiveFilters ? 'bg-white/20' : 'bg-white/10'}`}>\n              <FunnelIcon className=\"h-5 w-5\" />\n            </div>\n            <span className=\"text-base\">\n              {hasActiveFilters ? `Active Filters (${filters.departments.length + (filters.minRating > 0 || filters.maxRating < 5 ? 1 : 0)})` : 'Advanced Filters'}\n            </span>\n            {hasActiveFilters && (\n              <div className=\"absolute -top-2 -right-2 w-6 h-6 bg-gradient-warning rounded-full animate-pulse flex items-center justify-center\">\n                <span className=\"text-xs font-bold text-white\">{filters.departments.length + (filters.minRating > 0 || filters.maxRating < 5 ? 1 : 0)}</span>\n              </div>\n            )}\n          </Button>\n\n          {hasActiveFilters && (\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={onClearFilters}\n              className=\"glass border-red-500/30 text-red-400 hover:text-red-300 hover:bg-red-500/10 rounded-2xl px-4 py-3 font-semibold transition-all duration-300\"\n            >\n              <XMarkIcon className=\"h-4 w-4 mr-2\" />\n              Reset All\n            </Button>\n          )}\n        </div>\n      </div>\n\n      {/* Filters Panel */}\n      <Transition\n        show={showFilters}\n        enter=\"transition ease-out duration-200\"\n        enterFrom=\"opacity-0 scale-95\"\n        enterTo=\"opacity-100 scale-100\"\n        leave=\"transition ease-in duration-150\"\n        leaveFrom=\"opacity-100 scale-100\"\n        leaveTo=\"opacity-0 scale-95\"\n      >\n        <Card className=\"relative z-[100]\">\n          <div className=\"space-y-6\">\n            <div className=\"flex items-center justify-between\">\n              <h3 className=\"text-lg font-medium text-secondary-900 dark:text-white\">\n                Filters\n              </h3>\n              {hasActiveFilters && (\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={onClearFilters}\n                >\n                  <XMarkIcon className=\"h-4 w-4 mr-1\" />\n                  Clear All\n                </Button>\n              )}\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {/* Department Filter */}\n              <div>\n                <label className=\"block text-sm font-semibold text-secondary-800 dark:text-white mb-3\">\n                  Departments\n                </label>\n                <Listbox value={filters.departments} onChange={onDepartmentsChange} multiple>\n                  <div className=\"relative z-[200]\">\n                    <Listbox.Button className=\"relative w-full cursor-default rounded-lg bg-white dark:bg-gray-800 py-3 pl-4 pr-10 text-left border-2 border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\">\n                      <span className=\"block truncate text-gray-900 dark:text-gray-100 font-semibold\">\n                        {filters.departments.length === 0\n                          ? 'All Departments'\n                          : `${filters.departments.length} selected`}\n                      </span>\n                      <span className=\"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3\">\n                        <ChevronUpDownIcon className=\"h-5 w-5 text-gray-600 dark:text-gray-300\" />\n                      </span>\n                    </Listbox.Button>\n                    <Transition\n                      leave=\"transition ease-in duration-100\"\n                      leaveFrom=\"opacity-100\"\n                      leaveTo=\"opacity-0\"\n                    >\n                      <Listbox.Options className=\"absolute z-[9999] mt-2 max-h-60 w-full overflow-auto rounded-lg bg-white dark:bg-gray-900 py-2 shadow-2xl ring-1 ring-black ring-opacity-10 focus:outline-none border-2 border-gray-300 dark:border-gray-600\">\n                        {availableDepartments.map((department) => (\n                          <Listbox.Option\n                            key={department}\n                            value={department}\n                            className={({ active }) =>\n                              `relative cursor-pointer select-none py-3 pl-10 pr-4 transition-colors ${\n                                active\n                                  ? 'bg-blue-100 text-blue-900 dark:bg-blue-800 dark:text-blue-100'\n                                  : 'text-gray-900 dark:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-800'\n                              }`\n                            }\n                          >\n                            {({ selected }) => (\n                              <>\n                                <span className={`block truncate ${selected ? 'font-bold text-blue-600 dark:text-blue-300' : 'font-medium text-gray-900 dark:text-gray-100'}`}>\n                                  {department}\n                                </span>\n                                {selected && (\n                                  <span className=\"absolute inset-y-0 left-0 flex items-center pl-3 text-blue-600 dark:text-blue-300\">\n                                    <CheckIcon className=\"h-5 w-5\" />\n                                  </span>\n                                )}\n                              </>\n                            )}\n                          </Listbox.Option>\n                        ))}\n                      </Listbox.Options>\n                    </Transition>\n                  </div>\n                </Listbox>\n              </div>\n\n              {/* Rating Range Filter */}\n              <div>\n                <label className=\"block text-sm font-semibold text-secondary-800 dark:text-white mb-3\">\n                  Performance Rating Range\n                </label>\n                <div className=\"space-y-3\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2\">\n                      Minimum Rating: <span className=\"font-bold text-blue-600 dark:text-blue-400\">{filters.minRating}</span>\n                    </label>\n                    <input\n                      type=\"range\"\n                      min=\"0\"\n                      max=\"5\"\n                      step=\"1\"\n                      value={filters.minRating}\n                      onChange={(e) => onRatingRangeChange(Number(e.target.value), filters.maxRating)}\n                      className=\"w-full h-3 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer slider\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2\">\n                      Maximum Rating: <span className=\"font-bold text-blue-600 dark:text-blue-400\">{filters.maxRating}</span>\n                    </label>\n                    <input\n                      type=\"range\"\n                      min=\"0\"\n                      max=\"5\"\n                      step=\"1\"\n                      value={filters.maxRating}\n                      onChange={(e) => onRatingRangeChange(filters.minRating, Number(e.target.value))}\n                      className=\"w-full h-3 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer slider\"\n                    />\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </Card>\n      </Transition>\n\n      {/* Active Filters Display */}\n      {hasActiveFilters && (\n        <div className=\"flex flex-wrap gap-2\">\n          {filters.departments.map((dept) => (\n            <span\n              key={dept}\n              className=\"inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200\"\n            >\n              {dept}\n              <button\n                onClick={() => onDepartmentsChange(filters.departments.filter(d => d !== dept))}\n                className=\"ml-2 hover:text-primary-600 dark:hover:text-primary-400\"\n              >\n                <XMarkIcon className=\"h-4 w-4\" />\n              </button>\n            </span>\n          ))}\n          \n          {(filters.minRating > 0 || filters.maxRating < 5) && (\n            <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm bg-secondary-100 text-secondary-800 dark:bg-secondary-800 dark:text-secondary-200\">\n              Rating: {filters.minRating}-{filters.maxRating}\n              <button\n                onClick={() => onRatingRangeChange(0, 5)}\n                className=\"ml-2 hover:text-secondary-600 dark:hover:text-secondary-400\"\n              >\n                <XMarkIcon className=\"h-4 w-4\" />\n              </button>\n            </span>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default SearchAndFilters;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AAAA;AACA;AAAA;AARA;;;;;;;;AAmBA,MAAM,mBAAoD,CAAC,EACzD,OAAO,EACP,aAAa,EACb,mBAAmB,EACnB,mBAAmB,EACnB,cAAc,EACd,oBAAoB,EACrB;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,mBAAmB,QAAQ,WAAW,CAAC,MAAM,GAAG,KAAK,QAAQ,SAAS,GAAG,KAAK,QAAQ,SAAS,GAAG;IAExG,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,qOAAA,CAAA,sBAAmB;wCAAC,WAAU;;;;;;;;;;;8CAEjC,8OAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO,QAAQ,KAAK;oCACpB,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,WAAU;;;;;;;;;;;;sCAId,8OAAC,kIAAA,CAAA,UAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,eAAe,CAAC;4BAC/B,WAAW,CAAC,oFAAoF,EAC9F,mBACI,2DACA,0EACJ;;8CAEF,8OAAC;oCAAI,WAAW,CAAC,oBAAoB,EAAE,mBAAmB,gBAAgB,eAAe;8CACvF,cAAA,8OAAC,mNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;8CAExB,8OAAC;oCAAK,WAAU;8CACb,mBAAmB,CAAC,gBAAgB,EAAE,QAAQ,WAAW,CAAC,MAAM,GAAG,CAAC,QAAQ,SAAS,GAAG,KAAK,QAAQ,SAAS,GAAG,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG;;;;;;gCAEnI,kCACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAAgC,QAAQ,WAAW,CAAC,MAAM,GAAG,CAAC,QAAQ,SAAS,GAAG,KAAK,QAAQ,SAAS,GAAG,IAAI,IAAI,CAAC;;;;;;;;;;;;;;;;;wBAKzI,kCACC,8OAAC,kIAAA,CAAA,UAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;;8CAEV,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;0BAQ9C,8OAAC,uLAAA,CAAA,aAAU;gBACT,MAAM;gBACN,OAAM;gBACN,WAAU;gBACV,SAAQ;gBACR,OAAM;gBACN,WAAU;gBACV,SAAQ;0BAER,cAAA,8OAAC,gIAAA,CAAA,UAAI;oBAAC,WAAU;8BACd,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyD;;;;;;oCAGtE,kCACC,8OAAC,kIAAA,CAAA,UAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;;0DAET,8OAAC,iNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;0CAM5C,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAsE;;;;;;0DAGvF,8OAAC,iLAAA,CAAA,UAAO;gDAAC,OAAO,QAAQ,WAAW;gDAAE,UAAU;gDAAqB,QAAQ;0DAC1E,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iLAAA,CAAA,UAAO,CAAC,MAAM;4DAAC,WAAU;;8EACxB,8OAAC;oEAAK,WAAU;8EACb,QAAQ,WAAW,CAAC,MAAM,KAAK,IAC5B,oBACA,GAAG,QAAQ,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC;;;;;;8EAE9C,8OAAC;oEAAK,WAAU;8EACd,cAAA,8OAAC,+NAAA,CAAA,oBAAiB;wEAAC,WAAU;;;;;;;;;;;;;;;;;sEAGjC,8OAAC,uLAAA,CAAA,aAAU;4DACT,OAAM;4DACN,WAAU;4DACV,SAAQ;sEAER,cAAA,8OAAC,iLAAA,CAAA,UAAO,CAAC,OAAO;gEAAC,WAAU;0EACxB,qBAAqB,GAAG,CAAC,CAAC,2BACzB,8OAAC,iLAAA,CAAA,UAAO,CAAC,MAAM;wEAEb,OAAO;wEACP,WAAW,CAAC,EAAE,MAAM,EAAE,GACpB,CAAC,sEAAsE,EACrE,SACI,kEACA,6EACJ;kFAGH,CAAC,EAAE,QAAQ,EAAE,iBACZ;;kGACE,8OAAC;wFAAK,WAAW,CAAC,eAAe,EAAE,WAAW,+CAA+C,gDAAgD;kGAC1I;;;;;;oFAEF,0BACC,8OAAC;wFAAK,WAAU;kGACd,cAAA,8OAAC,+MAAA,CAAA,YAAS;4FAAC,WAAU;;;;;;;;;;;;;uEAjBxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDA+BnB,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAsE;;;;;;0DAGvF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;;oEAAkE;kFACjE,8OAAC;wEAAK,WAAU;kFAA8C,QAAQ,SAAS;;;;;;;;;;;;0EAEjG,8OAAC;gEACC,MAAK;gEACL,KAAI;gEACJ,KAAI;gEACJ,MAAK;gEACL,OAAO,QAAQ,SAAS;gEACxB,UAAU,CAAC,IAAM,oBAAoB,OAAO,EAAE,MAAM,CAAC,KAAK,GAAG,QAAQ,SAAS;gEAC9E,WAAU;;;;;;;;;;;;kEAGd,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;;oEAAkE;kFACjE,8OAAC;wEAAK,WAAU;kFAA8C,QAAQ,SAAS;;;;;;;;;;;;0EAEjG,8OAAC;gEACC,MAAK;gEACL,KAAI;gEACJ,KAAI;gEACJ,MAAK;gEACL,OAAO,QAAQ,SAAS;gEACxB,UAAU,CAAC,IAAM,oBAAoB,QAAQ,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gEAC7E,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAWzB,kCACC,8OAAC;gBAAI,WAAU;;oBACZ,QAAQ,WAAW,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;4BAEC,WAAU;;gCAET;8CACD,8OAAC;oCACC,SAAS,IAAM,oBAAoB,QAAQ,WAAW,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM;oCACzE,WAAU;8CAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;2BARlB;;;;;oBAaR,CAAC,QAAQ,SAAS,GAAG,KAAK,QAAQ,SAAS,GAAG,CAAC,mBAC9C,8OAAC;wBAAK,WAAU;;4BAA4I;4BACjJ,QAAQ,SAAS;4BAAC;4BAAE,QAAQ,SAAS;0CAC9C,8OAAC;gCACC,SAAS,IAAM,oBAAoB,GAAG;gCACtC,WAAU;0CAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrC;uCAEe", "debugId": null}}, {"offset": {"line": 1691, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/components/ui/SkeletonCard.tsx"], "sourcesContent": ["import React from 'react';\nimport Card from './Card';\n\nconst SkeletonCard: React.FC = () => {\n  return (\n    <Card className=\"animate-pulse\">\n      <div className=\"flex items-start space-x-4 mb-6\">\n        {/* Avatar skeleton */}\n        <div className=\"h-16 w-16 bg-gradient-to-br from-slate-200 to-slate-300 dark:from-slate-700 dark:to-slate-600 rounded-2xl\"></div>\n        \n        {/* Content skeleton */}\n        <div className=\"flex-1 space-y-2\">\n          <div className=\"h-5 bg-gradient-to-r from-slate-200 to-slate-300 dark:from-slate-700 dark:to-slate-600 rounded-lg w-3/4\"></div>\n          <div className=\"h-4 bg-gradient-to-r from-slate-200 to-slate-300 dark:from-slate-700 dark:to-slate-600 rounded-lg w-1/2\"></div>\n          <div className=\"flex space-x-2\">\n            <div className=\"h-6 bg-gradient-to-r from-slate-200 to-slate-300 dark:from-slate-700 dark:to-slate-600 rounded-full w-16\"></div>\n            <div className=\"h-6 bg-gradient-to-r from-slate-200 to-slate-300 dark:from-slate-700 dark:to-slate-600 rounded-full w-20\"></div>\n          </div>\n        </div>\n      </div>\n      \n      {/* Performance section skeleton */}\n      <div className=\"mb-6\">\n        <div className=\"flex items-center justify-between mb-3\">\n          <div className=\"h-4 bg-gradient-to-r from-slate-200 to-slate-300 dark:from-slate-700 dark:to-slate-600 rounded w-20\"></div>\n          <div className=\"h-6 bg-gradient-to-r from-slate-200 to-slate-300 dark:from-slate-700 dark:to-slate-600 rounded-full w-16\"></div>\n        </div>\n        <div className=\"bg-gradient-to-r from-slate-100 to-slate-200 dark:from-slate-800 dark:to-slate-700 p-3 rounded-xl\">\n          <div className=\"flex space-x-1\">\n            {Array.from({ length: 5 }).map((_, i) => (\n              <div key={i} className=\"h-5 w-5 bg-gradient-to-r from-slate-200 to-slate-300 dark:from-slate-700 dark:to-slate-600 rounded\"></div>\n            ))}\n          </div>\n        </div>\n      </div>\n      \n      {/* Buttons skeleton */}\n      <div className=\"flex space-x-2\">\n        <div className=\"flex-1 h-10 bg-gradient-to-r from-slate-200 to-slate-300 dark:from-slate-700 dark:to-slate-600 rounded-xl\"></div>\n        <div className=\"h-10 w-10 bg-gradient-to-r from-slate-200 to-slate-300 dark:from-slate-700 dark:to-slate-600 rounded-xl\"></div>\n        <div className=\"h-10 w-20 bg-gradient-to-r from-slate-200 to-slate-300 dark:from-slate-700 dark:to-slate-600 rounded-xl\"></div>\n      </div>\n    </Card>\n  );\n};\n\nexport default SkeletonCard;\n"], "names": [], "mappings": ";;;;AACA;;;AAEA,MAAM,eAAyB;IAC7B,qBACE,8OAAC,gIAAA,CAAA,UAAI;QAAC,WAAU;;0BACd,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;0BAMrB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAEjB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,MAAM,IAAI,CAAC;gCAAE,QAAQ;4BAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,8OAAC;oCAAY,WAAU;mCAAb;;;;;;;;;;;;;;;;;;;;;0BAOlB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;AAIvB;uCAEe", "debugId": null}}, {"offset": {"line": 1861, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Flam/Dummy/hr-dashboard/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Employee, User } from '@/types';\nimport { transformUserToEmployee } from '@/lib/utils';\nimport { useSearch } from '@/hooks/useSearch';\nimport { useBookmarks } from '@/hooks/useBookmarks';\nimport UserCard from '@/components/UserCard';\nimport SearchAndFilters from '@/components/SearchAndFilters';\nimport Card from '@/components/ui/Card';\nimport SkeletonCard from '@/components/ui/SkeletonCard';\n\nexport default function Dashboard() {\n  const [employees, setEmployees] = useState<Employee[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  const { bookmarks, addBookmark, removeBookmark, isBookmarked } = useBookmarks();\n  const {\n    filters,\n    filteredEmployees,\n    updateQuery,\n    updateDepartments,\n    updateRatingRange,\n    clearFilters,\n    availableDepartments\n  } = useSearch(employees);\n\n  useEffect(() => {\n    const fetchEmployees = async () => {\n      try {\n        setLoading(true);\n        const response = await fetch('https://dummyjson.com/users?limit=20');\n\n        if (!response.ok) {\n          throw new Error('Failed to fetch employees');\n        }\n\n        const data = await response.json();\n        const transformedEmployees = data.users.map((user: User) =>\n          transformUserToEmployee(user)\n        );\n\n        setEmployees(transformedEmployees);\n      } catch (err) {\n        setError(err instanceof Error ? err.message : 'An error occurred');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchEmployees();\n  }, []);\n\n  const handleBookmark = (id: number) => {\n    if (isBookmarked(id)) {\n      removeBookmark(id);\n    } else {\n      addBookmark(id);\n    }\n  };\n\n  const handlePromote = (id: number) => {\n    // In a real app, this would make an API call\n    console.log(`Promoting employee with ID: ${id}`);\n    // You could show a success toast here\n  };\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        {/* Header skeleton */}\n        <div className=\"text-center mb-8 animate-pulse\">\n          <div className=\"h-10 bg-gradient-to-r from-slate-200 to-slate-300 dark:from-slate-700 dark:to-slate-600 rounded-lg w-80 mx-auto mb-4\"></div>\n          <div className=\"h-6 bg-gradient-to-r from-slate-200 to-slate-300 dark:from-slate-700 dark:to-slate-600 rounded-lg w-96 mx-auto\"></div>\n        </div>\n\n        {/* Search skeleton */}\n        <Card>\n          <div className=\"animate-pulse\">\n            <div className=\"h-12 bg-gradient-to-r from-slate-200 to-slate-300 dark:from-slate-700 dark:to-slate-600 rounded-xl\"></div>\n          </div>\n        </Card>\n\n        {/* Cards skeleton */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n          {Array.from({ length: 8 }).map((_, index) => (\n            <SkeletonCard key={index} />\n          ))}\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <Card>\n        <div className=\"text-center py-12\">\n          <div className=\"text-red-500 text-lg font-medium mb-2\">Error Loading Employees</div>\n          <p className=\"text-secondary-600 dark:text-secondary-400 mb-4\">{error}</p>\n          <button\n            onClick={() => window.location.reload()}\n            className=\"px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\"\n          >\n            Try Again\n          </button>\n        </div>\n      </Card>\n    );\n  }\n\n  return (\n    <div className=\"space-y-8 p-8 min-h-screen\">\n      {/* Header */}\n      <div className=\"text-center mb-12 animate-fade-in\">\n        <div className=\"flex items-center justify-center space-x-4 mb-6\">\n          <div className=\"w-16 h-16 bg-gradient-primary rounded-3xl flex items-center justify-center shadow-2xl animate-glow\">\n            <span className=\"text-white font-bold text-2xl\">HR</span>\n          </div>\n          <div className=\"text-left\">\n            <h1 className=\"text-6xl font-bold text-premium mb-2\">\n              Elite Dashboard\n            </h1>\n            <div className=\"h-1 w-24 bg-gradient-primary rounded-full\"></div>\n          </div>\n        </div>\n        <p className=\"text-xl text-gray-400 font-medium max-w-3xl mx-auto leading-relaxed\">\n          Experience premium HR management with our cutting-edge analytics platform designed for excellence\n        </p>\n      </div>\n\n      {/* Search and Filters */}\n      <SearchAndFilters\n        filters={filters}\n        onQueryChange={updateQuery}\n        onDepartmentsChange={updateDepartments}\n        onRatingRangeChange={updateRatingRange}\n        onClearFilters={clearFilters}\n        availableDepartments={availableDepartments}\n      />\n\n      {/* Premium Stats Summary */}\n      <div className=\"glass-card p-6 rounded-3xl animate-slide-up\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-6\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-4 h-4 bg-gradient-primary rounded-full animate-pulse\"></div>\n              <p className=\"text-lg font-semibold text-white\">\n                Showing {filteredEmployees.length} of {employees.length} elite professionals\n              </p>\n            </div>\n\n            {filteredEmployees.length > 0 && (\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-4 h-4 bg-gradient-warning rounded-full animate-pulse\"></div>\n                <div className=\"text-lg font-semibold text-gradient-accent\">\n                  {bookmarks.length} starred talents\n                </div>\n              </div>\n            )}\n          </div>\n\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-3 h-3 bg-green-400 rounded-full animate-pulse\"></div>\n            <span className=\"text-sm font-bold text-green-400 bg-green-400/10 px-4 py-2 rounded-full\">\n              Live Analytics\n            </span>\n          </div>\n        </div>\n      </div>\n\n      {/* Elite Team Grid */}\n      {filteredEmployees.length === 0 ? (\n        <div className=\"glass-card p-12 rounded-3xl text-center animate-fade-in\">\n          <div className=\"w-24 h-24 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-6 animate-glow\">\n            <span className=\"text-white text-3xl\">🔍</span>\n          </div>\n          <div className=\"text-white text-2xl font-bold mb-4\">No Elite Professionals Found</div>\n          <p className=\"text-gray-400 text-lg mb-8 max-w-md mx-auto\">\n            Refine your search criteria to discover exceptional talent in our premium database\n          </p>\n          <button\n            onClick={clearFilters}\n            className=\"px-8 py-4 bg-gradient-primary text-white rounded-2xl hover-lift font-semibold text-lg shadow-2xl\"\n          >\n            Reset Search\n          </button>\n        </div>\n      ) : (\n        <div>\n          <div className=\"flex items-center space-x-3 mb-8\">\n            <h2 className=\"text-3xl font-bold text-gradient-primary\">Elite Professionals</h2>\n            <div className=\"flex-1 h-px bg-gradient-to-r from-purple-500/50 to-transparent\"></div>\n          </div>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\">\n            {filteredEmployees.map((employee, index) => (\n              <div\n                key={employee.id}\n                className=\"animate-scale-in\"\n                style={{ animationDelay: `${index * 100}ms` }}\n              >\n                <UserCard\n                  employee={employee}\n                  onView={(id) => console.log(`Viewing employee ${id}`)}\n                  onBookmark={handleBookmark}\n                  onPromote={handlePromote}\n                  isBookmarked={isBookmarked(employee.id)}\n                />\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;AAYe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,cAAc,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAC5E,MAAM,EACJ,OAAO,EACP,iBAAiB,EACjB,WAAW,EACX,iBAAiB,EACjB,iBAAiB,EACjB,YAAY,EACZ,oBAAoB,EACrB,GAAG,CAAA,GAAA,yHAAA,CAAA,YAAS,AAAD,EAAE;IAEd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB;YACrB,IAAI;gBACF,WAAW;gBACX,MAAM,WAAW,MAAM,MAAM;gBAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,uBAAuB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,OAC3C,CAAA,GAAA,mHAAA,CAAA,0BAAuB,AAAD,EAAE;gBAG1B,aAAa;YACf,EAAE,OAAO,KAAK;gBACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAChD,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAC;QACtB,IAAI,aAAa,KAAK;YACpB,eAAe;QACjB,OAAO;YACL,YAAY;QACd;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,6CAA6C;QAC7C,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,IAAI;IAC/C,sCAAsC;IACxC;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;;;;;;;8BAIjB,8OAAC,gIAAA,CAAA,UAAI;8BACH,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;8BAKnB,8OAAC;oBAAI,WAAU;8BACZ,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC,wIAAA,CAAA,UAAY,MAAM;;;;;;;;;;;;;;;;IAK7B;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC,gIAAA,CAAA,UAAI;sBACH,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAwC;;;;;;kCACvD,8OAAC;wBAAE,WAAU;kCAAmD;;;;;;kCAChE,8OAAC;wBACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;wBACrC,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAgC;;;;;;;;;;;0CAElD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAuC;;;;;;kDAGrD,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;kCAGnB,8OAAC;wBAAE,WAAU;kCAAsE;;;;;;;;;;;;0BAMrF,8OAAC,sIAAA,CAAA,UAAgB;gBACf,SAAS;gBACT,eAAe;gBACf,qBAAqB;gBACrB,qBAAqB;gBACrB,gBAAgB;gBAChB,sBAAsB;;;;;;0BAIxB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAE,WAAU;;gDAAmC;gDACrC,kBAAkB,MAAM;gDAAC;gDAAK,UAAU,MAAM;gDAAC;;;;;;;;;;;;;gCAI3D,kBAAkB,MAAM,GAAG,mBAC1B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;gDACZ,UAAU,MAAM;gDAAC;;;;;;;;;;;;;;;;;;;sCAM1B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAK,WAAU;8CAA0E;;;;;;;;;;;;;;;;;;;;;;;YAQ/F,kBAAkB,MAAM,KAAK,kBAC5B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;sCAAsB;;;;;;;;;;;kCAExC,8OAAC;wBAAI,WAAU;kCAAqC;;;;;;kCACpD,8OAAC;wBAAE,WAAU;kCAA8C;;;;;;kCAG3D,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;qCAKH,8OAAC;;kCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAEjB,8OAAC;wBAAI,WAAU;kCACZ,kBAAkB,GAAG,CAAC,CAAC,UAAU,sBAChC,8OAAC;gCAEC,WAAU;gCACV,OAAO;oCAAE,gBAAgB,GAAG,QAAQ,IAAI,EAAE,CAAC;gCAAC;0CAE5C,cAAA,8OAAC,8HAAA,CAAA,UAAQ;oCACP,UAAU;oCACV,QAAQ,CAAC,KAAO,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,IAAI;oCACpD,YAAY;oCACZ,WAAW;oCACX,cAAc,aAAa,SAAS,EAAE;;;;;;+BATnC,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;AAkBhC", "debugId": null}}]}