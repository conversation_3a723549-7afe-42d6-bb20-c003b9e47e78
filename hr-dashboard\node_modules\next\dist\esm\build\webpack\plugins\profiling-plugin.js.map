{"version": 3, "sources": ["../../../../src/build/webpack/plugins/profiling-plugin.ts"], "sourcesContent": ["import { NormalModule } from 'next/dist/compiled/webpack/webpack'\nimport type { Span } from '../../../trace'\nimport type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport path from 'path'\n\nconst pluginName = 'ProfilingPlugin'\nexport const spans = new WeakMap<webpack.Compilation | webpack.Compiler, Span>()\nconst moduleSpansByCompilation = new WeakMap<\n  webpack.Compilation,\n  WeakMap<webpack.Module, Span>\n>()\nconst makeSpanByCompilation = new WeakMap<webpack.Compilation, Span>()\nconst sealSpanByCompilation = new WeakMap<webpack.Compilation, Span>()\nexport const webpackInvalidSpans = new WeakMap<any, Span>()\n\nconst TRACE_LABELS_SEAL = [\n  'module assets',\n  'create chunk assets',\n  'asset render',\n  'asset emit',\n  'store asset',\n]\n\nfunction inTraceLabelsSeal(label: string) {\n  return TRACE_LABELS_SEAL.some((l) => label.startsWith(l))\n}\n\nexport class ProfilingPlugin {\n  compiler: any\n  runWebpackSpan: Span\n  rootDir: string\n\n  constructor({\n    runWebpackSpan,\n    rootDir,\n  }: {\n    runWebpackSpan: Span\n    rootDir: string\n  }) {\n    this.runWebpackSpan = runWebpackSpan\n    this.rootDir = rootDir\n  }\n  apply(compiler: any) {\n    this.traceTopLevelHooks(compiler)\n    this.traceCompilationHooks(compiler)\n    this.compiler = compiler\n  }\n\n  traceHookPair(\n    spanName: string | (() => string),\n    startHook: any,\n    stopHook: any,\n    {\n      parentSpan,\n      attrs,\n      onStart,\n      onStop,\n    }: {\n      parentSpan?: (...params: any[]) => Span\n      attrs?: any\n      onStart?: (span: Span, ...params: any[]) => void\n      onStop?: (span: Span, ...params: any[]) => void\n    } = {}\n  ) {\n    let span: Span | undefined\n    startHook.tap(\n      { name: pluginName, stage: -Infinity },\n      (...params: any[]) => {\n        const name = typeof spanName === 'function' ? spanName() : spanName\n        const attributes = attrs ? attrs(...params) : attrs\n        span = parentSpan\n          ? parentSpan(...params).traceChild(name, attributes)\n          : this.runWebpackSpan.traceChild(name, attributes)\n\n        if (onStart) onStart(span, ...params)\n      }\n    )\n    stopHook.tap({ name: pluginName, stage: Infinity }, (...params: any[]) => {\n      // `stopHook` may be triggered when `startHook` has not in cases\n      // where `stopHook` is used as the terminating event for more\n      // than one pair of hooks.\n      if (!span) {\n        return\n      }\n\n      if (onStop) onStop(span, ...params)\n      span.stop()\n    })\n  }\n\n  traceTopLevelHooks(compiler: any) {\n    this.traceHookPair(\n      'webpack-compilation',\n      compiler.hooks.compilation,\n      compiler.hooks.afterCompile,\n      {\n        parentSpan: () =>\n          webpackInvalidSpans.get(compiler) || this.runWebpackSpan,\n        attrs: () => ({ name: compiler.name }),\n        onStart: (span, compilation) => {\n          spans.set(compilation, span)\n          spans.set(compiler, span)\n          moduleSpansByCompilation.set(compilation, new WeakMap())\n        },\n      }\n    )\n\n    if (compiler.options.mode === 'development') {\n      this.traceHookPair(\n        () => `webpack-invalidated-${compiler.name}`,\n        compiler.hooks.invalid,\n        compiler.hooks.done,\n        {\n          onStart: (span) => webpackInvalidSpans.set(compiler, span),\n          onStop: () => webpackInvalidSpans.delete(compiler),\n          attrs: (fileName: any) => ({\n            trigger: fileName\n              ? path.relative(this.rootDir, fileName).replaceAll(path.sep, '/')\n              : 'manual',\n          }),\n        }\n      )\n    }\n  }\n\n  traceCompilationHooks(compiler: any) {\n    this.traceHookPair('emit', compiler.hooks.emit, compiler.hooks.afterEmit, {\n      parentSpan: () =>\n        webpackInvalidSpans.get(compiler) || this.runWebpackSpan,\n    })\n\n    this.traceHookPair('make', compiler.hooks.make, compiler.hooks.finishMake, {\n      parentSpan: (compilation) => {\n        const compilationSpan = spans.get(compilation)\n        if (!compilationSpan) {\n          return webpackInvalidSpans.get(compiler) || this.runWebpackSpan\n        }\n\n        return compilationSpan\n      },\n      onStart: (span, compilation) => {\n        makeSpanByCompilation.set(compilation, span)\n      },\n      onStop: (_span, compilation) => {\n        makeSpanByCompilation.delete(compilation)\n      },\n    })\n\n    compiler.hooks.compilation.tap(\n      { name: pluginName, stage: -Infinity },\n      (compilation: any) => {\n        compilation.hooks.buildModule.tap(pluginName, (module: any) => {\n          const moduleType = (() => {\n            const r = module.userRequest\n            if (!r || r.endsWith('!')) {\n              return ''\n            } else {\n              const resource = r.split('!').pop()\n              const match = /^[^?]+\\.([^?]+)$/.exec(resource)\n              return match ? match[1] : ''\n            }\n          })()\n\n          const issuerModule = compilation?.moduleGraph?.getIssuer(module)\n\n          let span: Span\n\n          const moduleSpans = moduleSpansByCompilation.get(compilation)\n          const spanName = `build-module${moduleType ? `-${moduleType}` : ''}`\n          const issuerSpan: Span | undefined =\n            issuerModule && moduleSpans?.get(issuerModule)\n          if (issuerSpan) {\n            span = issuerSpan.traceChild(spanName)\n          } else {\n            let parentSpan: Span | undefined\n            for (const incomingConnection of compilation.moduleGraph.getIncomingConnections(\n              module\n            )) {\n              const entrySpan = spans.get(incomingConnection.dependency)\n              if (entrySpan) {\n                parentSpan = entrySpan\n                break\n              }\n            }\n\n            if (!parentSpan) {\n              const compilationSpan = spans.get(compilation)\n              if (!compilationSpan) {\n                return\n              }\n\n              parentSpan = compilationSpan\n            }\n            span = parentSpan.traceChild(spanName)\n          }\n          span.setAttribute('name', module.userRequest)\n          span.setAttribute('layer', module.layer)\n          moduleSpans!.set(module, span)\n        })\n\n        const moduleHooks = NormalModule.getCompilationHooks(compilation)\n        moduleHooks.readResource.for(undefined).intercept({\n          register(tapInfo: any) {\n            const fn = tapInfo.fn\n            tapInfo.fn = (loaderContext: any, callback: any) => {\n              fn(loaderContext, (err: any, result: any) => {\n                callback(err, result)\n              })\n            }\n            return tapInfo\n          },\n        })\n\n        moduleHooks.loader.tap(\n          pluginName,\n          (loaderContext: any, module: any) => {\n            const moduleSpan = moduleSpansByCompilation\n              .get(compilation)\n              ?.get(module)\n            loaderContext.currentTraceSpan = moduleSpan\n          }\n        )\n\n        compilation.hooks.succeedModule.tap(pluginName, (module: any) => {\n          moduleSpansByCompilation?.get(compilation)?.get(module)?.stop()\n        })\n        compilation.hooks.failedModule.tap(pluginName, (module: any) => {\n          moduleSpansByCompilation?.get(compilation)?.get(module)?.stop()\n        })\n\n        this.traceHookPair(\n          'seal',\n          compilation.hooks.seal,\n          compilation.hooks.afterSeal,\n          {\n            parentSpan: () => spans.get(compilation)!,\n            onStart(span) {\n              sealSpanByCompilation.set(compilation, span)\n            },\n            onStop() {\n              sealSpanByCompilation.delete(compilation)\n            },\n          }\n        )\n\n        compilation.hooks.addEntry.tap(pluginName, (entry: any) => {\n          const parentSpan =\n            makeSpanByCompilation.get(compilation) || spans.get(compilation)\n          if (!parentSpan) {\n            return\n          }\n          const addEntrySpan = parentSpan.traceChild('add-entry')\n          addEntrySpan.setAttribute('request', entry.request)\n          spans.set(entry, addEntrySpan)\n        })\n\n        compilation.hooks.succeedEntry.tap(pluginName, (entry: any) => {\n          spans.get(entry)?.stop()\n          spans.delete(entry)\n        })\n        compilation.hooks.failedEntry.tap(pluginName, (entry: any) => {\n          spans.get(entry)?.stop()\n          spans.delete(entry)\n        })\n\n        this.traceHookPair(\n          'chunk-graph',\n          compilation.hooks.beforeChunks,\n          compilation.hooks.afterChunks,\n          {\n            parentSpan: () =>\n              sealSpanByCompilation.get(compilation) || spans.get(compilation)!,\n          }\n        )\n        this.traceHookPair(\n          'optimize',\n          compilation.hooks.optimize,\n          compilation.hooks.reviveModules,\n          {\n            parentSpan: () =>\n              sealSpanByCompilation.get(compilation) || spans.get(compilation)!,\n          }\n        )\n        this.traceHookPair(\n          'optimize-modules',\n          compilation.hooks.optimizeModules,\n          compilation.hooks.afterOptimizeModules,\n          {\n            parentSpan: () =>\n              sealSpanByCompilation.get(compilation) || spans.get(compilation)!,\n          }\n        )\n        this.traceHookPair(\n          'optimize-chunks',\n          compilation.hooks.optimizeChunks,\n          compilation.hooks.afterOptimizeChunks,\n          {\n            parentSpan: () =>\n              sealSpanByCompilation.get(compilation) || spans.get(compilation)!,\n          }\n        )\n        this.traceHookPair(\n          'optimize-tree',\n          compilation.hooks.optimizeTree,\n          compilation.hooks.afterOptimizeTree,\n          {\n            parentSpan: () =>\n              sealSpanByCompilation.get(compilation) || spans.get(compilation)!,\n          }\n        )\n        this.traceHookPair(\n          'optimize-chunk-modules',\n          compilation.hooks.optimizeChunkModules,\n          compilation.hooks.afterOptimizeChunkModules,\n          {\n            parentSpan: () =>\n              sealSpanByCompilation.get(compilation) || spans.get(compilation)!,\n          }\n        )\n        this.traceHookPair(\n          'module-hash',\n          compilation.hooks.beforeModuleHash,\n          compilation.hooks.afterModuleHash,\n          {\n            parentSpan: () =>\n              sealSpanByCompilation.get(compilation) || spans.get(compilation)!,\n          }\n        )\n        this.traceHookPair(\n          'code-generation',\n          compilation.hooks.beforeCodeGeneration,\n          compilation.hooks.afterCodeGeneration,\n          {\n            parentSpan: () =>\n              sealSpanByCompilation.get(compilation) || spans.get(compilation)!,\n          }\n        )\n        this.traceHookPair(\n          'hash',\n          compilation.hooks.beforeHash,\n          compilation.hooks.afterHash,\n          {\n            parentSpan: () =>\n              sealSpanByCompilation.get(compilation) || spans.get(compilation)!,\n          }\n        )\n        this.traceHookPair(\n          'code-generation-jobs',\n          compilation.hooks.afterHash,\n          compilation.hooks.beforeModuleAssets,\n          {\n            parentSpan: () =>\n              sealSpanByCompilation.get(compilation) || spans.get(compilation)!,\n          }\n        )\n\n        const logs = new Map()\n        const originalTime = compilation.logger.time\n        const originalTimeEnd = compilation.logger.timeEnd\n\n        compilation.logger.time = (label: string) => {\n          if (!inTraceLabelsSeal(label)) {\n            return originalTime.call(compilation.logger, label)\n          }\n          const span = sealSpanByCompilation.get(compilation)\n          if (span) {\n            logs.set(label, span.traceChild(label.replace(/ /g, '-')))\n          }\n          return originalTime.call(compilation.logger, label)\n        }\n        compilation.logger.timeEnd = (label: string) => {\n          if (!inTraceLabelsSeal(label)) {\n            return originalTimeEnd.call(compilation.logger, label)\n          }\n\n          const span = logs.get(label)\n          if (span) {\n            span.stop()\n            logs.delete(label)\n          }\n          return originalTimeEnd.call(compilation.logger, label)\n        }\n      }\n    )\n  }\n}\n"], "names": ["NormalModule", "path", "pluginName", "spans", "WeakMap", "moduleSpansByCompilation", "makeSpanByCompilation", "sealSpanByCompilation", "webpackInvalidSpans", "TRACE_LABELS_SEAL", "inTraceLabelsSeal", "label", "some", "l", "startsWith", "Profiling<PERSON><PERSON><PERSON>", "constructor", "runWebpackSpan", "rootDir", "apply", "compiler", "traceTopLevelHooks", "traceCompilationHooks", "traceHookPair", "spanName", "startHook", "stopHook", "parentSpan", "attrs", "onStart", "onStop", "span", "tap", "name", "stage", "Infinity", "params", "attributes", "<PERSON><PERSON><PERSON><PERSON>", "stop", "hooks", "compilation", "afterCompile", "get", "set", "options", "mode", "invalid", "done", "delete", "fileName", "trigger", "relative", "replaceAll", "sep", "emit", "afterEmit", "make", "finishMake", "compilationSpan", "_span", "buildModule", "module", "moduleType", "r", "userRequest", "endsWith", "resource", "split", "pop", "match", "exec", "issuerModule", "moduleGraph", "get<PERSON><PERSON><PERSON>", "moduleSpans", "issuerSpan", "incomingConnection", "getIncomingConnections", "entrySpan", "dependency", "setAttribute", "layer", "moduleHooks", "getCompilationHooks", "readResource", "for", "undefined", "intercept", "register", "tapInfo", "fn", "loaderContext", "callback", "err", "result", "loader", "moduleSpan", "currentTraceSpan", "succeedModule", "failedModule", "seal", "afterSeal", "addEntry", "entry", "addEntrySpan", "request", "<PERSON><PERSON><PERSON><PERSON>", "failedEntry", "beforeChunks", "after<PERSON><PERSON><PERSON>", "optimize", "reviveModules", "optimizeModules", "afterOptimizeModules", "optimizeChunks", "afterOptimizeChunks", "optimizeTree", "afterOptimizeTree", "optimizeChunkModules", "afterOptimizeChunkModules", "beforeModuleHash", "afterModuleHash", "beforeCodeGeneration", "afterCodeGeneration", "beforeHash", "afterHash", "beforeModuleAssets", "logs", "Map", "originalTime", "logger", "time", "originalTimeEnd", "timeEnd", "call", "replace"], "mappings": "AAAA,SAASA,YAAY,QAAQ,qCAAoC;AAGjE,OAAOC,UAAU,OAAM;AAEvB,MAAMC,aAAa;AACnB,OAAO,MAAMC,QAAQ,IAAIC,UAAuD;AAChF,MAAMC,2BAA2B,IAAID;AAIrC,MAAME,wBAAwB,IAAIF;AAClC,MAAMG,wBAAwB,IAAIH;AAClC,OAAO,MAAMI,sBAAsB,IAAIJ,UAAoB;AAE3D,MAAMK,oBAAoB;IACxB;IACA;IACA;IACA;IACA;CACD;AAED,SAASC,kBAAkBC,KAAa;IACtC,OAAOF,kBAAkBG,IAAI,CAAC,CAACC,IAAMF,MAAMG,UAAU,CAACD;AACxD;AAEA,OAAO,MAAME;IAKXC,YAAY,EACVC,cAAc,EACdC,OAAO,EAIR,CAAE;QACD,IAAI,CAACD,cAAc,GAAGA;QACtB,IAAI,CAACC,OAAO,GAAGA;IACjB;IACAC,MAAMC,QAAa,EAAE;QACnB,IAAI,CAACC,kBAAkB,CAACD;QACxB,IAAI,CAACE,qBAAqB,CAACF;QAC3B,IAAI,CAACA,QAAQ,GAAGA;IAClB;IAEAG,cACEC,QAAiC,EACjCC,SAAc,EACdC,QAAa,EACb,EACEC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,MAAM,EAMP,GAAG,CAAC,CAAC,EACN;QACA,IAAIC;QACJN,UAAUO,GAAG,CACX;YAAEC,MAAM/B;YAAYgC,OAAO,CAACC;QAAS,GACrC,CAAC,GAAGC;YACF,MAAMH,OAAO,OAAOT,aAAa,aAAaA,aAAaA;YAC3D,MAAMa,aAAaT,QAAQA,SAASQ,UAAUR;YAC9CG,OAAOJ,aACHA,cAAcS,QAAQE,UAAU,CAACL,MAAMI,cACvC,IAAI,CAACpB,cAAc,CAACqB,UAAU,CAACL,MAAMI;YAEzC,IAAIR,SAASA,QAAQE,SAASK;QAChC;QAEFV,SAASM,GAAG,CAAC;YAAEC,MAAM/B;YAAYgC,OAAOC;QAAS,GAAG,CAAC,GAAGC;YACtD,gEAAgE;YAChE,6DAA6D;YAC7D,0BAA0B;YAC1B,IAAI,CAACL,MAAM;gBACT;YACF;YAEA,IAAID,QAAQA,OAAOC,SAASK;YAC5BL,KAAKQ,IAAI;QACX;IACF;IAEAlB,mBAAmBD,QAAa,EAAE;QAChC,IAAI,CAACG,aAAa,CAChB,uBACAH,SAASoB,KAAK,CAACC,WAAW,EAC1BrB,SAASoB,KAAK,CAACE,YAAY,EAC3B;YACEf,YAAY,IACVnB,oBAAoBmC,GAAG,CAACvB,aAAa,IAAI,CAACH,cAAc;YAC1DW,OAAO,IAAO,CAAA;oBAAEK,MAAMb,SAASa,IAAI;gBAAC,CAAA;YACpCJ,SAAS,CAACE,MAAMU;gBACdtC,MAAMyC,GAAG,CAACH,aAAaV;gBACvB5B,MAAMyC,GAAG,CAACxB,UAAUW;gBACpB1B,yBAAyBuC,GAAG,CAACH,aAAa,IAAIrC;YAChD;QACF;QAGF,IAAIgB,SAASyB,OAAO,CAACC,IAAI,KAAK,eAAe;YAC3C,IAAI,CAACvB,aAAa,CAChB,IAAM,CAAC,oBAAoB,EAAEH,SAASa,IAAI,EAAE,EAC5Cb,SAASoB,KAAK,CAACO,OAAO,EACtB3B,SAASoB,KAAK,CAACQ,IAAI,EACnB;gBACEnB,SAAS,CAACE,OAASvB,oBAAoBoC,GAAG,CAACxB,UAAUW;gBACrDD,QAAQ,IAAMtB,oBAAoByC,MAAM,CAAC7B;gBACzCQ,OAAO,CAACsB,WAAmB,CAAA;wBACzBC,SAASD,WACLjD,KAAKmD,QAAQ,CAAC,IAAI,CAAClC,OAAO,EAAEgC,UAAUG,UAAU,CAACpD,KAAKqD,GAAG,EAAE,OAC3D;oBACN,CAAA;YACF;QAEJ;IACF;IAEAhC,sBAAsBF,QAAa,EAAE;QACnC,IAAI,CAACG,aAAa,CAAC,QAAQH,SAASoB,KAAK,CAACe,IAAI,EAAEnC,SAASoB,KAAK,CAACgB,SAAS,EAAE;YACxE7B,YAAY,IACVnB,oBAAoBmC,GAAG,CAACvB,aAAa,IAAI,CAACH,cAAc;QAC5D;QAEA,IAAI,CAACM,aAAa,CAAC,QAAQH,SAASoB,KAAK,CAACiB,IAAI,EAAErC,SAASoB,KAAK,CAACkB,UAAU,EAAE;YACzE/B,YAAY,CAACc;gBACX,MAAMkB,kBAAkBxD,MAAMwC,GAAG,CAACF;gBAClC,IAAI,CAACkB,iBAAiB;oBACpB,OAAOnD,oBAAoBmC,GAAG,CAACvB,aAAa,IAAI,CAACH,cAAc;gBACjE;gBAEA,OAAO0C;YACT;YACA9B,SAAS,CAACE,MAAMU;gBACdnC,sBAAsBsC,GAAG,CAACH,aAAaV;YACzC;YACAD,QAAQ,CAAC8B,OAAOnB;gBACdnC,sBAAsB2C,MAAM,CAACR;YAC/B;QACF;QAEArB,SAASoB,KAAK,CAACC,WAAW,CAACT,GAAG,CAC5B;YAAEC,MAAM/B;YAAYgC,OAAO,CAACC;QAAS,GACrC,CAACM;YACCA,YAAYD,KAAK,CAACqB,WAAW,CAAC7B,GAAG,CAAC9B,YAAY,CAAC4D;oBAYxBrB;gBAXrB,MAAMsB,aAAa,AAAC,CAAA;oBAClB,MAAMC,IAAIF,OAAOG,WAAW;oBAC5B,IAAI,CAACD,KAAKA,EAAEE,QAAQ,CAAC,MAAM;wBACzB,OAAO;oBACT,OAAO;wBACL,MAAMC,WAAWH,EAAEI,KAAK,CAAC,KAAKC,GAAG;wBACjC,MAAMC,QAAQ,mBAAmBC,IAAI,CAACJ;wBACtC,OAAOG,QAAQA,KAAK,CAAC,EAAE,GAAG;oBAC5B;gBACF,CAAA;gBAEA,MAAME,eAAe/B,gCAAAA,2BAAAA,YAAagC,WAAW,qBAAxBhC,yBAA0BiC,SAAS,CAACZ;gBAEzD,IAAI/B;gBAEJ,MAAM4C,cAActE,yBAAyBsC,GAAG,CAACF;gBACjD,MAAMjB,WAAW,CAAC,YAAY,EAAEuC,aAAa,CAAC,CAAC,EAAEA,YAAY,GAAG,IAAI;gBACpE,MAAMa,aACJJ,iBAAgBG,+BAAAA,YAAahC,GAAG,CAAC6B;gBACnC,IAAII,YAAY;oBACd7C,OAAO6C,WAAWtC,UAAU,CAACd;gBAC/B,OAAO;oBACL,IAAIG;oBACJ,KAAK,MAAMkD,sBAAsBpC,YAAYgC,WAAW,CAACK,sBAAsB,CAC7EhB,QACC;wBACD,MAAMiB,YAAY5E,MAAMwC,GAAG,CAACkC,mBAAmBG,UAAU;wBACzD,IAAID,WAAW;4BACbpD,aAAaoD;4BACb;wBACF;oBACF;oBAEA,IAAI,CAACpD,YAAY;wBACf,MAAMgC,kBAAkBxD,MAAMwC,GAAG,CAACF;wBAClC,IAAI,CAACkB,iBAAiB;4BACpB;wBACF;wBAEAhC,aAAagC;oBACf;oBACA5B,OAAOJ,WAAWW,UAAU,CAACd;gBAC/B;gBACAO,KAAKkD,YAAY,CAAC,QAAQnB,OAAOG,WAAW;gBAC5ClC,KAAKkD,YAAY,CAAC,SAASnB,OAAOoB,KAAK;gBACvCP,YAAa/B,GAAG,CAACkB,QAAQ/B;YAC3B;YAEA,MAAMoD,cAAcnF,aAAaoF,mBAAmB,CAAC3C;YACrD0C,YAAYE,YAAY,CAACC,GAAG,CAACC,WAAWC,SAAS,CAAC;gBAChDC,UAASC,OAAY;oBACnB,MAAMC,KAAKD,QAAQC,EAAE;oBACrBD,QAAQC,EAAE,GAAG,CAACC,eAAoBC;wBAChCF,GAAGC,eAAe,CAACE,KAAUC;4BAC3BF,SAASC,KAAKC;wBAChB;oBACF;oBACA,OAAOL;gBACT;YACF;YAEAP,YAAYa,MAAM,CAAChE,GAAG,CACpB9B,YACA,CAAC0F,eAAoB9B;oBACAzD;gBAAnB,MAAM4F,cAAa5F,gCAAAA,yBAChBsC,GAAG,CAACF,iCADYpC,8BAEfsC,GAAG,CAACmB;gBACR8B,cAAcM,gBAAgB,GAAGD;YACnC;YAGFxD,YAAYD,KAAK,CAAC2D,aAAa,CAACnE,GAAG,CAAC9B,YAAY,CAAC4D;oBAC/CzD,mCAAAA;gBAAAA,6CAAAA,gCAAAA,yBAA0BsC,GAAG,CAACF,kCAA9BpC,oCAAAA,8BAA4CsC,GAAG,CAACmB,4BAAhDzD,kCAAyDkC,IAAI;YAC/D;YACAE,YAAYD,KAAK,CAAC4D,YAAY,CAACpE,GAAG,CAAC9B,YAAY,CAAC4D;oBAC9CzD,mCAAAA;gBAAAA,6CAAAA,gCAAAA,yBAA0BsC,GAAG,CAACF,kCAA9BpC,oCAAAA,8BAA4CsC,GAAG,CAACmB,4BAAhDzD,kCAAyDkC,IAAI;YAC/D;YAEA,IAAI,CAAChB,aAAa,CAChB,QACAkB,YAAYD,KAAK,CAAC6D,IAAI,EACtB5D,YAAYD,KAAK,CAAC8D,SAAS,EAC3B;gBACE3E,YAAY,IAAMxB,MAAMwC,GAAG,CAACF;gBAC5BZ,SAAQE,IAAI;oBACVxB,sBAAsBqC,GAAG,CAACH,aAAaV;gBACzC;gBACAD;oBACEvB,sBAAsB0C,MAAM,CAACR;gBAC/B;YACF;YAGFA,YAAYD,KAAK,CAAC+D,QAAQ,CAACvE,GAAG,CAAC9B,YAAY,CAACsG;gBAC1C,MAAM7E,aACJrB,sBAAsBqC,GAAG,CAACF,gBAAgBtC,MAAMwC,GAAG,CAACF;gBACtD,IAAI,CAACd,YAAY;oBACf;gBACF;gBACA,MAAM8E,eAAe9E,WAAWW,UAAU,CAAC;gBAC3CmE,aAAaxB,YAAY,CAAC,WAAWuB,MAAME,OAAO;gBAClDvG,MAAMyC,GAAG,CAAC4D,OAAOC;YACnB;YAEAhE,YAAYD,KAAK,CAACmE,YAAY,CAAC3E,GAAG,CAAC9B,YAAY,CAACsG;oBAC9CrG;iBAAAA,aAAAA,MAAMwC,GAAG,CAAC6D,2BAAVrG,WAAkBoC,IAAI;gBACtBpC,MAAM8C,MAAM,CAACuD;YACf;YACA/D,YAAYD,KAAK,CAACoE,WAAW,CAAC5E,GAAG,CAAC9B,YAAY,CAACsG;oBAC7CrG;iBAAAA,aAAAA,MAAMwC,GAAG,CAAC6D,2BAAVrG,WAAkBoC,IAAI;gBACtBpC,MAAM8C,MAAM,CAACuD;YACf;YAEA,IAAI,CAACjF,aAAa,CAChB,eACAkB,YAAYD,KAAK,CAACqE,YAAY,EAC9BpE,YAAYD,KAAK,CAACsE,WAAW,EAC7B;gBACEnF,YAAY,IACVpB,sBAAsBoC,GAAG,CAACF,gBAAgBtC,MAAMwC,GAAG,CAACF;YACxD;YAEF,IAAI,CAAClB,aAAa,CAChB,YACAkB,YAAYD,KAAK,CAACuE,QAAQ,EAC1BtE,YAAYD,KAAK,CAACwE,aAAa,EAC/B;gBACErF,YAAY,IACVpB,sBAAsBoC,GAAG,CAACF,gBAAgBtC,MAAMwC,GAAG,CAACF;YACxD;YAEF,IAAI,CAAClB,aAAa,CAChB,oBACAkB,YAAYD,KAAK,CAACyE,eAAe,EACjCxE,YAAYD,KAAK,CAAC0E,oBAAoB,EACtC;gBACEvF,YAAY,IACVpB,sBAAsBoC,GAAG,CAACF,gBAAgBtC,MAAMwC,GAAG,CAACF;YACxD;YAEF,IAAI,CAAClB,aAAa,CAChB,mBACAkB,YAAYD,KAAK,CAAC2E,cAAc,EAChC1E,YAAYD,KAAK,CAAC4E,mBAAmB,EACrC;gBACEzF,YAAY,IACVpB,sBAAsBoC,GAAG,CAACF,gBAAgBtC,MAAMwC,GAAG,CAACF;YACxD;YAEF,IAAI,CAAClB,aAAa,CAChB,iBACAkB,YAAYD,KAAK,CAAC6E,YAAY,EAC9B5E,YAAYD,KAAK,CAAC8E,iBAAiB,EACnC;gBACE3F,YAAY,IACVpB,sBAAsBoC,GAAG,CAACF,gBAAgBtC,MAAMwC,GAAG,CAACF;YACxD;YAEF,IAAI,CAAClB,aAAa,CAChB,0BACAkB,YAAYD,KAAK,CAAC+E,oBAAoB,EACtC9E,YAAYD,KAAK,CAACgF,yBAAyB,EAC3C;gBACE7F,YAAY,IACVpB,sBAAsBoC,GAAG,CAACF,gBAAgBtC,MAAMwC,GAAG,CAACF;YACxD;YAEF,IAAI,CAAClB,aAAa,CAChB,eACAkB,YAAYD,KAAK,CAACiF,gBAAgB,EAClChF,YAAYD,KAAK,CAACkF,eAAe,EACjC;gBACE/F,YAAY,IACVpB,sBAAsBoC,GAAG,CAACF,gBAAgBtC,MAAMwC,GAAG,CAACF;YACxD;YAEF,IAAI,CAAClB,aAAa,CAChB,mBACAkB,YAAYD,KAAK,CAACmF,oBAAoB,EACtClF,YAAYD,KAAK,CAACoF,mBAAmB,EACrC;gBACEjG,YAAY,IACVpB,sBAAsBoC,GAAG,CAACF,gBAAgBtC,MAAMwC,GAAG,CAACF;YACxD;YAEF,IAAI,CAAClB,aAAa,CAChB,QACAkB,YAAYD,KAAK,CAACqF,UAAU,EAC5BpF,YAAYD,KAAK,CAACsF,SAAS,EAC3B;gBACEnG,YAAY,IACVpB,sBAAsBoC,GAAG,CAACF,gBAAgBtC,MAAMwC,GAAG,CAACF;YACxD;YAEF,IAAI,CAAClB,aAAa,CAChB,wBACAkB,YAAYD,KAAK,CAACsF,SAAS,EAC3BrF,YAAYD,KAAK,CAACuF,kBAAkB,EACpC;gBACEpG,YAAY,IACVpB,sBAAsBoC,GAAG,CAACF,gBAAgBtC,MAAMwC,GAAG,CAACF;YACxD;YAGF,MAAMuF,OAAO,IAAIC;YACjB,MAAMC,eAAezF,YAAY0F,MAAM,CAACC,IAAI;YAC5C,MAAMC,kBAAkB5F,YAAY0F,MAAM,CAACG,OAAO;YAElD7F,YAAY0F,MAAM,CAACC,IAAI,GAAG,CAACzH;gBACzB,IAAI,CAACD,kBAAkBC,QAAQ;oBAC7B,OAAOuH,aAAaK,IAAI,CAAC9F,YAAY0F,MAAM,EAAExH;gBAC/C;gBACA,MAAMoB,OAAOxB,sBAAsBoC,GAAG,CAACF;gBACvC,IAAIV,MAAM;oBACRiG,KAAKpF,GAAG,CAACjC,OAAOoB,KAAKO,UAAU,CAAC3B,MAAM6H,OAAO,CAAC,MAAM;gBACtD;gBACA,OAAON,aAAaK,IAAI,CAAC9F,YAAY0F,MAAM,EAAExH;YAC/C;YACA8B,YAAY0F,MAAM,CAACG,OAAO,GAAG,CAAC3H;gBAC5B,IAAI,CAACD,kBAAkBC,QAAQ;oBAC7B,OAAO0H,gBAAgBE,IAAI,CAAC9F,YAAY0F,MAAM,EAAExH;gBAClD;gBAEA,MAAMoB,OAAOiG,KAAKrF,GAAG,CAAChC;gBACtB,IAAIoB,MAAM;oBACRA,KAAKQ,IAAI;oBACTyF,KAAK/E,MAAM,CAACtC;gBACd;gBACA,OAAO0H,gBAAgBE,IAAI,CAAC9F,YAAY0F,MAAM,EAAExH;YAClD;QACF;IAEJ;AACF", "ignoreList": [0]}