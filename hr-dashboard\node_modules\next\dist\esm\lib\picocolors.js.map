{"version": 3, "sources": ["../../src/lib/picocolors.ts"], "sourcesContent": ["// ISC License\n\n// Copyright (c) 2021 <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>\n\n// Permission to use, copy, modify, and/or distribute this software for any\n// purpose with or without fee is hereby granted, provided that the above\n// copyright notice and this permission notice appear in all copies.\n\n// THE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\n// WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\n// MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\n// ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\n// WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN\n// ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF\n// OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n//\n// https://github.com/ale<PERSON><PERSON><PERSON>ov/picocolors/blob/b6261487e7b81aaab2440e397a356732cad9e342/picocolors.js#L1\n\nconst { env, stdout } = globalThis?.process ?? {}\n\nconst enabled =\n  env &&\n  !env.NO_COLOR &&\n  (env.FORCE_COLOR || (stdout?.isTTY && !env.CI && env.TERM !== 'dumb'))\n\nconst replaceClose = (\n  str: string,\n  close: string,\n  replace: string,\n  index: number\n): string => {\n  const start = str.substring(0, index) + replace\n  const end = str.substring(index + close.length)\n  const nextIndex = end.indexOf(close)\n  return ~nextIndex\n    ? start + replaceClose(end, close, replace, nextIndex)\n    : start + end\n}\n\nconst formatter = (open: string, close: string, replace = open) => {\n  if (!enabled) return String\n  return (input: string) => {\n    const string = '' + input\n    const index = string.indexOf(close, open.length)\n    return ~index\n      ? open + replaceClose(string, close, replace, index) + close\n      : open + string + close\n  }\n}\n\nexport const reset = enabled ? (s: string) => `\\x1b[0m${s}\\x1b[0m` : String\nexport const bold = formatter('\\x1b[1m', '\\x1b[22m', '\\x1b[22m\\x1b[1m')\nexport const dim = formatter('\\x1b[2m', '\\x1b[22m', '\\x1b[22m\\x1b[2m')\nexport const italic = formatter('\\x1b[3m', '\\x1b[23m')\nexport const underline = formatter('\\x1b[4m', '\\x1b[24m')\nexport const inverse = formatter('\\x1b[7m', '\\x1b[27m')\nexport const hidden = formatter('\\x1b[8m', '\\x1b[28m')\nexport const strikethrough = formatter('\\x1b[9m', '\\x1b[29m')\nexport const black = formatter('\\x1b[30m', '\\x1b[39m')\nexport const red = formatter('\\x1b[31m', '\\x1b[39m')\nexport const green = formatter('\\x1b[32m', '\\x1b[39m')\nexport const yellow = formatter('\\x1b[33m', '\\x1b[39m')\nexport const blue = formatter('\\x1b[34m', '\\x1b[39m')\nexport const magenta = formatter('\\x1b[35m', '\\x1b[39m')\nexport const purple = formatter('\\x1b[38;2;173;127;168m', '\\x1b[39m')\nexport const cyan = formatter('\\x1b[36m', '\\x1b[39m')\nexport const white = formatter('\\x1b[37m', '\\x1b[39m')\nexport const gray = formatter('\\x1b[90m', '\\x1b[39m')\nexport const bgBlack = formatter('\\x1b[40m', '\\x1b[49m')\nexport const bgRed = formatter('\\x1b[41m', '\\x1b[49m')\nexport const bgGreen = formatter('\\x1b[42m', '\\x1b[49m')\nexport const bgYellow = formatter('\\x1b[43m', '\\x1b[49m')\nexport const bgBlue = formatter('\\x1b[44m', '\\x1b[49m')\nexport const bgMagenta = formatter('\\x1b[45m', '\\x1b[49m')\nexport const bgCyan = formatter('\\x1b[46m', '\\x1b[49m')\nexport const bgWhite = formatter('\\x1b[47m', '\\x1b[49m')\n"], "names": ["globalThis", "env", "stdout", "process", "enabled", "NO_COLOR", "FORCE_COLOR", "isTTY", "CI", "TERM", "replaceClose", "str", "close", "replace", "index", "start", "substring", "end", "length", "nextIndex", "indexOf", "formatter", "open", "String", "input", "string", "reset", "s", "bold", "dim", "italic", "underline", "inverse", "hidden", "strikethrough", "black", "red", "green", "yellow", "blue", "magenta", "purple", "cyan", "white", "gray", "bgBlack", "bgRed", "bgGreen", "bgYellow", "bgBlue", "bgMagenta", "bg<PERSON>yan", "bgWhite"], "mappings": "AAAA,cAAc;AAEd,wEAAwE;AAExE,2EAA2E;AAC3E,yEAAyE;AACzE,oEAAoE;AAEpE,2EAA2E;AAC3E,mEAAmE;AACnE,0EAA0E;AAC1E,yEAAyE;AACzE,wEAAwE;AACxE,0EAA0E;AAC1E,iEAAiE;AACjE,EAAE;AACF,8GAA8G;IAEtFA;AAAxB,MAAM,EAAEC,GAAG,EAAEC,MAAM,EAAE,GAAGF,EAAAA,cAAAA,+BAAAA,YAAYG,OAAO,KAAI,CAAC;AAEhD,MAAMC,UACJH,OACA,CAACA,IAAII,QAAQ,IACZJ,CAAAA,IAAIK,WAAW,IAAKJ,CAAAA,0BAAAA,OAAQK,KAAK,KAAI,CAACN,IAAIO,EAAE,IAAIP,IAAIQ,IAAI,KAAK,MAAM;AAEtE,MAAMC,eAAe,CACnBC,KACAC,OACAC,SACAC;IAEA,MAAMC,QAAQJ,IAAIK,SAAS,CAAC,GAAGF,SAASD;IACxC,MAAMI,MAAMN,IAAIK,SAAS,CAACF,QAAQF,MAAMM,MAAM;IAC9C,MAAMC,YAAYF,IAAIG,OAAO,CAACR;IAC9B,OAAO,CAACO,YACJJ,QAAQL,aAAaO,KAAKL,OAAOC,SAASM,aAC1CJ,QAAQE;AACd;AAEA,MAAMI,YAAY,CAACC,MAAcV,OAAeC,UAAUS,IAAI;IAC5D,IAAI,CAAClB,SAAS,OAAOmB;IACrB,OAAO,CAACC;QACN,MAAMC,SAAS,KAAKD;QACpB,MAAMV,QAAQW,OAAOL,OAAO,CAACR,OAAOU,KAAKJ,MAAM;QAC/C,OAAO,CAACJ,QACJQ,OAAOZ,aAAae,QAAQb,OAAOC,SAASC,SAASF,QACrDU,OAAOG,SAASb;IACtB;AACF;AAEA,OAAO,MAAMc,QAAQtB,UAAU,CAACuB,IAAc,CAAC,OAAO,EAAEA,EAAE,OAAO,CAAC,GAAGJ,OAAM;AAC3E,OAAO,MAAMK,OAAOP,UAAU,WAAW,YAAY,mBAAkB;AACvE,OAAO,MAAMQ,MAAMR,UAAU,WAAW,YAAY,mBAAkB;AACtE,OAAO,MAAMS,SAAST,UAAU,WAAW,YAAW;AACtD,OAAO,MAAMU,YAAYV,UAAU,WAAW,YAAW;AACzD,OAAO,MAAMW,UAAUX,UAAU,WAAW,YAAW;AACvD,OAAO,MAAMY,SAASZ,UAAU,WAAW,YAAW;AACtD,OAAO,MAAMa,gBAAgBb,UAAU,WAAW,YAAW;AAC7D,OAAO,MAAMc,QAAQd,UAAU,YAAY,YAAW;AACtD,OAAO,MAAMe,MAAMf,UAAU,YAAY,YAAW;AACpD,OAAO,MAAMgB,QAAQhB,UAAU,YAAY,YAAW;AACtD,OAAO,MAAMiB,SAASjB,UAAU,YAAY,YAAW;AACvD,OAAO,MAAMkB,OAAOlB,UAAU,YAAY,YAAW;AACrD,OAAO,MAAMmB,UAAUnB,UAAU,YAAY,YAAW;AACxD,OAAO,MAAMoB,SAASpB,UAAU,0BAA0B,YAAW;AACrE,OAAO,MAAMqB,OAAOrB,UAAU,YAAY,YAAW;AACrD,OAAO,MAAMsB,QAAQtB,UAAU,YAAY,YAAW;AACtD,OAAO,MAAMuB,OAAOvB,UAAU,YAAY,YAAW;AACrD,OAAO,MAAMwB,UAAUxB,UAAU,YAAY,YAAW;AACxD,OAAO,MAAMyB,QAAQzB,UAAU,YAAY,YAAW;AACtD,OAAO,MAAM0B,UAAU1B,UAAU,YAAY,YAAW;AACxD,OAAO,MAAM2B,WAAW3B,UAAU,YAAY,YAAW;AACzD,OAAO,MAAM4B,SAAS5B,UAAU,YAAY,YAAW;AACvD,OAAO,MAAM6B,YAAY7B,UAAU,YAAY,YAAW;AAC1D,OAAO,MAAM8B,SAAS9B,UAAU,YAAY,YAAW;AACvD,OAAO,MAAM+B,UAAU/B,UAAU,YAAY,YAAW", "ignoreList": [0]}