{"version": 3, "sources": ["../../../src/server/dev/on-demand-entry-handler.ts"], "sourcesContent": ["import type ws from 'next/dist/compiled/ws'\nimport type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport type { NextConfigComplete } from '../config-shared'\nimport type {\n  DynamicParamTypesShort,\n  FlightRouterState,\n  FlightSegmentPath,\n} from '../app-render/types'\nimport type { CompilerNameValues } from '../../shared/lib/constants'\nimport type { RouteDefinition } from '../route-definitions/route-definition'\nimport type HotReloaderWebpack from './hot-reloader-webpack'\n\nimport createDebug from 'next/dist/compiled/debug'\nimport { EventEmitter } from 'events'\nimport { findPageFile } from '../lib/find-page-file'\nimport {\n  getStaticInfoIncludingLayouts,\n  runDependingOnPageType,\n} from '../../build/entries'\nimport { join, posix } from 'path'\nimport { normalizePathSep } from '../../shared/lib/page-path/normalize-path-sep'\nimport { normalizePagePath } from '../../shared/lib/page-path/normalize-page-path'\nimport { ensureLeadingSlash } from '../../shared/lib/page-path/ensure-leading-slash'\nimport { removePagePathTail } from '../../shared/lib/page-path/remove-page-path-tail'\nimport { reportTrigger } from '../../build/output'\nimport getRouteFromEntrypoint from '../get-route-from-entrypoint'\nimport {\n  isInstrumentationHookFile,\n  isInstrumentationHookFilename,\n  isMiddlewareFile,\n  isMiddlewareFilename,\n} from '../../build/utils'\nimport { PageNotFoundError, stringifyError } from '../../shared/lib/utils'\nimport {\n  COMPILER_INDEXES,\n  COMPILER_NAMES,\n  RSC_MODULE_TYPES,\n  UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n} from '../../shared/lib/constants'\nimport { PAGE_SEGMENT_KEY } from '../../shared/lib/segment'\nimport { HMR_ACTIONS_SENT_TO_BROWSER } from './hot-reloader-types'\nimport { isAppPageRouteDefinition } from '../route-definitions/app-page-route-definition'\nimport { scheduleOnNextTick } from '../../lib/scheduler'\nimport { Batcher } from '../../lib/batcher'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport { PAGE_TYPES } from '../../lib/page-types'\nimport { getNextFlightSegmentPath } from '../../client/flight-data-helpers'\n\nconst debug = createDebug('next:on-demand-entry-handler')\n\n/**\n * Returns object keys with type inferred from the object key\n */\nconst keys = Object.keys as <T>(o: T) => Extract<keyof T, string>[]\n\nconst COMPILER_KEYS = keys(COMPILER_INDEXES)\n\nfunction treePathToEntrypoint(\n  segmentPath: FlightSegmentPath,\n  parentPath?: string\n): string {\n  const [parallelRouteKey, segment] = segmentPath\n\n  // TODO-APP: modify this path to cover parallelRouteKey convention\n  const path =\n    (parentPath ? parentPath + '/' : '') +\n    (parallelRouteKey !== 'children' && !segment.startsWith('@')\n      ? `@${parallelRouteKey}/`\n      : '') +\n    (segment === '' ? 'page' : segment)\n\n  // Last segment\n  if (segmentPath.length === 2) {\n    return path\n  }\n\n  const childSegmentPath = getNextFlightSegmentPath(segmentPath)\n  return treePathToEntrypoint(childSegmentPath, path)\n}\n\nfunction convertDynamicParamTypeToSyntax(\n  dynamicParamTypeShort: DynamicParamTypesShort,\n  param: string\n) {\n  switch (dynamicParamTypeShort) {\n    case 'c':\n    case 'ci':\n      return `[...${param}]`\n    case 'oc':\n      return `[[...${param}]]`\n    case 'd':\n    case 'di':\n      return `[${param}]`\n    default:\n      throw new Error('Unknown dynamic param type')\n  }\n}\n\n/**\n * format: {compiler type}@{page type}@{page path}\n * e.g. client@pages@/index\n * e.g. server@app@app/page\n *\n * This guarantees the uniqueness for each page, to avoid conflicts between app/ and pages/\n */\n\nexport function getEntryKey(\n  compilerType: CompilerNameValues,\n  pageBundleType: PAGE_TYPES,\n  page: string\n) {\n  // TODO: handle the /children slot better\n  // this is a quick hack to handle when children is provided as children/page instead of /page\n  const pageKey = page.replace(/(@[^/]+)\\/children/g, '$1')\n  return `${compilerType}@${pageBundleType}@${pageKey}`\n}\n\nfunction getPageBundleType(pageBundlePath: string): PAGE_TYPES {\n  // Handle special case for /_error\n  if (pageBundlePath === '/_error') return PAGE_TYPES.PAGES\n  if (isMiddlewareFilename(pageBundlePath)) return PAGE_TYPES.ROOT\n  return pageBundlePath.startsWith('pages/')\n    ? PAGE_TYPES.PAGES\n    : pageBundlePath.startsWith('app/')\n      ? PAGE_TYPES.APP\n      : PAGE_TYPES.ROOT\n}\n\nfunction getEntrypointsFromTree(\n  tree: FlightRouterState,\n  isFirst: boolean,\n  parentPath: string[] = []\n) {\n  const [segment, parallelRoutes] = tree\n\n  const currentSegment = Array.isArray(segment)\n    ? convertDynamicParamTypeToSyntax(segment[2], segment[0])\n    : segment\n\n  const isPageSegment = currentSegment.startsWith(PAGE_SEGMENT_KEY)\n\n  const currentPath = [...parentPath, isPageSegment ? '' : currentSegment]\n\n  if (!isFirst && isPageSegment) {\n    // TODO get rid of '' at the start of tree\n    return [treePathToEntrypoint(currentPath.slice(1))]\n  }\n\n  return Object.keys(parallelRoutes).reduce(\n    (paths: string[], key: string): string[] => {\n      const childTree = parallelRoutes[key]\n      const childPages = getEntrypointsFromTree(childTree, false, [\n        ...currentPath,\n        key,\n      ])\n      return [...paths, ...childPages]\n    },\n    []\n  )\n}\n\nexport const ADDED = Symbol('added')\nexport const BUILDING = Symbol('building')\nexport const BUILT = Symbol('built')\n\ninterface EntryType {\n  /**\n   * Tells if a page is scheduled to be disposed.\n   */\n  dispose?: boolean\n  /**\n   * Timestamp with the last time the page was active.\n   */\n  lastActiveTime?: number\n  /**\n   * Page build status.\n   */\n  status?: typeof ADDED | typeof BUILDING | typeof BUILT\n\n  /**\n   * Path to the page file relative to the dist folder with no extension.\n   * For example: `pages/about/index`\n   */\n  bundlePath: string\n\n  /**\n   * Webpack request to create a dependency for.\n   */\n  request: string\n}\n\n// Shadowing check in ESLint does not account for enum\n// eslint-disable-next-line no-shadow\nexport const enum EntryTypes {\n  ENTRY,\n  CHILD_ENTRY,\n}\ninterface Entry extends EntryType {\n  type: EntryTypes.ENTRY\n  /**\n   * The absolute page to the page file. Used for detecting if the file was removed. For example:\n   * `/Users/<USER>/project/pages/about/index.js`\n   */\n  absolutePagePath: string\n  /**\n   * All parallel pages that match the same entry, for example:\n   * ['/parallel/@bar/nested/@a/page', '/parallel/@bar/nested/@b/page', '/parallel/@foo/nested/@a/page', '/parallel/@foo/nested/@b/page']\n   */\n  appPaths: ReadonlyArray<string> | null\n}\n\ninterface ChildEntry extends EntryType {\n  type: EntryTypes.CHILD_ENTRY\n  /**\n   * Which parent entries use this childEntry.\n   */\n  parentEntries: Set<string>\n  /**\n   * The absolute page to the entry file. Used for detecting if the file was removed. For example:\n   * `/Users/<USER>/project/app/about/layout.js`\n   */\n  absoluteEntryFilePath?: string\n}\n\nconst entriesMap: Map<\n  string,\n  {\n    /**\n     * The key composed of the compiler name and the page. For example:\n     * `edge-server/about`\n     */\n    [entryName: string]: Entry | ChildEntry\n  }\n> = new Map()\n\n// remove /server from end of output for server compiler\nconst normalizeOutputPath = (dir: string) => dir.replace(/[/\\\\]server$/, '')\n\nexport const getEntries = (\n  dir: string\n): NonNullable<ReturnType<(typeof entriesMap)['get']>> => {\n  dir = normalizeOutputPath(dir)\n  const entries = entriesMap.get(dir) || {}\n  entriesMap.set(dir, entries)\n  return entries\n}\n\nconst invalidators: Map<string, Invalidator> = new Map()\n\nexport const getInvalidator = (dir: string) => {\n  dir = normalizeOutputPath(dir)\n  return invalidators.get(dir)\n}\n\nconst doneCallbacks: EventEmitter = new EventEmitter()\nconst lastClientAccessPages = ['']\nconst lastServerAccessPagesForAppDir = ['']\n\ntype BuildingTracker = Set<CompilerNameValues>\ntype RebuildTracker = Set<CompilerNameValues>\n\n// Make sure only one invalidation happens at a time\n// Otherwise, webpack hash gets changed and it'll force the client to reload.\nclass Invalidator {\n  private multiCompiler: webpack.MultiCompiler\n\n  private building: BuildingTracker = new Set()\n  private rebuildAgain: RebuildTracker = new Set()\n\n  constructor(multiCompiler: webpack.MultiCompiler) {\n    this.multiCompiler = multiCompiler\n  }\n\n  public shouldRebuildAll() {\n    return this.rebuildAgain.size > 0\n  }\n\n  invalidate(compilerKeys: typeof COMPILER_KEYS = COMPILER_KEYS): void {\n    for (const key of compilerKeys) {\n      // If there's a current build is processing, we won't abort it by invalidating.\n      // (If aborted, it'll cause a client side hard reload)\n      // But let it to invalidate just after the completion.\n      // So, it can re-build the queued pages at once.\n\n      if (this.building.has(key)) {\n        this.rebuildAgain.add(key)\n        continue\n      }\n\n      this.building.add(key)\n      this.multiCompiler.compilers[COMPILER_INDEXES[key]].watching?.invalidate()\n    }\n  }\n\n  public startBuilding(compilerKey: keyof typeof COMPILER_INDEXES) {\n    this.building.add(compilerKey)\n  }\n\n  public doneBuilding(compilerKeys: typeof COMPILER_KEYS = []) {\n    const rebuild: typeof COMPILER_KEYS = []\n    for (const key of compilerKeys) {\n      this.building.delete(key)\n\n      if (this.rebuildAgain.has(key)) {\n        rebuild.push(key)\n        this.rebuildAgain.delete(key)\n      }\n    }\n\n    if (rebuild.length > 0) {\n      this.invalidate(rebuild)\n    }\n  }\n\n  public willRebuild(compilerKey: keyof typeof COMPILER_INDEXES) {\n    return this.rebuildAgain.has(compilerKey)\n  }\n}\n\nfunction disposeInactiveEntries(\n  entries: NonNullable<ReturnType<(typeof entriesMap)['get']>>,\n  maxInactiveAge: number\n) {\n  Object.keys(entries).forEach((entryKey) => {\n    const entryData = entries[entryKey]\n    const { lastActiveTime, status, dispose, bundlePath } = entryData\n\n    // TODO-APP: implement disposing of CHILD_ENTRY\n    if (entryData.type === EntryTypes.CHILD_ENTRY) {\n      return\n    }\n\n    // For the root middleware and the instrumentation hook files,\n    // we don't dispose them periodically as it's needed for every request.\n    if (\n      isMiddlewareFilename(bundlePath) ||\n      isInstrumentationHookFilename(bundlePath)\n    ) {\n      return\n    }\n\n    if (dispose)\n      // Skip pages already scheduled for disposing\n      return\n\n    // This means this entry is currently building or just added\n    // We don't need to dispose those entries.\n    if (status !== BUILT) return\n\n    // We should not build the last accessed page even we didn't get any pings\n    // Sometimes, it's possible our XHR ping to wait before completing other requests.\n    // In that case, we should not dispose the current viewing page\n    if (\n      lastClientAccessPages.includes(entryKey) ||\n      lastServerAccessPagesForAppDir.includes(entryKey)\n    )\n      return\n\n    if (lastActiveTime && Date.now() - lastActiveTime > maxInactiveAge) {\n      entries[entryKey].dispose = true\n    }\n  })\n}\n\n// Normalize both app paths and page paths\nfunction tryToNormalizePagePath(page: string) {\n  try {\n    return normalizePagePath(page)\n  } catch (err) {\n    console.error(err)\n    throw new PageNotFoundError(page)\n  }\n}\n\ninterface PagePathData {\n  filename: string\n  bundlePath: string\n  page: string\n}\n\n/**\n * Attempts to find a page file path from the given pages absolute directory,\n * a page and allowed extensions. If the page can't be found it will throw an\n * error. It defaults the `/_error` page to Next.js internal error page.\n *\n * @param rootDir Absolute path to the project root.\n * @param page The page normalized (it will be denormalized).\n * @param extensions Array of page extensions.\n * @param pagesDir Absolute path to the pages folder with trailing `/pages`.\n * @param appDir Absolute path to the app folder with trailing `/app`.\n */\nexport async function findPagePathData(\n  rootDir: string,\n  page: string,\n  extensions: string[],\n  pagesDir: string | undefined,\n  appDir: string | undefined,\n  isGlobalNotFoundEnabled: boolean\n): Promise<PagePathData> {\n  const normalizedPagePath = tryToNormalizePagePath(page)\n  let pagePath: string | null = null\n\n  const isInstrumentation = isInstrumentationHookFile(normalizedPagePath)\n  if (isMiddlewareFile(normalizedPagePath) || isInstrumentation) {\n    pagePath = await findPageFile(\n      rootDir,\n      normalizedPagePath,\n      extensions,\n      false\n    )\n\n    if (!pagePath) {\n      throw new PageNotFoundError(normalizedPagePath)\n    }\n\n    const pageUrl = ensureLeadingSlash(\n      removePagePathTail(normalizePathSep(pagePath), {\n        extensions,\n      })\n    )\n\n    let bundlePath = normalizedPagePath\n    let pageKey = posix.normalize(pageUrl)\n\n    if (isInstrumentation || isMiddlewareFile(normalizedPagePath)) {\n      bundlePath = bundlePath.replace('/src', '')\n      pageKey = page.replace('/src', '')\n    }\n\n    return {\n      filename: join(rootDir, pagePath),\n      bundlePath: bundlePath.slice(1),\n      page: pageKey,\n    }\n  }\n\n  // Check appDir first falling back to pagesDir\n  if (appDir) {\n    if (page === UNDERSCORE_NOT_FOUND_ROUTE_ENTRY) {\n      // Load `global-not-found` when global-not-found is enabled.\n      // Prefer to load it when both `global-not-found` and root `not-found` present.\n      if (isGlobalNotFoundEnabled) {\n        const globalNotFoundPath = await findPageFile(\n          appDir,\n          'global-not-found',\n          extensions,\n          true\n        )\n        if (globalNotFoundPath) {\n          return {\n            filename: join(appDir, globalNotFoundPath),\n            bundlePath: `app${UNDERSCORE_NOT_FOUND_ROUTE_ENTRY}`,\n            page: UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n          }\n        }\n      } else {\n        // Then if global-not-found.js doesn't exist then load not-found.js\n        const notFoundPath = await findPageFile(\n          appDir,\n          'not-found',\n          extensions,\n          true\n        )\n        if (notFoundPath) {\n          return {\n            filename: join(appDir, notFoundPath),\n            bundlePath: `app${UNDERSCORE_NOT_FOUND_ROUTE_ENTRY}`,\n            page: UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n          }\n        }\n      }\n\n      // If they're not presented, then fallback to global-not-found\n      return {\n        filename: require.resolve(\n          'next/dist/client/components/builtin/global-not-found'\n        ),\n        bundlePath: `app${UNDERSCORE_NOT_FOUND_ROUTE_ENTRY}`,\n        page: UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n      }\n    }\n    pagePath = await findPageFile(appDir, normalizedPagePath, extensions, true)\n    if (pagePath) {\n      const pageUrl = ensureLeadingSlash(\n        removePagePathTail(normalizePathSep(pagePath), {\n          keepIndex: true,\n          extensions,\n        })\n      )\n\n      return {\n        filename: join(appDir, pagePath),\n        bundlePath: posix.join('app', pageUrl),\n        page: posix.normalize(pageUrl),\n      }\n    }\n  }\n\n  if (!pagePath && pagesDir) {\n    pagePath = await findPageFile(\n      pagesDir,\n      normalizedPagePath,\n      extensions,\n      false\n    )\n  }\n\n  if (pagePath !== null && pagesDir) {\n    const pageUrl = ensureLeadingSlash(\n      removePagePathTail(normalizePathSep(pagePath), {\n        extensions,\n      })\n    )\n\n    return {\n      filename: join(pagesDir, pagePath),\n      bundlePath: posix.join('pages', normalizePagePath(pageUrl)),\n      page: posix.normalize(pageUrl),\n    }\n  }\n\n  if (page === '/_error') {\n    return {\n      filename: require.resolve('next/dist/pages/_error'),\n      bundlePath: page,\n      page: normalizePathSep(page),\n    }\n  } else {\n    throw new PageNotFoundError(normalizedPagePath)\n  }\n}\n\nexport function onDemandEntryHandler({\n  hotReloader,\n  maxInactiveAge,\n  multiCompiler,\n  nextConfig,\n  pagesBufferLength,\n  pagesDir,\n  rootDir,\n  appDir,\n}: {\n  hotReloader: HotReloaderWebpack\n  maxInactiveAge: number\n  multiCompiler: webpack.MultiCompiler\n  nextConfig: NextConfigComplete\n  pagesBufferLength: number\n  pagesDir?: string\n  rootDir: string\n  appDir?: string\n}) {\n  const hasAppDir = !!appDir\n  let curInvalidator: Invalidator = getInvalidator(\n    multiCompiler.outputPath\n  ) as any\n  const curEntries = getEntries(multiCompiler.outputPath) as any\n\n  if (!curInvalidator) {\n    curInvalidator = new Invalidator(multiCompiler)\n    invalidators.set(multiCompiler.outputPath, curInvalidator)\n  }\n\n  const startBuilding = (compilation: webpack.Compilation) => {\n    const compilationName = compilation.name as any as CompilerNameValues\n    curInvalidator.startBuilding(compilationName)\n  }\n  for (const compiler of multiCompiler.compilers) {\n    compiler.hooks.make.tap('NextJsOnDemandEntries', startBuilding)\n  }\n\n  function getPagePathsFromEntrypoints(\n    type: CompilerNameValues,\n    entrypoints: Map<string, { name?: string | null }>\n  ) {\n    const pagePaths: string[] = []\n    for (const entrypoint of entrypoints.values()) {\n      const page = getRouteFromEntrypoint(entrypoint.name!, hasAppDir)\n\n      if (page) {\n        const pageBundleType = entrypoint.name?.startsWith('app/')\n          ? PAGE_TYPES.APP\n          : PAGE_TYPES.PAGES\n        pagePaths.push(getEntryKey(type, pageBundleType, page))\n      } else if (\n        isMiddlewareFilename(entrypoint.name) ||\n        isInstrumentationHookFilename(entrypoint.name)\n      ) {\n        pagePaths.push(\n          getEntryKey(type, PAGE_TYPES.ROOT, `/${entrypoint.name}`)\n        )\n      }\n    }\n    return pagePaths\n  }\n\n  for (const compiler of multiCompiler.compilers) {\n    compiler.hooks.done.tap('NextJsOnDemandEntries', () =>\n      getInvalidator(compiler.outputPath)?.doneBuilding([\n        compiler.name as keyof typeof COMPILER_INDEXES,\n      ])\n    )\n  }\n\n  multiCompiler.hooks.done.tap('NextJsOnDemandEntries', (multiStats) => {\n    const [clientStats, serverStats, edgeServerStats] = multiStats.stats\n    const entryNames = [\n      ...getPagePathsFromEntrypoints(\n        COMPILER_NAMES.client,\n        clientStats.compilation.entrypoints\n      ),\n      ...getPagePathsFromEntrypoints(\n        COMPILER_NAMES.server,\n        serverStats.compilation.entrypoints\n      ),\n      ...(edgeServerStats\n        ? getPagePathsFromEntrypoints(\n            COMPILER_NAMES.edgeServer,\n            edgeServerStats.compilation.entrypoints\n          )\n        : []),\n    ]\n\n    for (const name of entryNames) {\n      const entry = curEntries[name]\n      if (!entry) {\n        continue\n      }\n\n      if (entry.status !== BUILDING) {\n        continue\n      }\n\n      entry.status = BUILT\n      doneCallbacks.emit(name)\n    }\n\n    getInvalidator(multiCompiler.outputPath)?.doneBuilding([...COMPILER_KEYS])\n  })\n\n  const pingIntervalTime = Math.max(1000, Math.min(5000, maxInactiveAge))\n\n  setInterval(function () {\n    disposeInactiveEntries(curEntries, maxInactiveAge)\n  }, pingIntervalTime + 1000).unref()\n\n  function handleAppDirPing(tree: FlightRouterState): void {\n    const pages = getEntrypointsFromTree(tree, true)\n\n    for (const page of pages) {\n      for (const compilerType of [\n        COMPILER_NAMES.client,\n        COMPILER_NAMES.server,\n        COMPILER_NAMES.edgeServer,\n      ]) {\n        const entryKey = getEntryKey(compilerType, PAGE_TYPES.APP, `/${page}`)\n        const entryInfo = curEntries[entryKey]\n\n        // If there's no entry, it may have been invalidated and needs to be re-built.\n        if (!entryInfo) {\n          // if (page !== lastEntry) client pings, but there's no entry for page\n          continue\n        }\n\n        // We don't need to maintain active state of anything other than BUILT entries\n        if (entryInfo.status !== BUILT) continue\n\n        // If there's an entryInfo\n        if (!lastServerAccessPagesForAppDir.includes(entryKey)) {\n          lastServerAccessPagesForAppDir.unshift(entryKey)\n\n          // Maintain the buffer max length\n          // TODO: verify that the current pageKey is not at the end of the array as multiple entrypoints can exist\n          if (lastServerAccessPagesForAppDir.length > pagesBufferLength) {\n            lastServerAccessPagesForAppDir.pop()\n          }\n        }\n        entryInfo.lastActiveTime = Date.now()\n        entryInfo.dispose = false\n      }\n    }\n  }\n\n  function handlePing(pg: string): void {\n    const page = normalizePathSep(pg)\n    for (const compilerType of [\n      COMPILER_NAMES.client,\n      COMPILER_NAMES.server,\n      COMPILER_NAMES.edgeServer,\n    ]) {\n      const entryKey = getEntryKey(compilerType, PAGE_TYPES.PAGES, page)\n      const entryInfo = curEntries[entryKey]\n\n      // If there's no entry, it may have been invalidated and needs to be re-built.\n      if (!entryInfo) {\n        // if (page !== lastEntry) client pings, but there's no entry for page\n        if (compilerType === COMPILER_NAMES.client) {\n          return\n        }\n        continue\n      }\n\n      // We don't need to maintain active state of anything other than BUILT entries\n      if (entryInfo.status !== BUILT) continue\n\n      // If there's an entryInfo\n      if (!lastClientAccessPages.includes(entryKey)) {\n        lastClientAccessPages.unshift(entryKey)\n\n        // Maintain the buffer max length\n        if (lastClientAccessPages.length > pagesBufferLength) {\n          lastClientAccessPages.pop()\n        }\n      }\n      entryInfo.lastActiveTime = Date.now()\n      entryInfo.dispose = false\n    }\n    return\n  }\n\n  async function ensurePageImpl({\n    page,\n    appPaths,\n    definition,\n    isApp,\n    url,\n  }: {\n    page: string\n    appPaths: ReadonlyArray<string> | null\n    definition: RouteDefinition | undefined\n    isApp: boolean | undefined\n    url?: string\n  }): Promise<void> {\n    const stalledTime = 60\n    const stalledEnsureTimeout = setTimeout(() => {\n      debug(\n        `Ensuring ${page} has taken longer than ${stalledTime}s, if this continues to stall this may be a bug`\n      )\n    }, stalledTime * 1000)\n\n    try {\n      let route: Pick<RouteDefinition, 'filename' | 'bundlePath' | 'page'>\n      if (definition) {\n        route = definition\n      } else {\n        route = await findPagePathData(\n          rootDir,\n          page,\n          nextConfig.pageExtensions,\n          pagesDir,\n          appDir,\n          !!nextConfig.experimental.globalNotFound\n        )\n      }\n\n      const isInsideAppDir = !!appDir && route.filename.startsWith(appDir)\n\n      if (typeof isApp === 'boolean' && isApp !== isInsideAppDir) {\n        Error.stackTraceLimit = 15\n        throw new Error(\n          `Ensure bailed, found path \"${\n            route.page\n          }\" does not match ensure type (${isApp ? 'app' : 'pages'})`\n        )\n      }\n\n      const pageBundleType = getPageBundleType(route.bundlePath)\n      const addEntry = (\n        compilerType: CompilerNameValues\n      ): {\n        entryKey: string\n        newEntry: boolean\n        shouldInvalidate: boolean\n      } => {\n        const entryKey = getEntryKey(compilerType, pageBundleType, route.page)\n        if (\n          curEntries[entryKey] &&\n          // there can be an overlap in the entryKey for the instrumentation hook file and a page named the same\n          // this is a quick fix to support this scenario by overwriting the instrumentation hook entry, since we only use it one time\n          // any changes to the instrumentation hook file will require a restart of the dev server anyway\n          !isInstrumentationHookFilename(curEntries[entryKey].bundlePath)\n        ) {\n          curEntries[entryKey].dispose = false\n          curEntries[entryKey].lastActiveTime = Date.now()\n          if (curEntries[entryKey].status === BUILT) {\n            return {\n              entryKey,\n              newEntry: false,\n              shouldInvalidate: false,\n            }\n          }\n\n          return {\n            entryKey,\n            newEntry: false,\n            shouldInvalidate: true,\n          }\n        }\n\n        curEntries[entryKey] = {\n          type: EntryTypes.ENTRY,\n          appPaths,\n          absolutePagePath: route.filename,\n          request: route.filename,\n          bundlePath: route.bundlePath,\n          dispose: false,\n          lastActiveTime: Date.now(),\n          status: ADDED,\n        }\n        return {\n          entryKey: entryKey,\n          newEntry: true,\n          shouldInvalidate: true,\n        }\n      }\n\n      const staticInfo = await getStaticInfoIncludingLayouts({\n        page,\n        pageFilePath: route.filename,\n        isInsideAppDir,\n        pageExtensions: nextConfig.pageExtensions,\n        isDev: true,\n        config: nextConfig,\n        appDir,\n      })\n\n      const added = new Map<CompilerNameValues, ReturnType<typeof addEntry>>()\n      const isServerComponent =\n        isInsideAppDir && staticInfo.rsc !== RSC_MODULE_TYPES.client\n\n      let pageRuntime = staticInfo.runtime\n\n      if (isMiddlewareFile(page) && !nextConfig.experimental.nodeMiddleware) {\n        pageRuntime = 'edge'\n      }\n\n      runDependingOnPageType({\n        page: route.page,\n        pageRuntime,\n        pageType: pageBundleType,\n        onClient: () => {\n          // Skip adding the client entry for app / Server Components.\n          if (isServerComponent || isInsideAppDir) {\n            return\n          }\n          added.set(COMPILER_NAMES.client, addEntry(COMPILER_NAMES.client))\n        },\n        onServer: () => {\n          added.set(COMPILER_NAMES.server, addEntry(COMPILER_NAMES.server))\n          const edgeServerEntry = getEntryKey(\n            COMPILER_NAMES.edgeServer,\n            pageBundleType,\n            route.page\n          )\n          if (\n            curEntries[edgeServerEntry] &&\n            !isInstrumentationHookFile(route.page)\n          ) {\n            // Runtime switched from edge to server\n            delete curEntries[edgeServerEntry]\n          }\n        },\n        onEdgeServer: () => {\n          added.set(\n            COMPILER_NAMES.edgeServer,\n            addEntry(COMPILER_NAMES.edgeServer)\n          )\n          const serverEntry = getEntryKey(\n            COMPILER_NAMES.server,\n            pageBundleType,\n            route.page\n          )\n          if (\n            curEntries[serverEntry] &&\n            !isInstrumentationHookFile(route.page)\n          ) {\n            // Runtime switched from server to edge\n            delete curEntries[serverEntry]\n          }\n        },\n      })\n\n      const addedValues = [...added.values()]\n      const entriesThatShouldBeInvalidated = [...added.entries()].filter(\n        ([, entry]) => entry.shouldInvalidate\n      )\n      const hasNewEntry = addedValues.some((entry) => entry.newEntry)\n\n      if (hasNewEntry) {\n        const routePage = isApp ? route.page : normalizeAppPath(route.page)\n        reportTrigger(routePage, url)\n      }\n\n      if (entriesThatShouldBeInvalidated.length > 0) {\n        const invalidatePromise = Promise.all(\n          entriesThatShouldBeInvalidated.map(([compilerKey, { entryKey }]) => {\n            return new Promise<void>((resolve, reject) => {\n              doneCallbacks.once(entryKey, (err: Error) => {\n                if (err) {\n                  return reject(err)\n                }\n\n                // If the invalidation also triggers a rebuild, we need to\n                // wait for that additional build to prevent race conditions.\n                const needsRebuild = curInvalidator.willRebuild(compilerKey)\n                if (needsRebuild) {\n                  doneCallbacks.once(entryKey, (rebuildErr: Error) => {\n                    if (rebuildErr) {\n                      return reject(rebuildErr)\n                    }\n                    resolve()\n                  })\n                } else {\n                  resolve()\n                }\n              })\n            })\n          })\n        )\n\n        curInvalidator.invalidate([...added.keys()])\n        await invalidatePromise\n      }\n    } finally {\n      clearTimeout(stalledEnsureTimeout)\n    }\n  }\n\n  type EnsurePageOptions = {\n    page: string\n    appPaths?: ReadonlyArray<string> | null\n    definition?: RouteDefinition\n    isApp?: boolean\n    url?: string\n  }\n\n  // Make sure that we won't have multiple invalidations ongoing concurrently.\n  const batcher = Batcher.create<EnsurePageOptions, void, string>({\n    // The cache key here is composed of the elements that affect the\n    // compilation, namely, the page, whether it's client only, and whether\n    // it's an app page. This ensures that we don't have multiple compilations\n    // for the same page happening concurrently.\n    //\n    // We don't include the whole match because it contains match specific\n    // parameters (like route params) that would just bust this cache. Any\n    // details that would possibly bust the cache should be listed here.\n    cacheKeyFn: (options) => JSON.stringify(options),\n    // Schedule the invocation of the ensurePageImpl function on the next tick.\n    schedulerFn: scheduleOnNextTick,\n  })\n\n  return {\n    async ensurePage({\n      page,\n      appPaths = null,\n      definition,\n      isApp,\n      url,\n    }: EnsurePageOptions) {\n      // If the route is actually an app page route, then we should have access\n      // to the app route definition, and therefore, the appPaths from it.\n      if (!appPaths && definition && isAppPageRouteDefinition(definition)) {\n        appPaths = definition.appPaths\n      }\n\n      // Wrap the invocation of the ensurePageImpl function in the pending\n      // wrapper, which will ensure that we don't have multiple compilations\n      // for the same page happening concurrently.\n      return batcher.batch({ page, appPaths, definition, isApp }, async () => {\n        await ensurePageImpl({\n          page,\n          appPaths,\n          definition,\n          isApp,\n          url,\n        })\n      })\n    },\n    onHMR(client: ws, getHmrServerError: () => Error | null) {\n      let bufferedHmrServerError: Error | null = null\n\n      client.addEventListener('close', () => {\n        bufferedHmrServerError = null\n      })\n      client.addEventListener('message', ({ data }) => {\n        try {\n          const error = getHmrServerError()\n\n          // New error occurred: buffered error is flushed and new error occurred\n          if (!bufferedHmrServerError && error) {\n            hotReloader.send({\n              action: HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ERROR,\n              errorJSON: stringifyError(error),\n            })\n            bufferedHmrServerError = null\n          }\n\n          const parsedData = JSON.parse(\n            typeof data !== 'string' ? data.toString() : data\n          )\n\n          if (parsedData.event === 'ping') {\n            if (parsedData.appDirRoute) {\n              handleAppDirPing(parsedData.tree)\n            } else {\n              handlePing(parsedData.page)\n            }\n          }\n        } catch {}\n      })\n    },\n  }\n}\n"], "names": ["createDebug", "EventEmitter", "findPageFile", "getStaticInfoIncludingLayouts", "runDependingOnPageType", "join", "posix", "normalizePathSep", "normalizePagePath", "ensureLeadingSlash", "removePagePathTail", "reportTrigger", "getRouteFromEntrypoint", "isInstrumentationHookFile", "isInstrumentationHookFilename", "isMiddlewareFile", "isMiddlewareFilename", "PageNotFoundError", "stringifyError", "COMPILER_INDEXES", "COMPILER_NAMES", "RSC_MODULE_TYPES", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "PAGE_SEGMENT_KEY", "HMR_ACTIONS_SENT_TO_BROWSER", "isAppPageRouteDefinition", "scheduleOnNextTick", "<PERSON><PERSON>", "normalizeAppPath", "PAGE_TYPES", "getNextFlightSegmentPath", "debug", "keys", "Object", "COMPILER_KEYS", "treePathToEntrypoint", "segmentPath", "parentPath", "parallelRouteKey", "segment", "path", "startsWith", "length", "childSegment<PERSON>ath", "convertDynamicParamTypeToSyntax", "dynamicParamTypeShort", "param", "Error", "getEntry<PERSON>ey", "compilerType", "pageBundleType", "page", "page<PERSON><PERSON>", "replace", "getPageBundleType", "pageBundlePath", "PAGES", "ROOT", "APP", "getEntrypointsFromTree", "tree", "<PERSON><PERSON><PERSON><PERSON>", "parallelRoutes", "currentSegment", "Array", "isArray", "isPageSegment", "currentPath", "slice", "reduce", "paths", "key", "childTree", "childPages", "ADDED", "Symbol", "BUILDING", "BUILT", "EntryTypes", "entriesMap", "Map", "normalizeOutputPath", "dir", "getEntries", "entries", "get", "set", "invalidators", "getInvalidator", "doneCallbacks", "lastClientAccessPages", "lastServerAccessPagesForAppDir", "Invalidator", "constructor", "multiCompiler", "building", "Set", "rebuildAgain", "shouldRebuildAll", "size", "invalidate", "compilerKeys", "has", "add", "compilers", "watching", "startBuilding", "<PERSON><PERSON><PERSON>", "doneBuilding", "rebuild", "delete", "push", "willRebuild", "disposeInactiveEntries", "maxInactiveAge", "for<PERSON>ach", "<PERSON><PERSON><PERSON>", "entryData", "lastActiveTime", "status", "dispose", "bundlePath", "type", "includes", "Date", "now", "tryToNormalizePagePath", "err", "console", "error", "findPagePathData", "rootDir", "extensions", "pagesDir", "appDir", "isGlobalNotFoundEnabled", "normalizedPagePath", "pagePath", "isInstrumentation", "pageUrl", "normalize", "filename", "globalNotFoundPath", "notFoundPath", "require", "resolve", "keepIndex", "onDemandEntryHandler", "hotReloader", "nextConfig", "pagesBufferLength", "hasAppDir", "curInvalidator", "outputPath", "curEntries", "compilation", "compilationName", "name", "compiler", "hooks", "make", "tap", "getPagePathsFromEntrypoints", "entrypoints", "pagePaths", "entrypoint", "values", "done", "multiStats", "clientStats", "serverStats", "edgeServerStats", "stats", "entryNames", "client", "server", "edgeServer", "entry", "emit", "pingIntervalTime", "Math", "max", "min", "setInterval", "unref", "handleAppDirPing", "pages", "entryInfo", "unshift", "pop", "handlePing", "pg", "ensurePageImpl", "appPaths", "definition", "isApp", "url", "stalledTime", "stalledEnsureTimeout", "setTimeout", "route", "pageExtensions", "experimental", "globalNotFound", "isInsideAppDir", "stackTraceLimit", "addEntry", "newEntry", "shouldInvalidate", "absolutePagePath", "request", "staticInfo", "pageFilePath", "isDev", "config", "added", "isServerComponent", "rsc", "pageRuntime", "runtime", "nodeMiddleware", "pageType", "onClient", "onServer", "edgeServerEntry", "onEdgeServer", "serverEntry", "addedV<PERSON>ues", "entriesThatShouldBeInvalidated", "filter", "hasNewEntry", "some", "routePage", "invalidate<PERSON><PERSON><PERSON>", "Promise", "all", "map", "reject", "once", "needsRebuild", "rebuildErr", "clearTimeout", "batcher", "create", "cacheKeyFn", "options", "JSON", "stringify", "schedulerFn", "ensurePage", "batch", "onHMR", "getHmrServerError", "bufferedHmrServerError", "addEventListener", "data", "send", "action", "SERVER_ERROR", "errorJSON", "parsedData", "parse", "toString", "event", "appDirRoute"], "mappings": "AAYA,OAAOA,iBAAiB,2BAA0B;AAClD,SAASC,YAAY,QAAQ,SAAQ;AACrC,SAASC,YAAY,QAAQ,wBAAuB;AACpD,SACEC,6BAA6B,EAC7BC,sBAAsB,QACjB,sBAAqB;AAC5B,SAASC,IAAI,EAAEC,KAAK,QAAQ,OAAM;AAClC,SAASC,gBAAgB,QAAQ,gDAA+C;AAChF,SAASC,iBAAiB,QAAQ,iDAAgD;AAClF,SAASC,kBAAkB,QAAQ,kDAAiD;AACpF,SAASC,kBAAkB,QAAQ,mDAAkD;AACrF,SAASC,aAAa,QAAQ,qBAAoB;AAClD,OAAOC,4BAA4B,+BAA8B;AACjE,SACEC,yBAAyB,EACzBC,6BAA6B,EAC7BC,gBAAgB,EAChBC,oBAAoB,QACf,oBAAmB;AAC1B,SAASC,iBAAiB,EAAEC,cAAc,QAAQ,yBAAwB;AAC1E,SACEC,gBAAgB,EAChBC,cAAc,EACdC,gBAAgB,EAChBC,gCAAgC,QAC3B,6BAA4B;AACnC,SAASC,gBAAgB,QAAQ,2BAA0B;AAC3D,SAASC,2BAA2B,QAAQ,uBAAsB;AAClE,SAASC,wBAAwB,QAAQ,iDAAgD;AACzF,SAASC,kBAAkB,QAAQ,sBAAqB;AACxD,SAASC,OAAO,QAAQ,oBAAmB;AAC3C,SAASC,gBAAgB,QAAQ,0CAAyC;AAC1E,SAASC,UAAU,QAAQ,uBAAsB;AACjD,SAASC,wBAAwB,QAAQ,mCAAkC;AAE3E,MAAMC,QAAQ/B,YAAY;AAE1B;;CAEC,GACD,MAAMgC,OAAOC,OAAOD,IAAI;AAExB,MAAME,gBAAgBF,KAAKb;AAE3B,SAASgB,qBACPC,WAA8B,EAC9BC,UAAmB;IAEnB,MAAM,CAACC,kBAAkBC,QAAQ,GAAGH;IAEpC,kEAAkE;IAClE,MAAMI,OACJ,AAACH,CAAAA,aAAaA,aAAa,MAAM,EAAC,IACjCC,CAAAA,qBAAqB,cAAc,CAACC,QAAQE,UAAU,CAAC,OACpD,CAAC,CAAC,EAAEH,iBAAiB,CAAC,CAAC,GACvB,EAAC,IACJC,CAAAA,YAAY,KAAK,SAASA,OAAM;IAEnC,eAAe;IACf,IAAIH,YAAYM,MAAM,KAAK,GAAG;QAC5B,OAAOF;IACT;IAEA,MAAMG,mBAAmBb,yBAAyBM;IAClD,OAAOD,qBAAqBQ,kBAAkBH;AAChD;AAEA,SAASI,gCACPC,qBAA6C,EAC7CC,KAAa;IAEb,OAAQD;QACN,KAAK;QACL,KAAK;YACH,OAAO,CAAC,IAAI,EAAEC,MAAM,CAAC,CAAC;QACxB,KAAK;YACH,OAAO,CAAC,KAAK,EAAEA,MAAM,EAAE,CAAC;QAC1B,KAAK;QACL,KAAK;YACH,OAAO,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC;QACrB;YACE,MAAM,qBAAuC,CAAvC,IAAIC,MAAM,+BAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAsC;IAChD;AACF;AAEA;;;;;;CAMC,GAED,OAAO,SAASC,YACdC,YAAgC,EAChCC,cAA0B,EAC1BC,IAAY;IAEZ,yCAAyC;IACzC,6FAA6F;IAC7F,MAAMC,UAAUD,KAAKE,OAAO,CAAC,uBAAuB;IACpD,OAAO,GAAGJ,aAAa,CAAC,EAAEC,eAAe,CAAC,EAAEE,SAAS;AACvD;AAEA,SAASE,kBAAkBC,cAAsB;IAC/C,kCAAkC;IAClC,IAAIA,mBAAmB,WAAW,OAAO1B,WAAW2B,KAAK;IACzD,IAAIxC,qBAAqBuC,iBAAiB,OAAO1B,WAAW4B,IAAI;IAChE,OAAOF,eAAed,UAAU,CAAC,YAC7BZ,WAAW2B,KAAK,GAChBD,eAAed,UAAU,CAAC,UACxBZ,WAAW6B,GAAG,GACd7B,WAAW4B,IAAI;AACvB;AAEA,SAASE,uBACPC,IAAuB,EACvBC,OAAgB,EAChBxB,aAAuB,EAAE;IAEzB,MAAM,CAACE,SAASuB,eAAe,GAAGF;IAElC,MAAMG,iBAAiBC,MAAMC,OAAO,CAAC1B,WACjCK,gCAAgCL,OAAO,CAAC,EAAE,EAAEA,OAAO,CAAC,EAAE,IACtDA;IAEJ,MAAM2B,gBAAgBH,eAAetB,UAAU,CAAClB;IAEhD,MAAM4C,cAAc;WAAI9B;QAAY6B,gBAAgB,KAAKH;KAAe;IAExE,IAAI,CAACF,WAAWK,eAAe;QAC7B,0CAA0C;QAC1C,OAAO;YAAC/B,qBAAqBgC,YAAYC,KAAK,CAAC;SAAI;IACrD;IAEA,OAAOnC,OAAOD,IAAI,CAAC8B,gBAAgBO,MAAM,CACvC,CAACC,OAAiBC;QAChB,MAAMC,YAAYV,cAAc,CAACS,IAAI;QACrC,MAAME,aAAad,uBAAuBa,WAAW,OAAO;eACvDL;YACHI;SACD;QACD,OAAO;eAAID;eAAUG;SAAW;IAClC,GACA,EAAE;AAEN;AAEA,OAAO,MAAMC,QAAQC,OAAO,SAAQ;AACpC,OAAO,MAAMC,WAAWD,OAAO,YAAW;AAC1C,OAAO,MAAME,QAAQF,OAAO,SAAQ;AA4BpC,sDAAsD;AACtD,qCAAqC;AACrC,OAAO,IAAA,AAAWG,oCAAAA;;;WAAAA;MAGjB;AA4BD,MAAMC,aASF,IAAIC;AAER,wDAAwD;AACxD,MAAMC,sBAAsB,CAACC,MAAgBA,IAAI7B,OAAO,CAAC,gBAAgB;AAEzE,OAAO,MAAM8B,aAAa,CACxBD;IAEAA,MAAMD,oBAAoBC;IAC1B,MAAME,UAAUL,WAAWM,GAAG,CAACH,QAAQ,CAAC;IACxCH,WAAWO,GAAG,CAACJ,KAAKE;IACpB,OAAOA;AACT,EAAC;AAED,MAAMG,eAAyC,IAAIP;AAEnD,OAAO,MAAMQ,iBAAiB,CAACN;IAC7BA,MAAMD,oBAAoBC;IAC1B,OAAOK,aAAaF,GAAG,CAACH;AAC1B,EAAC;AAED,MAAMO,gBAA8B,IAAIxF;AACxC,MAAMyF,wBAAwB;IAAC;CAAG;AAClC,MAAMC,iCAAiC;IAAC;CAAG;AAK3C,oDAAoD;AACpD,6EAA6E;AAC7E,MAAMC;IAMJC,YAAYC,aAAoC,CAAE;aAH1CC,WAA4B,IAAIC;aAChCC,eAA+B,IAAID;QAGzC,IAAI,CAACF,aAAa,GAAGA;IACvB;IAEOI,mBAAmB;QACxB,OAAO,IAAI,CAACD,YAAY,CAACE,IAAI,GAAG;IAClC;IAEAC,WAAWC,eAAqCnE,aAAa,EAAQ;QACnE,KAAK,MAAMqC,OAAO8B,aAAc;gBAY9B;YAXA,+EAA+E;YAC/E,sDAAsD;YACtD,sDAAsD;YACtD,gDAAgD;YAEhD,IAAI,IAAI,CAACN,QAAQ,CAACO,GAAG,CAAC/B,MAAM;gBAC1B,IAAI,CAAC0B,YAAY,CAACM,GAAG,CAAChC;gBACtB;YACF;YAEA,IAAI,CAACwB,QAAQ,CAACQ,GAAG,CAAChC;aAClB,8DAAA,IAAI,CAACuB,aAAa,CAACU,SAAS,CAACrF,gBAAgB,CAACoD,IAAI,CAAC,CAACkC,QAAQ,qBAA5D,4DAA8DL,UAAU;QAC1E;IACF;IAEOM,cAAcC,WAA0C,EAAE;QAC/D,IAAI,CAACZ,QAAQ,CAACQ,GAAG,CAACI;IACpB;IAEOC,aAAaP,eAAqC,EAAE,EAAE;QAC3D,MAAMQ,UAAgC,EAAE;QACxC,KAAK,MAAMtC,OAAO8B,aAAc;YAC9B,IAAI,CAACN,QAAQ,CAACe,MAAM,CAACvC;YAErB,IAAI,IAAI,CAAC0B,YAAY,CAACK,GAAG,CAAC/B,MAAM;gBAC9BsC,QAAQE,IAAI,CAACxC;gBACb,IAAI,CAAC0B,YAAY,CAACa,MAAM,CAACvC;YAC3B;QACF;QAEA,IAAIsC,QAAQnE,MAAM,GAAG,GAAG;YACtB,IAAI,CAAC0D,UAAU,CAACS;QAClB;IACF;IAEOG,YAAYL,WAA0C,EAAE;QAC7D,OAAO,IAAI,CAACV,YAAY,CAACK,GAAG,CAACK;IAC/B;AACF;AAEA,SAASM,uBACP7B,OAA4D,EAC5D8B,cAAsB;IAEtBjF,OAAOD,IAAI,CAACoD,SAAS+B,OAAO,CAAC,CAACC;QAC5B,MAAMC,YAAYjC,OAAO,CAACgC,SAAS;QACnC,MAAM,EAAEE,cAAc,EAAEC,MAAM,EAAEC,OAAO,EAAEC,UAAU,EAAE,GAAGJ;QAExD,+CAA+C;QAC/C,IAAIA,UAAUK,IAAI,QAA6B;YAC7C;QACF;QAEA,8DAA8D;QAC9D,uEAAuE;QACvE,IACE1G,qBAAqByG,eACrB3G,8BAA8B2G,aAC9B;YACA;QACF;QAEA,IAAID,SACF,6CAA6C;QAC7C;QAEF,4DAA4D;QAC5D,0CAA0C;QAC1C,IAAID,WAAW1C,OAAO;QAEtB,0EAA0E;QAC1E,kFAAkF;QAClF,+DAA+D;QAC/D,IACEa,sBAAsBiC,QAAQ,CAACP,aAC/BzB,+BAA+BgC,QAAQ,CAACP,WAExC;QAEF,IAAIE,kBAAkBM,KAAKC,GAAG,KAAKP,iBAAiBJ,gBAAgB;YAClE9B,OAAO,CAACgC,SAAS,CAACI,OAAO,GAAG;QAC9B;IACF;AACF;AAEA,0CAA0C;AAC1C,SAASM,uBAAuB3E,IAAY;IAC1C,IAAI;QACF,OAAO3C,kBAAkB2C;IAC3B,EAAE,OAAO4E,KAAK;QACZC,QAAQC,KAAK,CAACF;QACd,MAAM,IAAI9G,kBAAkBkC;IAC9B;AACF;AAQA;;;;;;;;;;CAUC,GACD,OAAO,eAAe+E,iBACpBC,OAAe,EACfhF,IAAY,EACZiF,UAAoB,EACpBC,QAA4B,EAC5BC,MAA0B,EAC1BC,uBAAgC;IAEhC,MAAMC,qBAAqBV,uBAAuB3E;IAClD,IAAIsF,WAA0B;IAE9B,MAAMC,oBAAoB7H,0BAA0B2H;IACpD,IAAIzH,iBAAiByH,uBAAuBE,mBAAmB;QAC7DD,WAAW,MAAMvI,aACfiI,SACAK,oBACAJ,YACA;QAGF,IAAI,CAACK,UAAU;YACb,MAAM,IAAIxH,kBAAkBuH;QAC9B;QAEA,MAAMG,UAAUlI,mBACdC,mBAAmBH,iBAAiBkI,WAAW;YAC7CL;QACF;QAGF,IAAIX,aAAae;QACjB,IAAIpF,UAAU9C,MAAMsI,SAAS,CAACD;QAE9B,IAAID,qBAAqB3H,iBAAiByH,qBAAqB;YAC7Df,aAAaA,WAAWpE,OAAO,CAAC,QAAQ;YACxCD,UAAUD,KAAKE,OAAO,CAAC,QAAQ;QACjC;QAEA,OAAO;YACLwF,UAAUxI,KAAK8H,SAASM;YACxBhB,YAAYA,WAAWrD,KAAK,CAAC;YAC7BjB,MAAMC;QACR;IACF;IAEA,8CAA8C;IAC9C,IAAIkF,QAAQ;QACV,IAAInF,SAAS7B,kCAAkC;YAC7C,4DAA4D;YAC5D,+EAA+E;YAC/E,IAAIiH,yBAAyB;gBAC3B,MAAMO,qBAAqB,MAAM5I,aAC/BoI,QACA,oBACAF,YACA;gBAEF,IAAIU,oBAAoB;oBACtB,OAAO;wBACLD,UAAUxI,KAAKiI,QAAQQ;wBACvBrB,YAAY,CAAC,GAAG,EAAEnG,kCAAkC;wBACpD6B,MAAM7B;oBACR;gBACF;YACF,OAAO;gBACL,mEAAmE;gBACnE,MAAMyH,eAAe,MAAM7I,aACzBoI,QACA,aACAF,YACA;gBAEF,IAAIW,cAAc;oBAChB,OAAO;wBACLF,UAAUxI,KAAKiI,QAAQS;wBACvBtB,YAAY,CAAC,GAAG,EAAEnG,kCAAkC;wBACpD6B,MAAM7B;oBACR;gBACF;YACF;YAEA,8DAA8D;YAC9D,OAAO;gBACLuH,UAAUG,QAAQC,OAAO,CACvB;gBAEFxB,YAAY,CAAC,GAAG,EAAEnG,kCAAkC;gBACpD6B,MAAM7B;YACR;QACF;QACAmH,WAAW,MAAMvI,aAAaoI,QAAQE,oBAAoBJ,YAAY;QACtE,IAAIK,UAAU;YACZ,MAAME,UAAUlI,mBACdC,mBAAmBH,iBAAiBkI,WAAW;gBAC7CS,WAAW;gBACXd;YACF;YAGF,OAAO;gBACLS,UAAUxI,KAAKiI,QAAQG;gBACvBhB,YAAYnH,MAAMD,IAAI,CAAC,OAAOsI;gBAC9BxF,MAAM7C,MAAMsI,SAAS,CAACD;YACxB;QACF;IACF;IAEA,IAAI,CAACF,YAAYJ,UAAU;QACzBI,WAAW,MAAMvI,aACfmI,UACAG,oBACAJ,YACA;IAEJ;IAEA,IAAIK,aAAa,QAAQJ,UAAU;QACjC,MAAMM,UAAUlI,mBACdC,mBAAmBH,iBAAiBkI,WAAW;YAC7CL;QACF;QAGF,OAAO;YACLS,UAAUxI,KAAKgI,UAAUI;YACzBhB,YAAYnH,MAAMD,IAAI,CAAC,SAASG,kBAAkBmI;YAClDxF,MAAM7C,MAAMsI,SAAS,CAACD;QACxB;IACF;IAEA,IAAIxF,SAAS,WAAW;QACtB,OAAO;YACL0F,UAAUG,QAAQC,OAAO,CAAC;YAC1BxB,YAAYtE;YACZA,MAAM5C,iBAAiB4C;QACzB;IACF,OAAO;QACL,MAAM,IAAIlC,kBAAkBuH;IAC9B;AACF;AAEA,OAAO,SAASW,qBAAqB,EACnCC,WAAW,EACXlC,cAAc,EACdpB,aAAa,EACbuD,UAAU,EACVC,iBAAiB,EACjBjB,QAAQ,EACRF,OAAO,EACPG,MAAM,EAUP;IACC,MAAMiB,YAAY,CAAC,CAACjB;IACpB,IAAIkB,iBAA8BhE,eAChCM,cAAc2D,UAAU;IAE1B,MAAMC,aAAavE,WAAWW,cAAc2D,UAAU;IAEtD,IAAI,CAACD,gBAAgB;QACnBA,iBAAiB,IAAI5D,YAAYE;QACjCP,aAAaD,GAAG,CAACQ,cAAc2D,UAAU,EAAED;IAC7C;IAEA,MAAM9C,gBAAgB,CAACiD;QACrB,MAAMC,kBAAkBD,YAAYE,IAAI;QACxCL,eAAe9C,aAAa,CAACkD;IAC/B;IACA,KAAK,MAAME,YAAYhE,cAAcU,SAAS,CAAE;QAC9CsD,SAASC,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,yBAAyBvD;IACnD;IAEA,SAASwD,4BACPxC,IAAwB,EACxByC,WAAkD;QAElD,MAAMC,YAAsB,EAAE;QAC9B,KAAK,MAAMC,cAAcF,YAAYG,MAAM,GAAI;YAC7C,MAAMnH,OAAOvC,uBAAuByJ,WAAWR,IAAI,EAAGN;YAEtD,IAAIpG,MAAM;oBACekH;gBAAvB,MAAMnH,iBAAiBmH,EAAAA,mBAAAA,WAAWR,IAAI,qBAAfQ,iBAAiB5H,UAAU,CAAC,WAC/CZ,WAAW6B,GAAG,GACd7B,WAAW2B,KAAK;gBACpB4G,UAAUrD,IAAI,CAAC/D,YAAY0E,MAAMxE,gBAAgBC;YACnD,OAAO,IACLnC,qBAAqBqJ,WAAWR,IAAI,KACpC/I,8BAA8BuJ,WAAWR,IAAI,GAC7C;gBACAO,UAAUrD,IAAI,CACZ/D,YAAY0E,MAAM7F,WAAW4B,IAAI,EAAE,CAAC,CAAC,EAAE4G,WAAWR,IAAI,EAAE;YAE5D;QACF;QACA,OAAOO;IACT;IAEA,KAAK,MAAMN,YAAYhE,cAAcU,SAAS,CAAE;QAC9CsD,SAASC,KAAK,CAACQ,IAAI,CAACN,GAAG,CAAC,yBAAyB;gBAC/CzE;oBAAAA,kBAAAA,eAAesE,SAASL,UAAU,sBAAlCjE,gBAAqCoB,YAAY,CAAC;gBAChDkD,SAASD,IAAI;aACd;;IAEL;IAEA/D,cAAciE,KAAK,CAACQ,IAAI,CAACN,GAAG,CAAC,yBAAyB,CAACO;YAiCrDhF;QAhCA,MAAM,CAACiF,aAAaC,aAAaC,gBAAgB,GAAGH,WAAWI,KAAK;QACpE,MAAMC,aAAa;eACdX,4BACD9I,eAAe0J,MAAM,EACrBL,YAAYd,WAAW,CAACQ,WAAW;eAElCD,4BACD9I,eAAe2J,MAAM,EACrBL,YAAYf,WAAW,CAACQ,WAAW;eAEjCQ,kBACAT,4BACE9I,eAAe4J,UAAU,EACzBL,gBAAgBhB,WAAW,CAACQ,WAAW,IAEzC,EAAE;SACP;QAED,KAAK,MAAMN,QAAQgB,WAAY;YAC7B,MAAMI,QAAQvB,UAAU,CAACG,KAAK;YAC9B,IAAI,CAACoB,OAAO;gBACV;YACF;YAEA,IAAIA,MAAM1D,MAAM,KAAK3C,UAAU;gBAC7B;YACF;YAEAqG,MAAM1D,MAAM,GAAG1C;YACfY,cAAcyF,IAAI,CAACrB;QACrB;SAEArE,kBAAAA,eAAeM,cAAc2D,UAAU,sBAAvCjE,gBAA0CoB,YAAY,CAAC;eAAI1E;SAAc;IAC3E;IAEA,MAAMiJ,mBAAmBC,KAAKC,GAAG,CAAC,MAAMD,KAAKE,GAAG,CAAC,MAAMpE;IAEvDqE,YAAY;QACVtE,uBAAuByC,YAAYxC;IACrC,GAAGiE,mBAAmB,MAAMK,KAAK;IAEjC,SAASC,iBAAiB7H,IAAuB;QAC/C,MAAM8H,QAAQ/H,uBAAuBC,MAAM;QAE3C,KAAK,MAAMT,QAAQuI,MAAO;YACxB,KAAK,MAAMzI,gBAAgB;gBACzB7B,eAAe0J,MAAM;gBACrB1J,eAAe2J,MAAM;gBACrB3J,eAAe4J,UAAU;aAC1B,CAAE;gBACD,MAAM5D,WAAWpE,YAAYC,cAAcpB,WAAW6B,GAAG,EAAE,CAAC,CAAC,EAAEP,MAAM;gBACrE,MAAMwI,YAAYjC,UAAU,CAACtC,SAAS;gBAEtC,8EAA8E;gBAC9E,IAAI,CAACuE,WAAW;oBAEd;gBACF;gBAEA,8EAA8E;gBAC9E,IAAIA,UAAUpE,MAAM,KAAK1C,OAAO;gBAEhC,0BAA0B;gBAC1B,IAAI,CAACc,+BAA+BgC,QAAQ,CAACP,WAAW;oBACtDzB,+BAA+BiG,OAAO,CAACxE;oBAEvC,iCAAiC;oBACjC,yGAAyG;oBACzG,IAAIzB,+BAA+BjD,MAAM,GAAG4G,mBAAmB;wBAC7D3D,+BAA+BkG,GAAG;oBACpC;gBACF;gBACAF,UAAUrE,cAAc,GAAGM,KAAKC,GAAG;gBACnC8D,UAAUnE,OAAO,GAAG;YACtB;QACF;IACF;IAEA,SAASsE,WAAWC,EAAU;QAC5B,MAAM5I,OAAO5C,iBAAiBwL;QAC9B,KAAK,MAAM9I,gBAAgB;YACzB7B,eAAe0J,MAAM;YACrB1J,eAAe2J,MAAM;YACrB3J,eAAe4J,UAAU;SAC1B,CAAE;YACD,MAAM5D,WAAWpE,YAAYC,cAAcpB,WAAW2B,KAAK,EAAEL;YAC7D,MAAMwI,YAAYjC,UAAU,CAACtC,SAAS;YAEtC,8EAA8E;YAC9E,IAAI,CAACuE,WAAW;gBACd,sEAAsE;gBACtE,IAAI1I,iBAAiB7B,eAAe0J,MAAM,EAAE;oBAC1C;gBACF;gBACA;YACF;YAEA,8EAA8E;YAC9E,IAAIa,UAAUpE,MAAM,KAAK1C,OAAO;YAEhC,0BAA0B;YAC1B,IAAI,CAACa,sBAAsBiC,QAAQ,CAACP,WAAW;gBAC7C1B,sBAAsBkG,OAAO,CAACxE;gBAE9B,iCAAiC;gBACjC,IAAI1B,sBAAsBhD,MAAM,GAAG4G,mBAAmB;oBACpD5D,sBAAsBmG,GAAG;gBAC3B;YACF;YACAF,UAAUrE,cAAc,GAAGM,KAAKC,GAAG;YACnC8D,UAAUnE,OAAO,GAAG;QACtB;QACA;IACF;IAEA,eAAewE,eAAe,EAC5B7I,IAAI,EACJ8I,QAAQ,EACRC,UAAU,EACVC,KAAK,EACLC,GAAG,EAOJ;QACC,MAAMC,cAAc;QACpB,MAAMC,uBAAuBC,WAAW;YACtCxK,MACE,CAAC,SAAS,EAAEoB,KAAK,uBAAuB,EAAEkJ,YAAY,+CAA+C,CAAC;QAE1G,GAAGA,cAAc;QAEjB,IAAI;YACF,IAAIG;YACJ,IAAIN,YAAY;gBACdM,QAAQN;YACV,OAAO;gBACLM,QAAQ,MAAMtE,iBACZC,SACAhF,MACAkG,WAAWoD,cAAc,EACzBpE,UACAC,QACA,CAAC,CAACe,WAAWqD,YAAY,CAACC,cAAc;YAE5C;YAEA,MAAMC,iBAAiB,CAAC,CAACtE,UAAUkE,MAAM3D,QAAQ,CAACpG,UAAU,CAAC6F;YAE7D,IAAI,OAAO6D,UAAU,aAAaA,UAAUS,gBAAgB;gBAC1D7J,MAAM8J,eAAe,GAAG;gBACxB,MAAM,qBAIL,CAJK,IAAI9J,MACR,CAAC,2BAA2B,EAC1ByJ,MAAMrJ,IAAI,CACX,8BAA8B,EAAEgJ,QAAQ,QAAQ,QAAQ,CAAC,CAAC,GAHvD,qBAAA;2BAAA;gCAAA;kCAAA;gBAIN;YACF;YAEA,MAAMjJ,iBAAiBI,kBAAkBkJ,MAAM/E,UAAU;YACzD,MAAMqF,WAAW,CACf7J;gBAMA,MAAMmE,WAAWpE,YAAYC,cAAcC,gBAAgBsJ,MAAMrJ,IAAI;gBACrE,IACEuG,UAAU,CAACtC,SAAS,IACpB,sGAAsG;gBACtG,4HAA4H;gBAC5H,+FAA+F;gBAC/F,CAACtG,8BAA8B4I,UAAU,CAACtC,SAAS,CAACK,UAAU,GAC9D;oBACAiC,UAAU,CAACtC,SAAS,CAACI,OAAO,GAAG;oBAC/BkC,UAAU,CAACtC,SAAS,CAACE,cAAc,GAAGM,KAAKC,GAAG;oBAC9C,IAAI6B,UAAU,CAACtC,SAAS,CAACG,MAAM,KAAK1C,OAAO;wBACzC,OAAO;4BACLuC;4BACA2F,UAAU;4BACVC,kBAAkB;wBACpB;oBACF;oBAEA,OAAO;wBACL5F;wBACA2F,UAAU;wBACVC,kBAAkB;oBACpB;gBACF;gBAEAtD,UAAU,CAACtC,SAAS,GAAG;oBACrBM,IAAI;oBACJuE;oBACAgB,kBAAkBT,MAAM3D,QAAQ;oBAChCqE,SAASV,MAAM3D,QAAQ;oBACvBpB,YAAY+E,MAAM/E,UAAU;oBAC5BD,SAAS;oBACTF,gBAAgBM,KAAKC,GAAG;oBACxBN,QAAQ7C;gBACV;gBACA,OAAO;oBACL0C,UAAUA;oBACV2F,UAAU;oBACVC,kBAAkB;gBACpB;YACF;YAEA,MAAMG,aAAa,MAAMhN,8BAA8B;gBACrDgD;gBACAiK,cAAcZ,MAAM3D,QAAQ;gBAC5B+D;gBACAH,gBAAgBpD,WAAWoD,cAAc;gBACzCY,OAAO;gBACPC,QAAQjE;gBACRf;YACF;YAEA,MAAMiF,QAAQ,IAAIvI;YAClB,MAAMwI,oBACJZ,kBAAkBO,WAAWM,GAAG,KAAKpM,iBAAiByJ,MAAM;YAE9D,IAAI4C,cAAcP,WAAWQ,OAAO;YAEpC,IAAI5M,iBAAiBoC,SAAS,CAACkG,WAAWqD,YAAY,CAACkB,cAAc,EAAE;gBACrEF,cAAc;YAChB;YAEAtN,uBAAuB;gBACrB+C,MAAMqJ,MAAMrJ,IAAI;gBAChBuK;gBACAG,UAAU3K;gBACV4K,UAAU;oBACR,4DAA4D;oBAC5D,IAAIN,qBAAqBZ,gBAAgB;wBACvC;oBACF;oBACAW,MAAMjI,GAAG,CAAClE,eAAe0J,MAAM,EAAEgC,SAAS1L,eAAe0J,MAAM;gBACjE;gBACAiD,UAAU;oBACRR,MAAMjI,GAAG,CAAClE,eAAe2J,MAAM,EAAE+B,SAAS1L,eAAe2J,MAAM;oBAC/D,MAAMiD,kBAAkBhL,YACtB5B,eAAe4J,UAAU,EACzB9H,gBACAsJ,MAAMrJ,IAAI;oBAEZ,IACEuG,UAAU,CAACsE,gBAAgB,IAC3B,CAACnN,0BAA0B2L,MAAMrJ,IAAI,GACrC;wBACA,uCAAuC;wBACvC,OAAOuG,UAAU,CAACsE,gBAAgB;oBACpC;gBACF;gBACAC,cAAc;oBACZV,MAAMjI,GAAG,CACPlE,eAAe4J,UAAU,EACzB8B,SAAS1L,eAAe4J,UAAU;oBAEpC,MAAMkD,cAAclL,YAClB5B,eAAe2J,MAAM,EACrB7H,gBACAsJ,MAAMrJ,IAAI;oBAEZ,IACEuG,UAAU,CAACwE,YAAY,IACvB,CAACrN,0BAA0B2L,MAAMrJ,IAAI,GACrC;wBACA,uCAAuC;wBACvC,OAAOuG,UAAU,CAACwE,YAAY;oBAChC;gBACF;YACF;YAEA,MAAMC,cAAc;mBAAIZ,MAAMjD,MAAM;aAAG;YACvC,MAAM8D,iCAAiC;mBAAIb,MAAMnI,OAAO;aAAG,CAACiJ,MAAM,CAChE,CAAC,GAAGpD,MAAM,GAAKA,MAAM+B,gBAAgB;YAEvC,MAAMsB,cAAcH,YAAYI,IAAI,CAAC,CAACtD,QAAUA,MAAM8B,QAAQ;YAE9D,IAAIuB,aAAa;gBACf,MAAME,YAAYrC,QAAQK,MAAMrJ,IAAI,GAAGvB,iBAAiB4K,MAAMrJ,IAAI;gBAClExC,cAAc6N,WAAWpC;YAC3B;YAEA,IAAIgC,+BAA+B1L,MAAM,GAAG,GAAG;gBAC7C,MAAM+L,oBAAoBC,QAAQC,GAAG,CACnCP,+BAA+BQ,GAAG,CAAC,CAAC,CAACjI,aAAa,EAAES,QAAQ,EAAE,CAAC;oBAC7D,OAAO,IAAIsH,QAAc,CAACzF,SAAS4F;wBACjCpJ,cAAcqJ,IAAI,CAAC1H,UAAU,CAACW;4BAC5B,IAAIA,KAAK;gCACP,OAAO8G,OAAO9G;4BAChB;4BAEA,0DAA0D;4BAC1D,6DAA6D;4BAC7D,MAAMgH,eAAevF,eAAexC,WAAW,CAACL;4BAChD,IAAIoI,cAAc;gCAChBtJ,cAAcqJ,IAAI,CAAC1H,UAAU,CAAC4H;oCAC5B,IAAIA,YAAY;wCACd,OAAOH,OAAOG;oCAChB;oCACA/F;gCACF;4BACF,OAAO;gCACLA;4BACF;wBACF;oBACF;gBACF;gBAGFO,eAAepD,UAAU,CAAC;uBAAImH,MAAMvL,IAAI;iBAAG;gBAC3C,MAAMyM;YACR;QACF,SAAU;YACRQ,aAAa3C;QACf;IACF;IAUA,4EAA4E;IAC5E,MAAM4C,UAAUvN,QAAQwN,MAAM,CAAkC;QAC9D,iEAAiE;QACjE,uEAAuE;QACvE,0EAA0E;QAC1E,4CAA4C;QAC5C,EAAE;QACF,sEAAsE;QACtE,sEAAsE;QACtE,oEAAoE;QACpEC,YAAY,CAACC,UAAYC,KAAKC,SAAS,CAACF;QACxC,2EAA2E;QAC3EG,aAAa9N;IACf;IAEA,OAAO;QACL,MAAM+N,YAAW,EACftM,IAAI,EACJ8I,WAAW,IAAI,EACfC,UAAU,EACVC,KAAK,EACLC,GAAG,EACe;YAClB,yEAAyE;YACzE,oEAAoE;YACpE,IAAI,CAACH,YAAYC,cAAczK,yBAAyByK,aAAa;gBACnED,WAAWC,WAAWD,QAAQ;YAChC;YAEA,oEAAoE;YACpE,sEAAsE;YACtE,4CAA4C;YAC5C,OAAOiD,QAAQQ,KAAK,CAAC;gBAAEvM;gBAAM8I;gBAAUC;gBAAYC;YAAM,GAAG;gBAC1D,MAAMH,eAAe;oBACnB7I;oBACA8I;oBACAC;oBACAC;oBACAC;gBACF;YACF;QACF;QACAuD,OAAM7E,MAAU,EAAE8E,iBAAqC;YACrD,IAAIC,yBAAuC;YAE3C/E,OAAOgF,gBAAgB,CAAC,SAAS;gBAC/BD,yBAAyB;YAC3B;YACA/E,OAAOgF,gBAAgB,CAAC,WAAW,CAAC,EAAEC,IAAI,EAAE;gBAC1C,IAAI;oBACF,MAAM9H,QAAQ2H;oBAEd,uEAAuE;oBACvE,IAAI,CAACC,0BAA0B5H,OAAO;wBACpCmB,YAAY4G,IAAI,CAAC;4BACfC,QAAQzO,4BAA4B0O,YAAY;4BAChDC,WAAWjP,eAAe+G;wBAC5B;wBACA4H,yBAAyB;oBAC3B;oBAEA,MAAMO,aAAad,KAAKe,KAAK,CAC3B,OAAON,SAAS,WAAWA,KAAKO,QAAQ,KAAKP;oBAG/C,IAAIK,WAAWG,KAAK,KAAK,QAAQ;wBAC/B,IAAIH,WAAWI,WAAW,EAAE;4BAC1B/E,iBAAiB2E,WAAWxM,IAAI;wBAClC,OAAO;4BACLkI,WAAWsE,WAAWjN,IAAI;wBAC5B;oBACF;gBACF,EAAE,OAAM,CAAC;YACX;QACF;IACF;AACF", "ignoreList": [0]}