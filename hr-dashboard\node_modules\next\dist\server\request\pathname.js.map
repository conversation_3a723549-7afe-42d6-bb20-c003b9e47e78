{"version": 3, "sources": ["../../../src/server/request/pathname.ts"], "sourcesContent": ["import type { WorkStore } from '../app-render/work-async-storage.external'\n\nimport {\n  postponeWithTracking,\n  type DynamicTrackingState,\n} from '../app-render/dynamic-rendering'\n\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStore,\n} from '../app-render/work-unit-async-storage.external'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nexport function createServerPathnameForMetadata(\n  underlyingPathname: string,\n  workStore: WorkStore\n): Promise<string> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy': {\n        return createPrerenderPathname(\n          underlyingPathname,\n          workStore,\n          workUnitStore\n        )\n      }\n      default:\n      // fallthrough\n    }\n  }\n  return createRenderPathname(underlyingPathname)\n}\n\nfunction createPrerenderPathname(\n  underlyingPathname: string,\n  workStore: WorkStore,\n  prerenderStore: PrerenderStore\n): Promise<string> {\n  const fallbackParams = workStore.fallbackRouteParams\n  if (fallbackParams && fallbackParams.size > 0) {\n    switch (prerenderStore.type) {\n      case 'prerender':\n        return makeHangingPromise<string>(\n          prerenderStore.renderSignal,\n          '`pathname`'\n        )\n      case 'prerender-client':\n        throw new InvariantError(\n          'createPrerenderPathname was called inside a client component scope.'\n        )\n      case 'prerender-ppr':\n        return makeErroringPathname(workStore, prerenderStore.dynamicTracking)\n        break\n      default:\n        return makeErroringPathname(workStore, null)\n    }\n  }\n\n  // We don't have any fallback params so we have an entirely static safe params object\n  return Promise.resolve(underlyingPathname)\n}\n\nfunction makeErroringPathname<T>(\n  workStore: WorkStore,\n  dynamicTracking: null | DynamicTrackingState\n): Promise<T> {\n  let reject: null | ((reason: unknown) => void) = null\n  const promise = new Promise<T>((_, re) => {\n    reject = re\n  })\n\n  const originalThen = promise.then.bind(promise)\n\n  // We instrument .then so that we can generate a tracking event only if you actually\n  // await this promise, not just that it is created.\n  promise.then = (onfulfilled, onrejected) => {\n    if (reject) {\n      try {\n        postponeWithTracking(\n          workStore.route,\n          'metadata relative url resolving',\n          dynamicTracking\n        )\n      } catch (error) {\n        reject(error)\n        reject = null\n      }\n    }\n    return originalThen(onfulfilled, onrejected)\n  }\n\n  // We wrap in a noop proxy to trick the runtime into thinking it\n  // isn't a native promise (it's not really). This is so that awaiting\n  // the promise will call the `then` property triggering the lazy postpone\n  return new Proxy(promise, {})\n}\n\nfunction createRenderPathname(underlyingPathname: string): Promise<string> {\n  return Promise.resolve(underlyingPathname)\n}\n"], "names": ["createServerPathnameForMetadata", "underlyingPathname", "workStore", "workUnitStore", "workUnitAsyncStorage", "getStore", "type", "createPrerenderPathname", "createRenderPathname", "prerenderStore", "fallbackP<PERSON><PERSON>", "fallbackRouteParams", "size", "makeHangingPromise", "renderSignal", "InvariantError", "makeErroringPathname", "dynamicTracking", "Promise", "resolve", "reject", "promise", "_", "re", "originalThen", "then", "bind", "onfulfilled", "onrejected", "postponeWithTracking", "route", "error", "Proxy"], "mappings": ";;;;+BAcg<PERSON>;;;eAAAA;;;kCATT;8CAKA;uCAC4B;gCACJ;AAExB,SAASA,gCACdC,kBAA0B,EAC1BC,SAAoB;IAEpB,MAAMC,gBAAgBC,kDAAoB,CAACC,QAAQ;IACnD,IAAIF,eAAe;QACjB,OAAQA,cAAcG,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBAAoB;oBACvB,OAAOC,wBACLN,oBACAC,WACAC;gBAEJ;YACA;QAEF;IACF;IACA,OAAOK,qBAAqBP;AAC9B;AAEA,SAASM,wBACPN,kBAA0B,EAC1BC,SAAoB,EACpBO,cAA8B;IAE9B,MAAMC,iBAAiBR,UAAUS,mBAAmB;IACpD,IAAID,kBAAkBA,eAAeE,IAAI,GAAG,GAAG;QAC7C,OAAQH,eAAeH,IAAI;YACzB,KAAK;gBACH,OAAOO,IAAAA,yCAAkB,EACvBJ,eAAeK,YAAY,EAC3B;YAEJ,KAAK;gBACH,MAAM,qBAEL,CAFK,IAAIC,8BAAc,CACtB,wEADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,OAAOC,qBAAqBd,WAAWO,eAAeQ,eAAe;gBACrE;YACF;gBACE,OAAOD,qBAAqBd,WAAW;QAC3C;IACF;IAEA,qFAAqF;IACrF,OAAOgB,QAAQC,OAAO,CAAClB;AACzB;AAEA,SAASe,qBACPd,SAAoB,EACpBe,eAA4C;IAE5C,IAAIG,SAA6C;IACjD,MAAMC,UAAU,IAAIH,QAAW,CAACI,GAAGC;QACjCH,SAASG;IACX;IAEA,MAAMC,eAAeH,QAAQI,IAAI,CAACC,IAAI,CAACL;IAEvC,oFAAoF;IACpF,mDAAmD;IACnDA,QAAQI,IAAI,GAAG,CAACE,aAAaC;QAC3B,IAAIR,QAAQ;YACV,IAAI;gBACFS,IAAAA,sCAAoB,EAClB3B,UAAU4B,KAAK,EACf,mCACAb;YAEJ,EAAE,OAAOc,OAAO;gBACdX,OAAOW;gBACPX,SAAS;YACX;QACF;QACA,OAAOI,aAAaG,aAAaC;IACnC;IAEA,gEAAgE;IAChE,qEAAqE;IACrE,yEAAyE;IACzE,OAAO,IAAII,MAAMX,SAAS,CAAC;AAC7B;AAEA,SAASb,qBAAqBP,kBAA0B;IACtD,OAAOiB,QAAQC,OAAO,CAAClB;AACzB", "ignoreList": [0]}